/**
 * @typedef {object} Phaser.Types.Loader.FileTypes.TilemapJSONFileConfig
 *
 * @property {string} key - The key of the file. Must be unique within both the Loader and the Tilemap Cache.
 * @property {object|string} [url] - The absolute or relative URL to load the file from. Or, a well formed JSON object.
 * @property {string} [extension='json'] - The default file extension to use if no url is provided.
 * @property {Phaser.Types.Loader.XHRSettingsObject} [xhrSettings] - Extra XHR Settings specifically for this file.
 */
