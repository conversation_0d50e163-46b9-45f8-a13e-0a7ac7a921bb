/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2025 Phaser Studio Inc.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

/**
 * Tatar SNES USB Controller Gamepad Configuration.
 * USB Gamepad  (STANDARD GAMEPAD Vendor: 0079 Product: 0011)
 *
 * @name Phaser.Input.Gamepad.Configs.SNES_USB
 * @namespace
 * @since 3.0.0
 */
module.exports = {

    /**
     * D-Pad up
     *
     * @name Phaser.Input.Gamepad.Configs.SNES_USB.UP
     * @const
     * @type {number}
     * @since 3.0.0
     */
    UP: 12,

    /**
     * D-Pad down
     *
     * @name Phaser.Input.Gamepad.Configs.SNES_USB.DOWN
     * @const
     * @type {number}
     * @since 3.0.0
     */
    DOWN: 13,

    /**
     * D-Pad left
     *
     * @name Phaser.Input.Gamepad.Configs.SNES_USB.LEFT
     * @const
     * @type {number}
     * @since 3.0.0
     */
    LEFT: 14,

    /**
     * D-Pad right
     *
     * @name Phaser.Input.Gamepad.Configs.SNES_USB.RIGHT
     * @const
     * @type {number}
     * @since 3.0.0
     */
    RIGHT: 15,

    /**
     * Select button
     *
     * @name Phaser.Input.Gamepad.Configs.SNES_USB.SELECT
     * @const
     * @type {number}
     * @since 3.0.0
     */
    SELECT: 8,

    /**
     * Start button
     *
     * @name Phaser.Input.Gamepad.Configs.SNES_USB.START
     * @const
     * @type {number}
     * @since 3.0.0
     */
    START: 9,

    /**
     * B Button (Bottom)
     *
     * @name Phaser.Input.Gamepad.Configs.SNES_USB.B
     * @const
     * @type {number}
     * @since 3.0.0
     */
    B: 0,

    /**
     * A Button (Right)
     *
     * @name Phaser.Input.Gamepad.Configs.SNES_USB.A
     * @const
     * @type {number}
     * @since 3.0.0
     */
    A: 1,

    /**
     * Y Button (Left)
     *
     * @name Phaser.Input.Gamepad.Configs.SNES_USB.Y
     * @const
     * @type {number}
     * @since 3.0.0
     */
    Y: 2,

    /**
     * X Button (Top)
     *
     * @name Phaser.Input.Gamepad.Configs.SNES_USB.X
     * @const
     * @type {number}
     * @since 3.0.0
     */
    X: 3,

    /**
     * Left bumper
     *
     * @name Phaser.Input.Gamepad.Configs.SNES_USB.LEFT_SHOULDER
     * @const
     * @type {number}
     * @since 3.0.0
     */
    LEFT_SHOULDER: 4,

    /**
     * Right bumper
     *
     * @name Phaser.Input.Gamepad.Configs.SNES_USB.RIGHT_SHOULDER
     * @const
     * @type {number}
     * @since 3.0.0
     */
    RIGHT_SHOULDER: 5

};
