/**
 * @typedef {object} Phaser.Types.Loader.FileTypes.SVGSizeConfig
 *
 * @property {number} [width] - An optional width. The SVG will be resized to this size before being rendered to a texture.
 * @property {number} [height] - An optional height. The SVG will be resized to this size before being rendered to a texture.
 * @property {number} [scale] - An optional scale. If given it overrides the width / height properties. The SVG is scaled by the scale factor before being rendered to a texture.
 */
