/**
 * @typedef {object} Phaser.Types.GameObjects.Vertex
 * @since 3.50.0
 *
 * @property {number} x - The x coordinate of the vertex.
 * @property {number} y - The y coordinate of the vertex.
 * @property {number} z - The z coordinate of the vertex.
 * @property {number} normalX - The x normal of the vertex.
 * @property {number} normalY - The y normal of the vertex.
 * @property {number} normalZ - The z normal of the vertex.
 * @property {number} u - UV u texture coordinate of the vertex.
 * @property {number} v - UV v texture coordinate of the vertex.
 * @property {number} alpha - The alpha value of the vertex.
 */
