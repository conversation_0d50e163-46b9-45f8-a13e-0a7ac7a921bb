/**
 * @typedef {object} Phaser.Types.Renderer.WebGL.WebGLPipelineUniformsConfig
 * @since 3.55.1
 *
 * @property {string} name - The name of the uniform as defined in the shader.
 * @property {number} location - The location of the uniform.
 * @property {?function} setter - The setter function called on the WebGL context to set the uniform value.
 * @property {number} [value1] - The first cached value of the uniform.
 * @property {number} [value2] - The first cached value of the uniform.
 * @property {number} [value3] - The first cached value of the uniform.
 * @property {number} [value4] - The first cached value of the uniform.
 */
