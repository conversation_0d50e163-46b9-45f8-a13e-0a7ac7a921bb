/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2025 Phaser Studio Inc.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

/**
 * The Input Plugin Start Event.
 *
 * This internal event is dispatched by the Input Plugin when it has finished setting-up,
 * signalling to all of its internal systems to start.
 *
 * @event Phaser.Input.Events#START
 * @type {string}
 * @since 3.0.0
 */
module.exports = 'start';
