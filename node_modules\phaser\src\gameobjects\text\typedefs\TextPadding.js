/**
 * A Text Padding configuration object as used by the Text Style.
 *
 * @typedef {object} Phaser.Types.GameObjects.Text.TextPadding
 * @since 3.18.0
 *
 * @property {number} [x] - If set this value is used for both the left and right padding.
 * @property {number} [y] - If set this value is used for both the top and bottom padding.
 * @property {number} [left] - The amount of padding added to the left of the Text object.
 * @property {number} [right] - The amount of padding added to the right of the Text object.
 * @property {number} [top] - The amount of padding added to the top of the Text object.
 * @property {number} [bottom] - The amount of padding added to the bottom of the Text object.
 */
