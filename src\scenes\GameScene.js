import Phaser from 'phaser';
import { GameConfig } from '../config/GameConfig.js';

export class GameScene extends Phaser.Scene {
    constructor() {
        super({ key: 'GameScene' });
        this.players = [];
        this.enemies = [];
        this.bullets = [];
        this.gameMode = 'single';
    }

    init(data) {
        this.gameMode = data.mode || 'single';
        console.log('游戏模式:', this.gameMode);
    }

    create() {
        // 创建游戏世界
        this.createWorld();
        
        // 创建玩家
        this.createPlayer();
        
        // 创建敌人生成系统
        this.createEnemySpawner();
        
        // 设置输入控制
        this.setupControls();
        
        // 启动UI场景
        this.scene.launch('UIScene');
        
        // 设置摄像机
        this.setupCamera();
        
        console.log('游戏场景已创建');
    }

    createWorld() {
        const width = GameConfig.GAME_WIDTH;
        const height = GameConfig.GAME_HEIGHT;
        
        // 背景
        this.add.image(width / 2, height / 2, 'background');
        
        // 创建地面
        this.ground = this.physics.add.staticGroup();
        for (let x = 0; x < width; x += 32) {
            this.ground.create(x + 16, height - 16, 'ground');
        }
        
        // 创建物理组
        this.playerGroup = this.physics.add.group();
        this.enemyGroup = this.physics.add.group();
        this.bulletGroup = this.physics.add.group();
        
        // 设置碰撞检测
        this.physics.add.collider(this.playerGroup, this.ground);
        this.physics.add.collider(this.enemyGroup, this.ground);
        this.physics.add.overlap(this.bulletGroup, this.enemyGroup, this.bulletHitEnemy, null, this);
        this.physics.add.overlap(this.playerGroup, this.enemyGroup, this.playerHitEnemy, null, this);
    }

    createPlayer() {
        const startX = 100;
        const startY = GameConfig.GAME_HEIGHT - 100;
        
        // 创建玩家精灵
        this.player = this.physics.add.sprite(startX, startY, 'player');
        this.player.setCollideWorldBounds(true);
        this.player.setBounce(0.2);
        
        // 玩家属性
        this.player.health = GameConfig.PLAYER.HEALTH;
        this.player.maxHealth = GameConfig.PLAYER.HEALTH;
        this.player.isAlive = true;
        this.player.lastShot = 0;
        this.player.currentWeapon = 'ASSAULT_RIFLE';
        
        // 添加到玩家组
        this.playerGroup.add(this.player);
        
        console.log('玩家已创建');
    }

    createEnemySpawner() {
        // 定时生成敌人
        this.enemySpawnTimer = this.time.addEvent({
            delay: GameConfig.GAME.SPAWN_RATE,
            callback: this.spawnEnemy,
            callbackScope: this,
            loop: true
        });
    }

    spawnEnemy() {
        const spawnX = GameConfig.GAME_WIDTH + 50;
        const spawnY = GameConfig.GAME_HEIGHT - 100;
        
        // 创建敌人
        const enemy = this.physics.add.sprite(spawnX, spawnY, 'enemy');
        enemy.setVelocityX(-GameConfig.ENEMIES.GRUNT.speed);
        enemy.health = GameConfig.ENEMIES.GRUNT.health;
        enemy.maxHealth = GameConfig.ENEMIES.GRUNT.health;
        enemy.damage = GameConfig.ENEMIES.GRUNT.damage;
        
        // 添加到敌人组
        this.enemyGroup.add(enemy);
        this.enemies.push(enemy);
        
        // 敌人超出屏幕时销毁
        enemy.checkWorldBounds = true;
        enemy.outOfBoundsKill = true;
    }

    setupControls() {
        // 创建键盘输入
        this.cursors = this.input.keyboard.createCursorKeys();
        this.wasd = this.input.keyboard.addKeys('W,S,A,D,SPACE,R,Q');
        
        // 鼠标输入
        this.input.on('pointerdown', this.handleMouseClick, this);
    }

    setupCamera() {
        // 摄像机跟随玩家
        this.cameras.main.startFollow(this.player);
        this.cameras.main.setLerp(0.1, 0.1);
    }

    update() {
        if (!this.player || !this.player.isAlive) return;
        
        // 更新玩家
        this.updatePlayer();
        
        // 更新敌人
        this.updateEnemies();
        
        // 更新子弹
        this.updateBullets();
    }

    updatePlayer() {
        const player = this.player;
        const speed = GameConfig.PLAYER.SPEED;
        
        // 重置速度
        player.setVelocity(0, 0);
        
        // 移动控制
        if (this.cursors.left.isDown || this.wasd.A.isDown) {
            player.setVelocityX(-speed);
        } else if (this.cursors.right.isDown || this.wasd.D.isDown) {
            player.setVelocityX(speed);
        }
        
        if (this.cursors.up.isDown || this.wasd.W.isDown) {
            player.setVelocityY(-speed);
        } else if (this.cursors.down.isDown || this.wasd.S.isDown) {
            player.setVelocityY(speed);
        }
        
        // 射击控制
        if (this.wasd.SPACE.isDown) {
            this.playerShoot();
        }
    }

    updateEnemies() {
        this.enemies.forEach((enemy, index) => {
            if (!enemy.active) {
                this.enemies.splice(index, 1);
                return;
            }
            
            // 简单AI：朝玩家方向移动
            if (this.player && this.player.isAlive) {
                const distance = Phaser.Math.Distance.Between(
                    enemy.x, enemy.y, this.player.x, this.player.y
                );
                
                if (distance < 300) {
                    this.physics.moveToObject(enemy, this.player, 50);
                }
            }
        });
    }

    updateBullets() {
        this.bullets.forEach((bullet, index) => {
            if (!bullet.active || bullet.x > GameConfig.GAME_WIDTH + 100) {
                bullet.destroy();
                this.bullets.splice(index, 1);
            }
        });
    }

    playerShoot() {
        const now = this.time.now;
        const weapon = GameConfig.WEAPONS[this.player.currentWeapon];
        
        if (now - this.player.lastShot > weapon.fireRate) {
            this.createBullet(this.player.x + 20, this.player.y, 400);
            this.player.lastShot = now;
        }
    }

    createBullet(x, y, velocityX) {
        const bullet = this.physics.add.sprite(x, y, 'bullet');
        bullet.setVelocityX(velocityX);
        bullet.damage = GameConfig.WEAPONS.ASSAULT_RIFLE.damage;
        
        this.bulletGroup.add(bullet);
        this.bullets.push(bullet);
        
        // 子弹生命周期
        this.time.delayedCall(2000, () => {
            if (bullet.active) {
                bullet.destroy();
            }
        });
    }

    handleMouseClick(pointer) {
        // 鼠标点击射击
        this.playerShoot();
    }

    bulletHitEnemy(bullet, enemy) {
        // 子弹击中敌人
        enemy.health -= bullet.damage;
        bullet.destroy();
        
        if (enemy.health <= 0) {
            enemy.destroy();
            // 这里可以添加得分、掉落物品等逻辑
        }
    }

    playerHitEnemy(player, enemy) {
        // 玩家被敌人击中
        if (player.isAlive) {
            player.health -= enemy.damage;
            
            if (player.health <= 0) {
                this.playerDie();
            }
            
            // 击退效果
            this.physics.moveToObject(player, enemy, -200);
        }
    }

    playerDie() {
        this.player.isAlive = false;
        this.player.setTint(0xff0000);
        
        // 重生逻辑
        this.time.delayedCall(GameConfig.PLAYER.RESPAWN_TIME, () => {
            this.respawnPlayer();
        });
    }

    respawnPlayer() {
        this.player.health = this.player.maxHealth;
        this.player.isAlive = true;
        this.player.clearTint();
        this.player.setPosition(100, GameConfig.GAME_HEIGHT - 100);
    }
}
