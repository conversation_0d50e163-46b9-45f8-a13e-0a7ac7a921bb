/**
 * @typedef {object} Phaser.Types.Tilemaps.StyleConfig
 * @since 3.0.0
 * 
 * @property {?(Phaser.Display.Color|number|null)} [tileColor=blue] - Color to use for drawing a filled rectangle at non-colliding tile locations. If set to null, non-colliding tiles will not be drawn.
 * @property {?(Phaser.Display.Color|number|null)} [collidingTileColor=orange] - Color to use for drawing a filled rectangle at colliding tile locations. If set to null, colliding tiles will not be drawn.
 * @property {?(Phaser.Display.Color|number|null)} [faceColor=grey] - Color to use for drawing a line at interesting tile faces. If set to null, interesting tile faces will not be drawn.
 */
