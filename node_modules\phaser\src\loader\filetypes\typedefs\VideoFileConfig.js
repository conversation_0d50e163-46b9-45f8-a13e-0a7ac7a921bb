/**
 * @typedef {object} Phaser.Types.Loader.FileTypes.VideoFileConfig
 *
 * @property {(string|Phaser.Types.Loader.FileTypes.VideoFileConfig)} key - The key to use for this file, or a file configuration object.
 * @property {(string|string[]|Phaser.Types.Loader.FileTypes.VideoFileURLConfig|Phaser.Types.Loader.FileTypes.VideoFileURLConfig[])} [url] - The absolute or relative URLs to load the video files from.
 * @property {boolean} [noAudio] - Does the video have an audio track? If not you can enable auto-playing on it.
 */
