/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2025 Phaser Studio Inc.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

/**
 * XBox 360 Gamepad Configuration.
 *
 * @name Phaser.Input.Gamepad.Configs.XBOX_360
 * @namespace
 * @since 3.0.0
 */
module.exports = {

    /**
     * D-Pad up
     *
     * @name Phaser.Input.Gamepad.Configs.XBOX_360.UP
     * @const
     * @type {number}
     * @since 3.0.0
     */
    UP: 12,

    /**
     * D-Pad down
     *
     * @name Phaser.Input.Gamepad.Configs.XBOX_360.DOWN
     * @const
     * @type {number}
     * @since 3.0.0
     */
    DOWN: 13,

    /**
     * D-Pad left
     *
     * @name Phaser.Input.Gamepad.Configs.XBOX_360.LEFT
     * @const
     * @type {number}
     * @since 3.0.0
     */
    LEFT: 14,

    /**
     * D-Pad right
     *
     * @name Phaser.Input.Gamepad.Configs.XBOX_360.RIGHT
     * @const
     * @type {number}
     * @since 3.0.0
     */
    RIGHT: 15,

    /**
     * XBox menu button
     *
     * @name Phaser.Input.Gamepad.Configs.XBOX_360.MENU
     * @const
     * @type {number}
     * @since 3.0.0
     */
    MENU: 16,

    /**
     * A button (Bottom)
     *
     * @name Phaser.Input.Gamepad.Configs.XBOX_360.A
     * @const
     * @type {number}
     * @since 3.0.0
     */
    A: 0,

    /**
     * B button (Right)
     *
     * @name Phaser.Input.Gamepad.Configs.XBOX_360.B
     * @const
     * @type {number}
     * @since 3.0.0
     */
    B: 1,

    /**
     * X button (Left)
     *
     * @name Phaser.Input.Gamepad.Configs.XBOX_360.X
     * @const
     * @type {number}
     * @since 3.0.0
     */
    X: 2,

    /**
     * Y button (Top)
     *
     * @name Phaser.Input.Gamepad.Configs.XBOX_360.Y
     * @const
     * @type {number}
     * @since 3.0.0
     */
    Y: 3,

    /**
     * Left Bumper
     *
     * @name Phaser.Input.Gamepad.Configs.XBOX_360.LB
     * @const
     * @type {number}
     * @since 3.0.0
     */
    LB: 4,

    /**
     * Right Bumper
     *
     * @name Phaser.Input.Gamepad.Configs.XBOX_360.RB
     * @const
     * @type {number}
     * @since 3.0.0
     */
    RB: 5,

    /**
     * Left Trigger
     *
     * @name Phaser.Input.Gamepad.Configs.XBOX_360.LT
     * @const
     * @type {number}
     * @since 3.0.0
     */
    LT: 6,

    /**
     * Right Trigger
     *
     * @name Phaser.Input.Gamepad.Configs.XBOX_360.RT
     * @const
     * @type {number}
     * @since 3.0.0
     */
    RT: 7,

    /**
     * Back / Change View button
     *
     * @name Phaser.Input.Gamepad.Configs.XBOX_360.BACK
     * @const
     * @type {number}
     * @since 3.0.0
     */
    BACK: 8,

    /**
     * Start button
     *
     * @name Phaser.Input.Gamepad.Configs.XBOX_360.START
     * @const
     * @type {number}
     * @since 3.0.0
     */
    START: 9,

    /**
     * Left Stick press
     *
     * @name Phaser.Input.Gamepad.Configs.XBOX_360.LS
     * @const
     * @type {number}
     * @since 3.0.0
     */
    LS: 10,

    /**
     * Right stick press
     *
     * @name Phaser.Input.Gamepad.Configs.XBOX_360.RS
     * @const
     * @type {number}
     * @since 3.0.0
     */
    RS: 11,

    /**
     * Left Stick horizontal
     *
     * @name Phaser.Input.Gamepad.Configs.XBOX_360.LEFT_STICK_H
     * @const
     * @type {number}
     * @since 3.0.0
     */
    LEFT_STICK_H: 0,

    /**
     * Left Stick vertical
     *
     * @name Phaser.Input.Gamepad.Configs.XBOX_360.LEFT_STICK_V
     * @const
     * @type {number}
     * @since 3.0.0
     */
    LEFT_STICK_V: 1,

    /**
     * Right Stick horizontal
     *
     * @name Phaser.Input.Gamepad.Configs.XBOX_360.RIGHT_STICK_H
     * @const
     * @type {number}
     * @since 3.0.0
     */
    RIGHT_STICK_H: 2,

    /**
     * Right Stick vertical
     *
     * @name Phaser.Input.Gamepad.Configs.XBOX_360.RIGHT_STICK_V
     * @const
     * @type {number}
     * @since 3.0.0
     */
    RIGHT_STICK_V: 3

};
