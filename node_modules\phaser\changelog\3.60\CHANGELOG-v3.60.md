# Version 3.60.0 - Miku - 12th April 2023

Due to the size and importance of the v3.60 release we have split the Change Log up into multiple sections.

This makes it easier for you to browse and find the information you need.

## New Features

These are the headliner features in this release:

* 14 bundled [Special FX](FX.md) including Bloom, Blur, Distort, Glow, Wipe and more
* Vastly improved [Mobile Rendering Performance](MobilePerformance.md) - over 7000% faster!
* New [Timeline Sequencer](Timeline.md) for creating complex flows of events
* New [Plane Game Object](PlaneGameObject.md) for perspective distortions
* New [Nine Slice Game Object](NineSliceGameObject.md) for perfect UI scaling
* [Built-in Spector JS](Spector.md) for WebGL debugging on desktop and mobile
* Brand new [Video Game Object](VideoGameObject.md) handles videos and media streams with ease
* Brand new [Particle Emitter](ParticleEmitter.md) comes with explosive new features
* Support for [Spatial Audio](SpatialSound.md) and distance based volume
* New [Spine 4 Plugin](Spine4.md) support
* Upgraded to [Matter Physics v0.19](MatterPhysics.md)
* New [Tween Manager](TweenManager.md) with better performance and memory management
* New [Dynamic Textures](DynamicTextures.md) for rendering to textures at runtime
* New [TimeStep features and Timer Event Updates](Timestep.md) for enforcing fps rates and more
* Support for [Compressed Textures](CompressedTextures.md)
* [ESM Module Support](ESMSupport.md)

## System and Plugins

Pick any of the following sections to see the breaking changes, new features, updates, and bug fixes for that area of the API.

* [Animation System](Animation.md)
* [Arcade Physics](ArcadePhysics.md)
* [Bitmap and Geometry Masks](Masks.md)
* [Camera System](Camera.md)
* [Canvas Renderer](CanvasRenderer.md)
* [WebGL Renderer](WebGLRenderer.md)
* [Colors and Display](Colors.md)
* [Game, Device and Game Config](Game.md)
* [Geometry, Paths and Curves](Geometry.md)
* [Input System](Input.md)
* [Loader System](Loader.md)
* [Scale Manager](ScaleManager.md)
* [Scenes and Scene Manager](Scene.md)
* [Sound System](Sound.md)
* [Spine 3 Plugin](Spine3.md)
* [Texture Manager](TextureManager.md)
* [Utils, Math and Actions](Utils.md)
* [Build Config and Browser Updates](Build.md)

## Game Object Updates

Finally, here are the updates related to Game Objects:

* [Bitmap Text Game Object](BitmapTextGameObject.md)
* [Container Game Object](Container.md)
* [Graphics Game Object](GraphicsGameObject.md)
* [Mesh Game Object, Vertices and Faces](Mesh.md)
* [Text Game Object](TextGameObject.md)
* [Tilemap Game Object](Tilemap.md)
* [All other Game Object related Updates](GameObject.md)

## Examples, Documentation, Beta Testing and TypeScript

My thanks to the following for helping with the Phaser 3 Examples, Beta Testing, Docs, and TypeScript definitions, either by reporting errors, fixing them, or helping author the docs:

| 💖 | 💖 | 💖 | 💖 |
| ----- | ----- | ----- | ----- |
| @0day-oni | @201flaviosilva | @AlbertMontagutCasero | @Arcanorum |
| @arosemena | @austinlyon | @chrisl8 | @christian-post |
| @danfoster | @darrylpizarro | @DeweyHur | @drunkcat |
| @ef4 | @eltociear | @EsteFilipe | @etherealmachine |
| @EmilSV | @Fake | @florestankorp | @hacheraw |
| @hanzooo | @jerricko | @joegaffey | @jonasrundberg |
| @kainage | @kootoopas | @lolimay | @MaffDev |
| @michalfialadev | @monteiz | @necrokot | @Nero0 |
| @OdinvonDoom | @orjandh | @pavle-goloskokovic | @PhaserEditor2D |
| @Pythux | @quocsinh | @rgk | @rollinsafary-inomma |
| @rstanuwijaya | 👑 @samme 👑 | @Smirnov48 | @steveja42 |
| @sylvainpolletvillard | @twoco | @ubershmekel | @ultimoistante |
| @VanaMartin | @vforsh | @Vidminas | @x-wk |
| @xmahle | @xuxucode | @YeloPartyHat | @ZekeLu |
| FromChris | Golen | OmniOwl | and you ... |

📖 Read the [Phaser 3 API Docs](https://newdocs.phaser.io/) 💻 Browse 2000+ [Code Examples](https://labs.phaser.io) 🤝 Join the awesome [Phaser Discord](https://discord.gg/phaser)
