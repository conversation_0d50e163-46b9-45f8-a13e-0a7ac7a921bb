/**
 * A Text Shadow configuration object as used by the Text Style.
 *
 * @typedef {object} Phaser.Types.GameObjects.Text.TextShadow
 * @since 3.0.0
 *
 * @property {number} [offsetX=0] - The horizontal offset of the shadow.
 * @property {number} [offsetY=0] - The vertical offset of the shadow.
 * @property {string} [color='#000'] - The color of the shadow, given as a CSS string value.
 * @property {number} [blur=0] - The amount of blur applied to the shadow. Leave as zero for a hard shadow.
 * @property {boolean} [stroke=false] - Apply the shadow to the stroke effect on the Text object?
 * @property {boolean} [fill=false] - Apply the shadow to the fill effect on the Text object?
 */
