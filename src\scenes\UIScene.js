import Phaser from 'phaser';

export class UIScene extends Phaser.Scene {
    constructor() {
        super({ key: 'UIScene' });
    }

    create() {
        // 获取游戏场景引用
        this.gameScene = this.scene.get('GameScene');
        
        // 创建UI元素
        this.createHealthBar();
        this.createAmmoDisplay();
        this.createScoreDisplay();
        this.createMinimap();
        
        console.log('UI场景已创建');
    }

    createHealthBar() {
        const x = 50;
        const y = 50;
        
        // 生命值背景
        this.healthBarBg = this.add.rectangle(x, y, 200, 20, 0x333333)
            .setOrigin(0, 0.5)
            .setScrollFactor(0);
        
        // 生命值条
        this.healthBar = this.add.rectangle(x, y, 200, 16, 0x00ff00)
            .setOrigin(0, 0.5)
            .setScrollFactor(0);
        
        // 生命值文字
        this.healthText = this.add.text(x, y - 30, 'HP: 100/100', {
            fontSize: '16px',
            fill: '#ffffff',
            fontFamily: 'Courier New'
        }).setScrollFactor(0);
    }

    createAmmoDisplay() {
        const x = this.cameras.main.width - 200;
        const y = 50;
        
        // 弹药显示
        this.ammoText = this.add.text(x, y, '弹药: 30/30', {
            fontSize: '16px',
            fill: '#ffffff',
            fontFamily: 'Courier New'
        }).setScrollFactor(0);
        
        // 武器显示
        this.weaponText = this.add.text(x, y + 25, '武器: 突击步枪', {
            fontSize: '14px',
            fill: '#ffff00',
            fontFamily: 'Courier New'
        }).setScrollFactor(0);
    }

    createScoreDisplay() {
        const x = this.cameras.main.width / 2;
        const y = 30;
        
        // 分数显示
        this.scoreText = this.add.text(x, y, '分数: 0', {
            fontSize: '20px',
            fill: '#00ff00',
            fontFamily: 'Courier New'
        }).setOrigin(0.5, 0).setScrollFactor(0);
        
        // 波次显示
        this.waveText = this.add.text(x, y + 30, '第 1 波', {
            fontSize: '16px',
            fill: '#ffffff',
            fontFamily: 'Courier New'
        }).setOrigin(0.5, 0).setScrollFactor(0);
    }

    createMinimap() {
        const size = 150;
        const x = this.cameras.main.width - size - 20;
        const y = this.cameras.main.height - size - 20;
        
        // 小地图背景
        this.minimapBg = this.add.rectangle(x, y, size, size, 0x000000, 0.7)
            .setScrollFactor(0);
        
        // 小地图边框
        this.minimapBorder = this.add.rectangle(x, y, size, size)
            .setStrokeStyle(2, 0x00ff00)
            .setScrollFactor(0);
        
        // 小地图标题
        this.add.text(x, y - size/2 - 20, '小地图', {
            fontSize: '12px',
            fill: '#ffffff',
            fontFamily: 'Courier New'
        }).setOrigin(0.5, 0).setScrollFactor(0);
    }

    update() {
        // 更新UI元素
        this.updateHealthBar();
        this.updateAmmoDisplay();
        this.updateMinimap();
    }

    updateHealthBar() {
        if (this.gameScene && this.gameScene.player) {
            const player = this.gameScene.player;
            const healthPercent = player.health / player.maxHealth;
            
            // 更新生命值条宽度
            this.healthBar.width = 200 * healthPercent;
            
            // 更新生命值颜色
            if (healthPercent > 0.6) {
                this.healthBar.setFillStyle(0x00ff00); // 绿色
            } else if (healthPercent > 0.3) {
                this.healthBar.setFillStyle(0xffff00); // 黄色
            } else {
                this.healthBar.setFillStyle(0xff0000); // 红色
            }
            
            // 更新文字
            this.healthText.setText(`HP: ${Math.max(0, Math.round(player.health))}/${player.maxHealth}`);
        }
    }

    updateAmmoDisplay() {
        if (this.gameScene && this.gameScene.player) {
            const player = this.gameScene.player;
            const weaponName = this.getWeaponName(player.currentWeapon);
            
            // 这里暂时显示固定值，后续会实现真实的弹药系统
            this.ammoText.setText('弹药: 30/30');
            this.weaponText.setText(`武器: ${weaponName}`);
        }
    }

    updateMinimap() {
        // 这里后续会实现小地图的实时更新
        // 显示玩家位置、敌人位置等
    }

    getWeaponName(weaponKey) {
        const weaponNames = {
            'ASSAULT_RIFLE': '突击步枪',
            'SHOTGUN': '霰弹枪',
            'SNIPER': '狙击步枪',
            'ROCKET_LAUNCHER': '火箭筒'
        };
        return weaponNames[weaponKey] || '未知武器';
    }

    // 显示游戏暂停菜单
    showPauseMenu() {
        const width = this.cameras.main.width;
        const height = this.cameras.main.height;
        
        // 暂停背景
        this.pauseOverlay = this.add.rectangle(width / 2, height / 2, width, height, 0x000000, 0.7)
            .setScrollFactor(0);
        
        // 暂停文字
        this.add.text(width / 2, height / 2 - 50, '游戏暂停', {
            fontSize: '32px',
            fill: '#ffffff',
            fontFamily: 'Courier New'
        }).setOrigin(0.5).setScrollFactor(0);
        
        // 继续按钮
        const continueBtn = this.add.text(width / 2, height / 2 + 20, '继续游戏', {
            fontSize: '20px',
            fill: '#00ff00',
            fontFamily: 'Courier New'
        }).setOrigin(0.5).setScrollFactor(0);
        
        continueBtn.setInteractive({ useHandCursor: true })
            .on('pointerdown', () => {
                this.hidePauseMenu();
                this.scene.resume('GameScene');
            });
        
        // 返回菜单按钮
        const menuBtn = this.add.text(width / 2, height / 2 + 60, '返回主菜单', {
            fontSize: '20px',
            fill: '#ff0000',
            fontFamily: 'Courier New'
        }).setOrigin(0.5).setScrollFactor(0);
        
        menuBtn.setInteractive({ useHandCursor: true })
            .on('pointerdown', () => {
                this.scene.stop('GameScene');
                this.scene.stop('UIScene');
                this.scene.start('MenuScene');
            });
    }

    hidePauseMenu() {
        if (this.pauseOverlay) {
            this.pauseOverlay.destroy();
        }
    }
}
