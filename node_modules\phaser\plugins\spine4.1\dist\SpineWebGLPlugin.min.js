(()=>{var t={4399:t=>{"use strict";var e=Object.prototype.hasOwnProperty,i="~";function s(){}function r(t,e,i){this.fn=t,this.context=e,this.once=i||!1}function n(t,e,s,n,a){if("function"!=typeof s)throw new TypeError("The listener must be a function");var o=new r(s,n||t,a),h=i?i+e:e;return t._events[h]?t._events[h].fn?t._events[h]=[t._events[h],o]:t._events[h].push(o):(t._events[h]=o,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new s:delete t._events[e]}function o(){this._events=new s,this._eventsCount=0}Object.create&&(s.prototype=Object.create(null),(new s).__proto__||(i=!1)),o.prototype.eventNames=function(){var t,s,r=[];if(0===this._eventsCount)return r;for(s in t=this._events)e.call(t,s)&&r.push(i?s.slice(1):s);return Object.getOwnPropertySymbols?r.concat(Object.getOwnPropertySymbols(t)):r},o.prototype.listeners=function(t){var e=i?i+t:t,s=this._events[e];if(!s)return[];if(s.fn)return[s.fn];for(var r=0,n=s.length,a=new Array(n);r<n;r++)a[r]=s[r].fn;return a},o.prototype.listenerCount=function(t){var e=i?i+t:t,s=this._events[e];return s?s.fn?1:s.length:0},o.prototype.emit=function(t,e,s,r,n,a){var o=i?i+t:t;if(!this._events[o])return!1;var h,l,c=this._events[o],u=arguments.length;if(c.fn){switch(c.once&&this.removeListener(t,c.fn,void 0,!0),u){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,e),!0;case 3:return c.fn.call(c.context,e,s),!0;case 4:return c.fn.call(c.context,e,s,r),!0;case 5:return c.fn.call(c.context,e,s,r,n),!0;case 6:return c.fn.call(c.context,e,s,r,n,a),!0}for(l=1,h=new Array(u-1);l<u;l++)h[l-1]=arguments[l];c.fn.apply(c.context,h)}else{var d,f=c.length;for(l=0;l<f;l++)switch(c[l].once&&this.removeListener(t,c[l].fn,void 0,!0),u){case 1:c[l].fn.call(c[l].context);break;case 2:c[l].fn.call(c[l].context,e);break;case 3:c[l].fn.call(c[l].context,e,s);break;case 4:c[l].fn.call(c[l].context,e,s,r);break;default:if(!h)for(d=1,h=new Array(u-1);d<u;d++)h[d-1]=arguments[d];c[l].fn.apply(c[l].context,h)}}return!0},o.prototype.on=function(t,e,i){return n(this,t,e,i,!1)},o.prototype.once=function(t,e,i){return n(this,t,e,i,!0)},o.prototype.removeListener=function(t,e,s,r){var n=i?i+t:t;if(!this._events[n])return this;if(!e)return a(this,n),this;var o=this._events[n];if(o.fn)o.fn!==e||r&&!o.once||s&&o.context!==s||a(this,n);else{for(var h=0,l=[],c=o.length;h<c;h++)(o[h].fn!==e||r&&!o[h].once||s&&o[h].context!==s)&&l.push(o[h]);l.length?this._events[n]=1===l.length?l[0]:l:a(this,n)}return this},o.prototype.removeAllListeners=function(t){var e;return t?(e=i?i+t:t,this._events[e]&&a(this,e)):(this._events=new s,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=i,o.EventEmitter=o,t.exports=o},6937:t=>{(function(){"use strict";var e=(()=>{var t=Object.defineProperty,e=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,r={};((e,i)=>{for(var s in i)t(e,s,{get:i[s],enumerable:!0})})(r,{AlphaTimeline:()=>ft,Animation:()=>C,AnimationState:()=>kt,AnimationStateAdapter:()=>Ot,AnimationStateData:()=>Vt,AssetManager:()=>Ei,AssetManagerBase:()=>ce,AtlasAttachmentLoader:()=>ne,Attachment:()=>b,AttachmentTimeline:()=>mt,BinaryInput:()=>Pe,BlendMode:()=>ke,Bone:()=>he,BoneData:()=>ae,BoundingBoxAttachment:()=>Ut,CURRENT:()=>Nt,CameraController:()=>ji,ClippingAttachment:()=>Wt,Color:()=>h,Color2Attribute:()=>is,ColorAttribute:()=>es,ConstraintData:()=>le,CurveTimeline:()=>Q,CurveTimeline1:()=>$,CurveTimeline2:()=>tt,DebugUtils:()=>m,DeformTimeline:()=>xt,Downloader:()=>ue,DrawOrderTimeline:()=>bt,Event:()=>de,EventData:()=>fe,EventQueue:()=>Lt,EventTimeline:()=>yt,EventType:()=>Pt,FIRST:()=>Dt,FakeTexture:()=>Zt,GLTexture:()=>Mi,HOLD_FIRST:()=>Bt,HOLD_MIX:()=>zt,HOLD_SUBSEQUENT:()=>Xt,IkConstraint:()=>pe,IkConstraintData:()=>ge,IkConstraintTimeline:()=>At,Input:()=>qi,IntSet:()=>n,Interpolation:()=>u,LoadingScreen:()=>Ms,M00:()=>Ti,M01:()=>Ii,M02:()=>Ci,M03:()=>Ri,M10:()=>ki,M11:()=>Fi,M12:()=>Li,M13:()=>Pi,M20:()=>Oi,M21:()=>Yi,M22:()=>Di,M23:()=>Xi,M30:()=>Bi,M31:()=>zi,M32:()=>_i,M33:()=>Ni,ManagedWebGLRenderingContext:()=>yi,MathUtils:()=>c,Matrix4:()=>Ui,Mesh:()=>Ki,MeshAttachment:()=>te,MixBlend:()=>R,MixDirection:()=>k,OrthoCamera:()=>Wi,PathAttachment:()=>ee,PathConstraint:()=>be,PathConstraintData:()=>me,PathConstraintMixTimeline:()=>Tt,PathConstraintPositionTimeline:()=>Et,PathConstraintSpacingTimeline:()=>St,PointAttachment:()=>ie,PolygonBatcher:()=>ns,Pool:()=>x,Position2Attribute:()=>Qi,Position3Attribute:()=>$i,PositionMode:()=>xe,Pow:()=>d,PowOut:()=>f,RGB2Timeline:()=>gt,RGBA2Timeline:()=>pt,RGBATimeline:()=>ut,RGBTimeline:()=>dt,RegionAttachment:()=>re,ResizeMode:()=>ws,RotateMode:()=>ye,RotateTimeline:()=>et,SETUP:()=>_t,SUBSEQUENT:()=>Yt,ScaleTimeline:()=>nt,ScaleXTimeline:()=>at,ScaleYTimeline:()=>ot,SceneRenderer:()=>ys,SequenceTimeline:()=>Ct,Shader:()=>Zi,ShapeRenderer:()=>as,ShapeType:()=>os,ShearTimeline:()=>ht,ShearXTimeline:()=>lt,ShearYTimeline:()=>ct,Skeleton:()=>Se,SkeletonBinary:()=>Le,SkeletonBounds:()=>ci,SkeletonClipping:()=>di,SkeletonData:()=>Te,SkeletonDebugRenderer:()=>ls,SkeletonJson:()=>fi,SkeletonRenderer:()=>ds,Skin:()=>Ce,SkinEntry:()=>Ie,Slot:()=>Ae,SlotData:()=>Re,SpacingMode:()=>ve,SpineCanvas:()=>Ts,StringSet:()=>a,TexCoordAttribute:()=>ts,Texture:()=>qt,TextureAtlas:()=>Kt,TextureAtlasPage:()=>Qt,TextureAtlasRegion:()=>$t,TextureFilter:()=>Gt,TextureRegion:()=>Ht,TextureWrap:()=>jt,TimeKeeper:()=>y,Timeline:()=>J,Touch:()=>Gi,TrackEntry:()=>Ft,TransformConstraint:()=>Me,TransformConstraintData:()=>Fe,TransformConstraintTimeline:()=>Mt,TransformMode:()=>oe,TranslateTimeline:()=>it,TranslateXTimeline:()=>st,TranslateYTimeline:()=>rt,Triangulator:()=>ui,Utils:()=>g,Vector2:()=>v,Vector3:()=>Si,VertexAttachment:()=>M,VertexAttribute:()=>Ji,VertexAttributeType:()=>ss,WebGLBlendModeConverter:()=>bi,WindowedMean:()=>w});var n=class{constructor(){this.array=new Array}add(t){let e=this.contains(t);return this.array[0|t]=0|t,!e}contains(t){return null!=this.array[0|t]}remove(t){this.array[0|t]=void 0}clear(){this.array.length=0}},a=class{constructor(){this.entries={},this.size=0}add(t){let e=this.entries[t];return this.entries[t]=!0,!e&&(this.size++,!0)}addAll(t){let e=this.size;for(var i=0,s=t.length;i<s;i++)this.add(t[i]);return e!=this.size}contains(t){return this.entries[t]}clear(){this.entries={},this.size=0}},o=class{constructor(t=0,e=0,i=0,s=0){this.r=t,this.g=e,this.b=i,this.a=s}set(t,e,i,s){return this.r=t,this.g=e,this.b=i,this.a=s,this.clamp()}setFromColor(t){return this.r=t.r,this.g=t.g,this.b=t.b,this.a=t.a,this}setFromString(t){return t="#"==t.charAt(0)?t.substr(1):t,this.r=parseInt(t.substr(0,2),16)/255,this.g=parseInt(t.substr(2,2),16)/255,this.b=parseInt(t.substr(4,2),16)/255,this.a=8!=t.length?1:parseInt(t.substr(6,2),16)/255,this}add(t,e,i,s){return this.r+=t,this.g+=e,this.b+=i,this.a+=s,this.clamp()}clamp(){return this.r<0?this.r=0:this.r>1&&(this.r=1),this.g<0?this.g=0:this.g>1&&(this.g=1),this.b<0?this.b=0:this.b>1&&(this.b=1),this.a<0?this.a=0:this.a>1&&(this.a=1),this}static rgba8888ToColor(t,e){t.r=((4278190080&e)>>>24)/255,t.g=((16711680&e)>>>16)/255,t.b=((65280&e)>>>8)/255,t.a=(255&e)/255}static rgb888ToColor(t,e){t.r=((16711680&e)>>>16)/255,t.g=((65280&e)>>>8)/255,t.b=(255&e)/255}static fromString(t){return(new o).setFromString(t)}},h=o;h.WHITE=new o(1,1,1,1),h.RED=new o(1,0,0,1),h.GREEN=new o(0,1,0,1),h.BLUE=new o(0,0,1,1),h.MAGENTA=new o(1,0,1,1);var l=class{static clamp(t,e,i){return t<e?e:t>i?i:t}static cosDeg(t){return Math.cos(t*l.degRad)}static sinDeg(t){return Math.sin(t*l.degRad)}static signum(t){return t>0?1:t<0?-1:0}static toInt(t){return t>0?Math.floor(t):Math.ceil(t)}static cbrt(t){let e=Math.pow(Math.abs(t),1/3);return t<0?-e:e}static randomTriangular(t,e){return l.randomTriangularWith(t,e,.5*(t+e))}static randomTriangularWith(t,e,i){let s=Math.random(),r=e-t;return s<=(i-t)/r?t+Math.sqrt(s*r*(i-t)):e-Math.sqrt((1-s)*r*(e-i))}static isPowerOfTwo(t){return t&&0==(t&t-1)}},c=l;c.PI=3.1415927,c.PI2=2*l.PI,c.radiansToDegrees=180/l.PI,c.radDeg=l.radiansToDegrees,c.degreesToRadians=l.PI/180,c.degRad=l.degreesToRadians;var u=class{apply(t,e,i){return t+(e-t)*this.applyInternal(i)}},d=class extends u{constructor(t){super(),this.power=2,this.power=t}applyInternal(t){return t<=.5?Math.pow(2*t,this.power)/2:Math.pow(2*(t-1),this.power)/(this.power%2==0?-2:2)+1}},f=class extends d{constructor(t){super(t)}applyInternal(t){return Math.pow(t-1,this.power)*(this.power%2==0?-1:1)+1}},p=class{static arrayCopy(t,e,i,s,r){for(let n=e,a=s;n<e+r;n++,a++)i[a]=t[n]}static arrayFill(t,e,i,s){for(let r=e;r<i;r++)t[r]=s}static setArraySize(t,e,i=0){let s=t.length;if(s==e)return t;if(t.length=e,s<e)for(let r=s;r<e;r++)t[r]=i;return t}static ensureArrayCapacity(t,e,i=0){return t.length>=e?t:p.setArraySize(t,e,i)}static newArray(t,e){let i=new Array(t);for(let s=0;s<t;s++)i[s]=e;return i}static newFloatArray(t){if(p.SUPPORTS_TYPED_ARRAYS)return new Float32Array(t);{let e=new Array(t);for(let t=0;t<e.length;t++)e[t]=0;return e}}static newShortArray(t){if(p.SUPPORTS_TYPED_ARRAYS)return new Int16Array(t);{let e=new Array(t);for(let t=0;t<e.length;t++)e[t]=0;return e}}static toFloatArray(t){return p.SUPPORTS_TYPED_ARRAYS?new Float32Array(t):t}static toSinglePrecision(t){return p.SUPPORTS_TYPED_ARRAYS?Math.fround(t):t}static webkit602BugfixHelper(t,e){}static contains(t,e,i=!0){for(var s=0;s<t.length;s++)if(t[s]==e)return!0;return!1}static enumValue(t,e){return t[e[0].toUpperCase()+e.slice(1)]}},g=p;g.SUPPORTS_TYPED_ARRAYS="undefined"!=typeof Float32Array;var m=class{static logBones(t){for(let e=0;e<t.bones.length;e++){let i=t.bones[e];console.log(i.data.name+", "+i.a+", "+i.b+", "+i.c+", "+i.d+", "+i.worldX+", "+i.worldY)}}},x=class{constructor(t){this.items=new Array,this.instantiator=t}obtain(){return this.items.length>0?this.items.pop():this.instantiator()}free(t){t.reset&&t.reset(),this.items.push(t)}freeAll(t){for(let e=0;e<t.length;e++)this.free(t[e])}clear(){this.items.length=0}},v=class{constructor(t=0,e=0){this.x=t,this.y=e}set(t,e){return this.x=t,this.y=e,this}length(){let t=this.x,e=this.y;return Math.sqrt(t*t+e*e)}normalize(){let t=this.length();return 0!=t&&(this.x/=t,this.y/=t),this}},y=class{constructor(){this.maxDelta=.064,this.framesPerSecond=0,this.delta=0,this.totalTime=0,this.lastTime=Date.now()/1e3,this.frameCount=0,this.frameTime=0}update(){let t=Date.now()/1e3;this.delta=t-this.lastTime,this.frameTime+=this.delta,this.totalTime+=this.delta,this.delta>this.maxDelta&&(this.delta=this.maxDelta),this.lastTime=t,this.frameCount++,this.frameTime>1&&(this.framesPerSecond=this.frameCount/this.frameTime,this.frameTime=0,this.frameCount=0)}},w=class{constructor(t=32){this.addedValues=0,this.lastValue=0,this.mean=0,this.dirty=!0,this.values=new Array(t)}hasEnoughData(){return this.addedValues>=this.values.length}addValue(t){this.addedValues<this.values.length&&this.addedValues++,this.values[this.lastValue++]=t,this.lastValue>this.values.length-1&&(this.lastValue=0),this.dirty=!0}getMean(){if(this.hasEnoughData()){if(this.dirty){let t=0;for(let e=0;e<this.values.length;e++)t+=this.values[e];this.mean=t/this.values.length,this.dirty=!1}return this.mean}return 0}},b=class{constructor(t){if(!t)throw new Error("name cannot be null.");this.name=t}},A=class extends b{constructor(t){super(t),this.id=A.nextID++,this.bones=null,this.vertices=[],this.worldVerticesLength=0,this.timelineAttachment=this}computeWorldVertices(t,e,i,s,r,n){i=r+(i>>1)*n;let a=t.bone.skeleton,o=t.deform,h=this.vertices,l=this.bones;if(!l){o.length>0&&(h=o);let a=t.bone,l=a.worldX,c=a.worldY,u=a.a,d=a.b,f=a.c,p=a.d;for(let t=e,a=r;a<i;t+=2,a+=n){let e=h[t],i=h[t+1];s[a]=e*u+i*d+l,s[a+1]=e*f+i*p+c}return}let c=0,u=0;for(let t=0;t<e;t+=2){let t=l[c];c+=t+1,u+=t}let d=a.bones;if(0==o.length)for(let t=r,e=3*u;t<i;t+=n){let i=0,r=0,n=l[c++];for(n+=c;c<n;c++,e+=3){let t=d[l[c]],s=h[e],n=h[e+1],a=h[e+2];i+=(s*t.a+n*t.b+t.worldX)*a,r+=(s*t.c+n*t.d+t.worldY)*a}s[t]=i,s[t+1]=r}else{let t=o;for(let e=r,a=3*u,o=u<<1;e<i;e+=n){let i=0,r=0,n=l[c++];for(n+=c;c<n;c++,a+=3,o+=2){let e=d[l[c]],s=h[a]+t[o],n=h[a+1]+t[o+1],u=h[a+2];i+=(s*e.a+n*e.b+e.worldX)*u,r+=(s*e.c+n*e.d+e.worldY)*u}s[e]=i,s[e+1]=r}}}copyTo(t){this.bones?(t.bones=new Array(this.bones.length),g.arrayCopy(this.bones,0,t.bones,0,this.bones.length)):t.bones=null,this.vertices&&(t.vertices=g.newFloatArray(this.vertices.length),g.arrayCopy(this.vertices,0,t.vertices,0,this.vertices.length)),t.worldVerticesLength=this.worldVerticesLength,t.timelineAttachment=this.timelineAttachment}},M=A;M.nextID=0;var E=class{constructor(t){this.id=E.nextID(),this.start=0,this.digits=0,this.setupIndex=0,this.regions=new Array(t)}copy(){let t=new E(this.regions.length);return g.arrayCopy(this.regions,0,t.regions,0,this.regions.length),t.start=this.start,t.digits=this.digits,t.setupIndex=this.setupIndex,t}apply(t,e){let i=t.sequenceIndex;-1==i&&(i=this.setupIndex),i>=this.regions.length&&(i=this.regions.length-1);let s=this.regions[i];e.region!=s&&(e.region=s,e.updateRegion())}getPath(t,e){let i=t,s=(this.start+e).toString();for(let t=this.digits-s.length;t>0;t--)i+="0";return i+=s,i}static nextID(){return E._nextID++}},S=E;S._nextID=0;var T=(t=>(t[t.hold=0]="hold",t[t.once=1]="once",t[t.loop=2]="loop",t[t.pingpong=3]="pingpong",t[t.onceReverse=4]="onceReverse",t[t.loopReverse=5]="loopReverse",t[t.pingpongReverse=6]="pingpongReverse",t))(T||{}),I=[0,1,2,3,4,5,6],C=class{constructor(t,e,i){if(this.timelines=[],this.timelineIds=new a,!t)throw new Error("name cannot be null.");this.name=t,this.setTimelines(e),this.duration=i}setTimelines(t){if(!t)throw new Error("timelines cannot be null.");this.timelines=t,this.timelineIds.clear();for(var e=0;e<t.length;e++)this.timelineIds.addAll(t[e].getPropertyIds())}hasTimeline(t){for(let e=0;e<t.length;e++)if(this.timelineIds.contains(t[e]))return!0;return!1}apply(t,e,i,s,r,n,a,o){if(!t)throw new Error("skeleton cannot be null.");s&&0!=this.duration&&(i%=this.duration,e>0&&(e%=this.duration));let h=this.timelines;for(let s=0,l=h.length;s<l;s++)h[s].apply(t,e,i,r,n,a,o)}},R=(t=>(t[t.setup=0]="setup",t[t.first=1]="first",t[t.replace=2]="replace",t[t.add=3]="add",t))(R||{}),k=(t=>(t[t.mixIn=0]="mixIn",t[t.mixOut=1]="mixOut",t))(k||{}),F=0,L=1,P=2,O=3,Y=4,D=5,X=6,B=7,z=8,_=9,N=10,V=11,U=12,W=13,q=14,G=15,j=16,H=17,Z=18,K=19,J=class{constructor(t,e){this.propertyIds=e,this.frames=g.newFloatArray(t*this.getFrameEntries())}getPropertyIds(){return this.propertyIds}getFrameEntries(){return 1}getFrameCount(){return this.frames.length/this.getFrameEntries()}getDuration(){return this.frames[this.frames.length-this.getFrameEntries()]}static search1(t,e){let i=t.length;for(let s=1;s<i;s++)if(t[s]>e)return s-1;return i-1}static search(t,e,i){let s=t.length;for(let r=i;r<s;r+=i)if(t[r]>e)return r-i;return s-i}},Q=class extends J{constructor(t,e,i){super(t,i),this.curves=g.newFloatArray(t+18*e),this.curves[t-1]=1}setLinear(t){this.curves[t]=0}setStepped(t){this.curves[t]=1}shrink(t){let e=this.getFrameCount()+18*t;if(this.curves.length>e){let t=g.newFloatArray(e);g.arrayCopy(this.curves,0,t,0,e),this.curves=t}}setBezier(t,e,i,s,r,n,a,o,h,l,c){let u=this.curves,d=this.getFrameCount()+18*t;0==i&&(u[e]=2+d);let f=.03*(s-2*n+o),p=.03*(r-2*a+h),g=.006*(3*(n-o)-s+l),m=.006*(3*(a-h)-r+c),x=2*f+g,v=2*p+m,y=.3*(n-s)+f+.16666667*g,w=.3*(a-r)+p+.16666667*m,b=s+y,A=r+w;for(let t=d+18;d<t;d+=2)u[d]=b,u[d+1]=A,y+=x,w+=v,x+=g,v+=m,b+=y,A+=w}getBezierValue(t,e,i,s){let r=this.curves;if(r[s]>t){let n=this.frames[e],a=this.frames[e+i];return a+(t-n)/(r[s]-n)*(r[s+1]-a)}let n=s+18;for(s+=2;s<n;s+=2)if(r[s]>=t){let e=r[s-2],i=r[s-1];return i+(t-e)/(r[s]-e)*(r[s+1]-i)}e+=this.getFrameEntries();let a=r[n-2],o=r[n-1];return o+(t-a)/(this.frames[e]-a)*(this.frames[e+i]-o)}},$=class extends Q{constructor(t,e,i){super(t,e,[i])}getFrameEntries(){return 2}setFrame(t,e,i){t<<=1,this.frames[t]=e,this.frames[t+1]=i}getCurveValue(t){let e=this.frames,i=e.length-2;for(let s=2;s<=i;s+=2)if(e[s]>t){i=s-2;break}let s=this.curves[i>>1];switch(s){case 0:let s=e[i],r=e[i+1];return r+(t-s)/(e[i+2]-s)*(e[i+2+1]-r);case 1:return e[i+1]}return this.getBezierValue(t,i,1,s-2)}},tt=class extends Q{constructor(t,e,i,s){super(t,e,[i,s])}getFrameEntries(){return 3}setFrame(t,e,i,s){t*=3,this.frames[t]=e,this.frames[t+1]=i,this.frames[t+2]=s}},et=class extends ${constructor(t,e,i){super(t,e,F+"|"+i),this.boneIndex=0,this.boneIndex=i}apply(t,e,i,s,r,n,a){let o=t.bones[this.boneIndex];if(!o.active)return;if(i<this.frames[0]){switch(n){case 0:return void(o.rotation=o.data.rotation);case 1:o.rotation+=(o.data.rotation-o.rotation)*r}return}let h=this.getCurveValue(i);switch(n){case 0:o.rotation=o.data.rotation+h*r;break;case 1:case 2:h+=o.data.rotation-o.rotation;case 3:o.rotation+=h*r}}},it=class extends tt{constructor(t,e,i){super(t,e,L+"|"+i,P+"|"+i),this.boneIndex=0,this.boneIndex=i}apply(t,e,i,s,r,n,a){let o=t.bones[this.boneIndex];if(!o.active)return;let h=this.frames;if(i<h[0]){switch(n){case 0:return o.x=o.data.x,void(o.y=o.data.y);case 1:o.x+=(o.data.x-o.x)*r,o.y+=(o.data.y-o.y)*r}return}let l=0,c=0,u=J.search(h,i,3),d=this.curves[u/3];switch(d){case 0:let t=h[u];l=h[u+1],c=h[u+2];let e=(i-t)/(h[u+3]-t);l+=(h[u+3+1]-l)*e,c+=(h[u+3+2]-c)*e;break;case 1:l=h[u+1],c=h[u+2];break;default:l=this.getBezierValue(i,u,1,d-2),c=this.getBezierValue(i,u,2,d+18-2)}switch(n){case 0:o.x=o.data.x+l*r,o.y=o.data.y+c*r;break;case 1:case 2:o.x+=(o.data.x+l-o.x)*r,o.y+=(o.data.y+c-o.y)*r;break;case 3:o.x+=l*r,o.y+=c*r}}},st=class extends ${constructor(t,e,i){super(t,e,L+"|"+i),this.boneIndex=0,this.boneIndex=i}apply(t,e,i,s,r,n,a){let o=t.bones[this.boneIndex];if(!o.active)return;if(i<this.frames[0]){switch(n){case 0:return void(o.x=o.data.x);case 1:o.x+=(o.data.x-o.x)*r}return}let h=this.getCurveValue(i);switch(n){case 0:o.x=o.data.x+h*r;break;case 1:case 2:o.x+=(o.data.x+h-o.x)*r;break;case 3:o.x+=h*r}}},rt=class extends ${constructor(t,e,i){super(t,e,P+"|"+i),this.boneIndex=0,this.boneIndex=i}apply(t,e,i,s,r,n,a){let o=t.bones[this.boneIndex];if(!o.active)return;if(i<this.frames[0]){switch(n){case 0:return void(o.y=o.data.y);case 1:o.y+=(o.data.y-o.y)*r}return}let h=this.getCurveValue(i);switch(n){case 0:o.y=o.data.y+h*r;break;case 1:case 2:o.y+=(o.data.y+h-o.y)*r;break;case 3:o.y+=h*r}}},nt=class extends tt{constructor(t,e,i){super(t,e,O+"|"+i,Y+"|"+i),this.boneIndex=0,this.boneIndex=i}apply(t,e,i,s,r,n,a){let o=t.bones[this.boneIndex];if(!o.active)return;let h,l,u=this.frames;if(i<u[0]){switch(n){case 0:return o.scaleX=o.data.scaleX,void(o.scaleY=o.data.scaleY);case 1:o.scaleX+=(o.data.scaleX-o.scaleX)*r,o.scaleY+=(o.data.scaleY-o.scaleY)*r}return}let d=J.search(u,i,3),f=this.curves[d/3];switch(f){case 0:let t=u[d];h=u[d+1],l=u[d+2];let e=(i-t)/(u[d+3]-t);h+=(u[d+3+1]-h)*e,l+=(u[d+3+2]-l)*e;break;case 1:h=u[d+1],l=u[d+2];break;default:h=this.getBezierValue(i,d,1,f-2),l=this.getBezierValue(i,d,2,f+18-2)}if(h*=o.data.scaleX,l*=o.data.scaleY,1==r)3==n?(o.scaleX+=h-o.data.scaleX,o.scaleY+=l-o.data.scaleY):(o.scaleX=h,o.scaleY=l);else{let t=0,e=0;if(1==a)switch(n){case 0:t=o.data.scaleX,e=o.data.scaleY,o.scaleX=t+(Math.abs(h)*c.signum(t)-t)*r,o.scaleY=e+(Math.abs(l)*c.signum(e)-e)*r;break;case 1:case 2:t=o.scaleX,e=o.scaleY,o.scaleX=t+(Math.abs(h)*c.signum(t)-t)*r,o.scaleY=e+(Math.abs(l)*c.signum(e)-e)*r;break;case 3:o.scaleX+=(h-o.data.scaleX)*r,o.scaleY+=(l-o.data.scaleY)*r}else switch(n){case 0:t=Math.abs(o.data.scaleX)*c.signum(h),e=Math.abs(o.data.scaleY)*c.signum(l),o.scaleX=t+(h-t)*r,o.scaleY=e+(l-e)*r;break;case 1:case 2:t=Math.abs(o.scaleX)*c.signum(h),e=Math.abs(o.scaleY)*c.signum(l),o.scaleX=t+(h-t)*r,o.scaleY=e+(l-e)*r;break;case 3:o.scaleX+=(h-o.data.scaleX)*r,o.scaleY+=(l-o.data.scaleY)*r}}}},at=class extends ${constructor(t,e,i){super(t,e,O+"|"+i),this.boneIndex=0,this.boneIndex=i}apply(t,e,i,s,r,n,a){let o=t.bones[this.boneIndex];if(!o.active)return;if(i<this.frames[0]){switch(n){case 0:return void(o.scaleX=o.data.scaleX);case 1:o.scaleX+=(o.data.scaleX-o.scaleX)*r}return}let h=this.getCurveValue(i)*o.data.scaleX;if(1==r)3==n?o.scaleX+=h-o.data.scaleX:o.scaleX=h;else{let t=0;if(1==a)switch(n){case 0:t=o.data.scaleX,o.scaleX=t+(Math.abs(h)*c.signum(t)-t)*r;break;case 1:case 2:t=o.scaleX,o.scaleX=t+(Math.abs(h)*c.signum(t)-t)*r;break;case 3:o.scaleX+=(h-o.data.scaleX)*r}else switch(n){case 0:t=Math.abs(o.data.scaleX)*c.signum(h),o.scaleX=t+(h-t)*r;break;case 1:case 2:t=Math.abs(o.scaleX)*c.signum(h),o.scaleX=t+(h-t)*r;break;case 3:o.scaleX+=(h-o.data.scaleX)*r}}}},ot=class extends ${constructor(t,e,i){super(t,e,Y+"|"+i),this.boneIndex=0,this.boneIndex=i}apply(t,e,i,s,r,n,a){let o=t.bones[this.boneIndex];if(!o.active)return;if(i<this.frames[0]){switch(n){case 0:return void(o.scaleY=o.data.scaleY);case 1:o.scaleY+=(o.data.scaleY-o.scaleY)*r}return}let h=this.getCurveValue(i)*o.data.scaleY;if(1==r)3==n?o.scaleY+=h-o.data.scaleY:o.scaleY=h;else{let t=0;if(1==a)switch(n){case 0:t=o.data.scaleY,o.scaleY=t+(Math.abs(h)*c.signum(t)-t)*r;break;case 1:case 2:t=o.scaleY,o.scaleY=t+(Math.abs(h)*c.signum(t)-t)*r;break;case 3:o.scaleY+=(h-o.data.scaleY)*r}else switch(n){case 0:t=Math.abs(o.data.scaleY)*c.signum(h),o.scaleY=t+(h-t)*r;break;case 1:case 2:t=Math.abs(o.scaleY)*c.signum(h),o.scaleY=t+(h-t)*r;break;case 3:o.scaleY+=(h-o.data.scaleY)*r}}}},ht=class extends tt{constructor(t,e,i){super(t,e,D+"|"+i,X+"|"+i),this.boneIndex=0,this.boneIndex=i}apply(t,e,i,s,r,n,a){let o=t.bones[this.boneIndex];if(!o.active)return;let h=this.frames;if(i<h[0]){switch(n){case 0:return o.shearX=o.data.shearX,void(o.shearY=o.data.shearY);case 1:o.shearX+=(o.data.shearX-o.shearX)*r,o.shearY+=(o.data.shearY-o.shearY)*r}return}let l=0,c=0,u=J.search(h,i,3),d=this.curves[u/3];switch(d){case 0:let t=h[u];l=h[u+1],c=h[u+2];let e=(i-t)/(h[u+3]-t);l+=(h[u+3+1]-l)*e,c+=(h[u+3+2]-c)*e;break;case 1:l=h[u+1],c=h[u+2];break;default:l=this.getBezierValue(i,u,1,d-2),c=this.getBezierValue(i,u,2,d+18-2)}switch(n){case 0:o.shearX=o.data.shearX+l*r,o.shearY=o.data.shearY+c*r;break;case 1:case 2:o.shearX+=(o.data.shearX+l-o.shearX)*r,o.shearY+=(o.data.shearY+c-o.shearY)*r;break;case 3:o.shearX+=l*r,o.shearY+=c*r}}},lt=class extends ${constructor(t,e,i){super(t,e,D+"|"+i),this.boneIndex=0,this.boneIndex=i}apply(t,e,i,s,r,n,a){let o=t.bones[this.boneIndex];if(!o.active)return;if(i<this.frames[0]){switch(n){case 0:return void(o.shearX=o.data.shearX);case 1:o.shearX+=(o.data.shearX-o.shearX)*r}return}let h=this.getCurveValue(i);switch(n){case 0:o.shearX=o.data.shearX+h*r;break;case 1:case 2:o.shearX+=(o.data.shearX+h-o.shearX)*r;break;case 3:o.shearX+=h*r}}},ct=class extends ${constructor(t,e,i){super(t,e,X+"|"+i),this.boneIndex=0,this.boneIndex=i}apply(t,e,i,s,r,n,a){let o=t.bones[this.boneIndex];if(!o.active)return;if(i<this.frames[0]){switch(n){case 0:return void(o.shearY=o.data.shearY);case 1:o.shearY+=(o.data.shearY-o.shearY)*r}return}let h=this.getCurveValue(i);switch(n){case 0:o.shearY=o.data.shearY+h*r;break;case 1:case 2:o.shearY+=(o.data.shearY+h-o.shearY)*r;break;case 3:o.shearY+=h*r}}},ut=class extends Q{constructor(t,e,i){super(t,e,[B+"|"+i,z+"|"+i]),this.slotIndex=0,this.slotIndex=i}getFrameEntries(){return 5}setFrame(t,e,i,s,r,n){t*=5,this.frames[t]=e,this.frames[t+1]=i,this.frames[t+2]=s,this.frames[t+3]=r,this.frames[t+4]=n}apply(t,e,i,s,r,n,a){let o=t.slots[this.slotIndex];if(!o.bone.active)return;let h=this.frames,l=o.color;if(i<h[0]){let t=o.data.color;switch(n){case 0:return void l.setFromColor(t);case 1:l.add((t.r-l.r)*r,(t.g-l.g)*r,(t.b-l.b)*r,(t.a-l.a)*r)}return}let c=0,u=0,d=0,f=0,p=J.search(h,i,5),g=this.curves[p/5];switch(g){case 0:let t=h[p];c=h[p+1],u=h[p+2],d=h[p+3],f=h[p+4];let e=(i-t)/(h[p+5]-t);c+=(h[p+5+1]-c)*e,u+=(h[p+5+2]-u)*e,d+=(h[p+5+3]-d)*e,f+=(h[p+5+4]-f)*e;break;case 1:c=h[p+1],u=h[p+2],d=h[p+3],f=h[p+4];break;default:c=this.getBezierValue(i,p,1,g-2),u=this.getBezierValue(i,p,2,g+18-2),d=this.getBezierValue(i,p,3,g+36-2),f=this.getBezierValue(i,p,4,g+54-2)}1==r?l.set(c,u,d,f):(0==n&&l.setFromColor(o.data.color),l.add((c-l.r)*r,(u-l.g)*r,(d-l.b)*r,(f-l.a)*r))}},dt=class extends Q{constructor(t,e,i){super(t,e,[B+"|"+i]),this.slotIndex=0,this.slotIndex=i}getFrameEntries(){return 4}setFrame(t,e,i,s,r){t<<=2,this.frames[t]=e,this.frames[t+1]=i,this.frames[t+2]=s,this.frames[t+3]=r}apply(t,e,i,s,r,n,a){let o=t.slots[this.slotIndex];if(!o.bone.active)return;let h=this.frames,l=o.color;if(i<h[0]){let t=o.data.color;switch(n){case 0:return l.r=t.r,l.g=t.g,void(l.b=t.b);case 1:l.r+=(t.r-l.r)*r,l.g+=(t.g-l.g)*r,l.b+=(t.b-l.b)*r}return}let c=0,u=0,d=0,f=J.search(h,i,4),p=this.curves[f>>2];switch(p){case 0:let t=h[f];c=h[f+1],u=h[f+2],d=h[f+3];let e=(i-t)/(h[f+4]-t);c+=(h[f+4+1]-c)*e,u+=(h[f+4+2]-u)*e,d+=(h[f+4+3]-d)*e;break;case 1:c=h[f+1],u=h[f+2],d=h[f+3];break;default:c=this.getBezierValue(i,f,1,p-2),u=this.getBezierValue(i,f,2,p+18-2),d=this.getBezierValue(i,f,3,p+36-2)}if(1==r)l.r=c,l.g=u,l.b=d;else{if(0==n){let t=o.data.color;l.r=t.r,l.g=t.g,l.b=t.b}l.r+=(c-l.r)*r,l.g+=(u-l.g)*r,l.b+=(d-l.b)*r}}},ft=class extends ${constructor(t,e,i){super(t,e,z+"|"+i),this.slotIndex=0,this.slotIndex=i}apply(t,e,i,s,r,n,a){let o=t.slots[this.slotIndex];if(!o.bone.active)return;let h=o.color;if(i<this.frames[0]){let t=o.data.color;switch(n){case 0:return void(h.a=t.a);case 1:h.a+=(t.a-h.a)*r}return}let l=this.getCurveValue(i);1==r?h.a=l:(0==n&&(h.a=o.data.color.a),h.a+=(l-h.a)*r)}},pt=class extends Q{constructor(t,e,i){super(t,e,[B+"|"+i,z+"|"+i,_+"|"+i]),this.slotIndex=0,this.slotIndex=i}getFrameEntries(){return 8}setFrame(t,e,i,s,r,n,a,o,h){t<<=3,this.frames[t]=e,this.frames[t+1]=i,this.frames[t+2]=s,this.frames[t+3]=r,this.frames[t+4]=n,this.frames[t+5]=a,this.frames[t+6]=o,this.frames[t+7]=h}apply(t,e,i,s,r,n,a){let o=t.slots[this.slotIndex];if(!o.bone.active)return;let h=this.frames,l=o.color,c=o.darkColor;if(i<h[0]){let t=o.data.color,e=o.data.darkColor;switch(n){case 0:return l.setFromColor(t),c.r=e.r,c.g=e.g,void(c.b=e.b);case 1:l.add((t.r-l.r)*r,(t.g-l.g)*r,(t.b-l.b)*r,(t.a-l.a)*r),c.r+=(e.r-c.r)*r,c.g+=(e.g-c.g)*r,c.b+=(e.b-c.b)*r}return}let u=0,d=0,f=0,p=0,g=0,m=0,x=0,v=J.search(h,i,8),y=this.curves[v>>3];switch(y){case 0:let t=h[v];u=h[v+1],d=h[v+2],f=h[v+3],p=h[v+4],g=h[v+5],m=h[v+6],x=h[v+7];let e=(i-t)/(h[v+8]-t);u+=(h[v+8+1]-u)*e,d+=(h[v+8+2]-d)*e,f+=(h[v+8+3]-f)*e,p+=(h[v+8+4]-p)*e,g+=(h[v+8+5]-g)*e,m+=(h[v+8+6]-m)*e,x+=(h[v+8+7]-x)*e;break;case 1:u=h[v+1],d=h[v+2],f=h[v+3],p=h[v+4],g=h[v+5],m=h[v+6],x=h[v+7];break;default:u=this.getBezierValue(i,v,1,y-2),d=this.getBezierValue(i,v,2,y+18-2),f=this.getBezierValue(i,v,3,y+36-2),p=this.getBezierValue(i,v,4,y+54-2),g=this.getBezierValue(i,v,5,y+72-2),m=this.getBezierValue(i,v,6,y+90-2),x=this.getBezierValue(i,v,7,y+108-2)}if(1==r)l.set(u,d,f,p),c.r=g,c.g=m,c.b=x;else{if(0==n){l.setFromColor(o.data.color);let t=o.data.darkColor;c.r=t.r,c.g=t.g,c.b=t.b}l.add((u-l.r)*r,(d-l.g)*r,(f-l.b)*r,(p-l.a)*r),c.r+=(g-c.r)*r,c.g+=(m-c.g)*r,c.b+=(x-c.b)*r}}},gt=class extends Q{constructor(t,e,i){super(t,e,[B+"|"+i,_+"|"+i]),this.slotIndex=0,this.slotIndex=i}getFrameEntries(){return 7}setFrame(t,e,i,s,r,n,a,o){t*=7,this.frames[t]=e,this.frames[t+1]=i,this.frames[t+2]=s,this.frames[t+3]=r,this.frames[t+4]=n,this.frames[t+5]=a,this.frames[t+6]=o}apply(t,e,i,s,r,n,a){let o=t.slots[this.slotIndex];if(!o.bone.active)return;let h=this.frames,l=o.color,c=o.darkColor;if(i<h[0]){let t=o.data.color,e=o.data.darkColor;switch(n){case 0:return l.r=t.r,l.g=t.g,l.b=t.b,c.r=e.r,c.g=e.g,void(c.b=e.b);case 1:l.r+=(t.r-l.r)*r,l.g+=(t.g-l.g)*r,l.b+=(t.b-l.b)*r,c.r+=(e.r-c.r)*r,c.g+=(e.g-c.g)*r,c.b+=(e.b-c.b)*r}return}let u=0,d=0,f=0,p=0,g=0,m=0,x=J.search(h,i,7),v=this.curves[x/7];switch(v){case 0:let t=h[x];u=h[x+1],d=h[x+2],f=h[x+3],p=h[x+4],g=h[x+5],m=h[x+6];let e=(i-t)/(h[x+7]-t);u+=(h[x+7+1]-u)*e,d+=(h[x+7+2]-d)*e,f+=(h[x+7+3]-f)*e,p+=(h[x+7+4]-p)*e,g+=(h[x+7+5]-g)*e,m+=(h[x+7+6]-m)*e;break;case 1:u=h[x+1],d=h[x+2],f=h[x+3],p=h[x+4],g=h[x+5],m=h[x+6];break;default:u=this.getBezierValue(i,x,1,v-2),d=this.getBezierValue(i,x,2,v+18-2),f=this.getBezierValue(i,x,3,v+36-2),p=this.getBezierValue(i,x,4,v+54-2),g=this.getBezierValue(i,x,5,v+72-2),m=this.getBezierValue(i,x,6,v+90-2)}if(1==r)l.r=u,l.g=d,l.b=f,c.r=p,c.g=g,c.b=m;else{if(0==n){let t=o.data.color,e=o.data.darkColor;l.r=t.r,l.g=t.g,l.b=t.b,c.r=e.r,c.g=e.g,c.b=e.b}l.r+=(u-l.r)*r,l.g+=(d-l.g)*r,l.b+=(f-l.b)*r,c.r+=(p-c.r)*r,c.g+=(g-c.g)*r,c.b+=(m-c.b)*r}}},mt=class extends J{constructor(t,e){super(t,[N+"|"+e]),this.slotIndex=0,this.slotIndex=e,this.attachmentNames=new Array(t)}getFrameCount(){return this.frames.length}setFrame(t,e,i){this.frames[t]=e,this.attachmentNames[t]=i}apply(t,e,i,s,r,n,a){let o=t.slots[this.slotIndex];o.bone.active&&(1!=a?i<this.frames[0]?0!=n&&1!=n||this.setAttachment(t,o,o.data.attachmentName):this.setAttachment(t,o,this.attachmentNames[J.search1(this.frames,i)]):0==n&&this.setAttachment(t,o,o.data.attachmentName))}setAttachment(t,e,i){e.setAttachment(i?t.getAttachment(this.slotIndex,i):null)}},xt=class extends Q{constructor(t,e,i,s){super(t,e,[V+"|"+i+"|"+s.id]),this.slotIndex=0,this.slotIndex=i,this.attachment=s,this.vertices=new Array(t)}getFrameCount(){return this.frames.length}setFrame(t,e,i){this.frames[t]=e,this.vertices[t]=i}setBezier(t,e,i,s,r,n,a,o,h,l,c){let u=this.curves,d=this.getFrameCount()+18*t;0==i&&(u[e]=2+d);let f=.03*(s-2*n+o),p=.03*h-.06*a,g=.006*(3*(n-o)-s+l),m=.018*(a-h+.33333333),x=2*f+g,v=2*p+m,y=.3*(n-s)+f+.16666667*g,w=.3*a+p+.16666667*m,b=s+y,A=w;for(let t=d+18;d<t;d+=2)u[d]=b,u[d+1]=A,y+=x,w+=v,x+=g,v+=m,b+=y,A+=w}getCurvePercent(t,e){let i=this.curves,s=i[e];switch(s){case 0:let i=this.frames[e];return(t-i)/(this.frames[e+this.getFrameEntries()]-i);case 1:return 0}if(s-=2,i[s]>t){let r=this.frames[e];return i[s+1]*(t-r)/(i[s]-r)}let r=s+18;for(s+=2;s<r;s+=2)if(i[s]>=t){let e=i[s-2],r=i[s-1];return r+(t-e)/(i[s]-e)*(i[s+1]-r)}let n=i[r-2],a=i[r-1];return a+(1-a)*(t-n)/(this.frames[e+this.getFrameEntries()]-n)}apply(t,e,i,s,r,n,a){let o=t.slots[this.slotIndex];if(!o.bone.active)return;let h=o.getAttachment();if(!h)return;if(!(h instanceof M)||h.timelineAttachment!=this.attachment)return;let l=o.deform;0==l.length&&(n=0);let c=this.vertices,u=c[0].length,d=this.frames;if(i<d[0]){switch(n){case 0:return void(l.length=0);case 1:if(1==r)return void(l.length=0);l.length=u;let t=h;if(t.bones){r=1-r;for(f=0;f<u;f++)l[f]*=r}else{let e=t.vertices;for(var f=0;f<u;f++)l[f]+=(e[f]-l[f])*r}}return}if(l.length=u,i>=d[d.length-1]){let t=c[d.length-1];if(1==r)if(3==n){let e=h;if(e.bones)for(let e=0;e<u;e++)l[e]+=t[e];else{let i=e.vertices;for(let e=0;e<u;e++)l[e]+=t[e]-i[e]}}else g.arrayCopy(t,0,l,0,u);else switch(n){case 0:{let e=h;if(e.bones)for(let e=0;e<u;e++)l[e]=t[e]*r;else{let i=e.vertices;for(let e=0;e<u;e++){let s=i[e];l[e]=s+(t[e]-s)*r}}break}case 1:case 2:for(let e=0;e<u;e++)l[e]+=(t[e]-l[e])*r;break;case 3:let e=h;if(e.bones)for(let e=0;e<u;e++)l[e]+=t[e]*r;else{let i=e.vertices;for(let e=0;e<u;e++)l[e]+=(t[e]-i[e])*r}}return}let p=J.search1(d,i),m=this.getCurvePercent(i,p),x=c[p],v=c[p+1];if(1==r)if(3==n){let t=h;if(t.bones)for(let t=0;t<u;t++){let e=x[t];l[t]+=e+(v[t]-e)*m}else{let e=t.vertices;for(let t=0;t<u;t++){let i=x[t];l[t]+=i+(v[t]-i)*m-e[t]}}}else for(let t=0;t<u;t++){let e=x[t];l[t]=e+(v[t]-e)*m}else switch(n){case 0:{let t=h;if(t.bones)for(let t=0;t<u;t++){let e=x[t];l[t]=(e+(v[t]-e)*m)*r}else{let e=t.vertices;for(let t=0;t<u;t++){let i=x[t],s=e[t];l[t]=s+(i+(v[t]-i)*m-s)*r}}break}case 1:case 2:for(let t=0;t<u;t++){let e=x[t];l[t]+=(e+(v[t]-e)*m-l[t])*r}break;case 3:let t=h;if(t.bones)for(let t=0;t<u;t++){let e=x[t];l[t]+=(e+(v[t]-e)*m)*r}else{let e=t.vertices;for(let t=0;t<u;t++){let i=x[t];l[t]+=(i+(v[t]-i)*m-e[t])*r}}}}},vt=class extends J{constructor(t){super(t,vt.propertyIds),this.events=new Array(t)}getFrameCount(){return this.frames.length}setFrame(t,e){this.frames[t]=e.time,this.events[t]=e}apply(t,e,i,s,r,n,a){if(!s)return;let o=this.frames,h=this.frames.length;if(e>i)this.apply(t,e,Number.MAX_VALUE,s,r,n,a),e=-1;else if(e>=o[h-1])return;if(i<o[0])return;let l=0;if(e<o[0])l=0;else{l=J.search1(o,e)+1;let t=o[l];for(;l>0&&o[l-1]==t;)l--}for(;l<h&&i>=o[l];l++)s.push(this.events[l])}},yt=vt;yt.propertyIds=[""+U];var wt=class extends J{constructor(t){super(t,wt.propertyIds),this.drawOrders=new Array(t)}getFrameCount(){return this.frames.length}setFrame(t,e,i){this.frames[t]=e,this.drawOrders[t]=i}apply(t,e,i,s,r,n,a){if(1==a)return void(0==n&&g.arrayCopy(t.slots,0,t.drawOrder,0,t.slots.length));if(i<this.frames[0])return void(0!=n&&1!=n||g.arrayCopy(t.slots,0,t.drawOrder,0,t.slots.length));let o=J.search1(this.frames,i),h=this.drawOrders[o];if(h){let e=t.drawOrder,i=t.slots;for(let t=0,s=h.length;t<s;t++)e[t]=i[h[t]]}else g.arrayCopy(t.slots,0,t.drawOrder,0,t.slots.length)}},bt=wt;bt.propertyIds=[""+W];var At=class extends Q{constructor(t,e,i){super(t,e,[q+"|"+i]),this.ikConstraintIndex=0,this.ikConstraintIndex=i}getFrameEntries(){return 6}setFrame(t,e,i,s,r,n,a){t*=6,this.frames[t]=e,this.frames[t+1]=i,this.frames[t+2]=s,this.frames[t+3]=r,this.frames[t+4]=n?1:0,this.frames[t+5]=a?1:0}apply(t,e,i,s,r,n,a){let o=t.ikConstraints[this.ikConstraintIndex];if(!o.active)return;let h=this.frames;if(i<h[0]){switch(n){case 0:return o.mix=o.data.mix,o.softness=o.data.softness,o.bendDirection=o.data.bendDirection,o.compress=o.data.compress,void(o.stretch=o.data.stretch);case 1:o.mix+=(o.data.mix-o.mix)*r,o.softness+=(o.data.softness-o.softness)*r,o.bendDirection=o.data.bendDirection,o.compress=o.data.compress,o.stretch=o.data.stretch}return}let l=0,c=0,u=J.search(h,i,6),d=this.curves[u/6];switch(d){case 0:let t=h[u];l=h[u+1],c=h[u+2];let e=(i-t)/(h[u+6]-t);l+=(h[u+6+1]-l)*e,c+=(h[u+6+2]-c)*e;break;case 1:l=h[u+1],c=h[u+2];break;default:l=this.getBezierValue(i,u,1,d-2),c=this.getBezierValue(i,u,2,d+18-2)}0==n?(o.mix=o.data.mix+(l-o.data.mix)*r,o.softness=o.data.softness+(c-o.data.softness)*r,1==a?(o.bendDirection=o.data.bendDirection,o.compress=o.data.compress,o.stretch=o.data.stretch):(o.bendDirection=h[u+3],o.compress=0!=h[u+4],o.stretch=0!=h[u+5])):(o.mix+=(l-o.mix)*r,o.softness+=(c-o.softness)*r,0==a&&(o.bendDirection=h[u+3],o.compress=0!=h[u+4],o.stretch=0!=h[u+5]))}},Mt=class extends Q{constructor(t,e,i){super(t,e,[G+"|"+i]),this.transformConstraintIndex=0,this.transformConstraintIndex=i}getFrameEntries(){return 7}setFrame(t,e,i,s,r,n,a,o){let h=this.frames;h[t*=7]=e,h[t+1]=i,h[t+2]=s,h[t+3]=r,h[t+4]=n,h[t+5]=a,h[t+6]=o}apply(t,e,i,s,r,n,a){let o=t.transformConstraints[this.transformConstraintIndex];if(!o.active)return;let h,l,c,u,d,f,p=this.frames;if(i<p[0]){let t=o.data;switch(n){case 0:return o.mixRotate=t.mixRotate,o.mixX=t.mixX,o.mixY=t.mixY,o.mixScaleX=t.mixScaleX,o.mixScaleY=t.mixScaleY,void(o.mixShearY=t.mixShearY);case 1:o.mixRotate+=(t.mixRotate-o.mixRotate)*r,o.mixX+=(t.mixX-o.mixX)*r,o.mixY+=(t.mixY-o.mixY)*r,o.mixScaleX+=(t.mixScaleX-o.mixScaleX)*r,o.mixScaleY+=(t.mixScaleY-o.mixScaleY)*r,o.mixShearY+=(t.mixShearY-o.mixShearY)*r}return}let g=J.search(p,i,7),m=this.curves[g/7];switch(m){case 0:let t=p[g];h=p[g+1],l=p[g+2],c=p[g+3],u=p[g+4],d=p[g+5],f=p[g+6];let e=(i-t)/(p[g+7]-t);h+=(p[g+7+1]-h)*e,l+=(p[g+7+2]-l)*e,c+=(p[g+7+3]-c)*e,u+=(p[g+7+4]-u)*e,d+=(p[g+7+5]-d)*e,f+=(p[g+7+6]-f)*e;break;case 1:h=p[g+1],l=p[g+2],c=p[g+3],u=p[g+4],d=p[g+5],f=p[g+6];break;default:h=this.getBezierValue(i,g,1,m-2),l=this.getBezierValue(i,g,2,m+18-2),c=this.getBezierValue(i,g,3,m+36-2),u=this.getBezierValue(i,g,4,m+54-2),d=this.getBezierValue(i,g,5,m+72-2),f=this.getBezierValue(i,g,6,m+90-2)}if(0==n){let t=o.data;o.mixRotate=t.mixRotate+(h-t.mixRotate)*r,o.mixX=t.mixX+(l-t.mixX)*r,o.mixY=t.mixY+(c-t.mixY)*r,o.mixScaleX=t.mixScaleX+(u-t.mixScaleX)*r,o.mixScaleY=t.mixScaleY+(d-t.mixScaleY)*r,o.mixShearY=t.mixShearY+(f-t.mixShearY)*r}else o.mixRotate+=(h-o.mixRotate)*r,o.mixX+=(l-o.mixX)*r,o.mixY+=(c-o.mixY)*r,o.mixScaleX+=(u-o.mixScaleX)*r,o.mixScaleY+=(d-o.mixScaleY)*r,o.mixShearY+=(f-o.mixShearY)*r}},Et=class extends ${constructor(t,e,i){super(t,e,j+"|"+i),this.pathConstraintIndex=0,this.pathConstraintIndex=i}apply(t,e,i,s,r,n,a){let o=t.pathConstraints[this.pathConstraintIndex];if(!o.active)return;if(i<this.frames[0]){switch(n){case 0:return void(o.position=o.data.position);case 1:o.position+=(o.data.position-o.position)*r}return}let h=this.getCurveValue(i);0==n?o.position=o.data.position+(h-o.data.position)*r:o.position+=(h-o.position)*r}},St=class extends ${constructor(t,e,i){super(t,e,H+"|"+i),this.pathConstraintIndex=0,this.pathConstraintIndex=i}apply(t,e,i,s,r,n,a){let o=t.pathConstraints[this.pathConstraintIndex];if(!o.active)return;if(i<this.frames[0]){switch(n){case 0:return void(o.spacing=o.data.spacing);case 1:o.spacing+=(o.data.spacing-o.spacing)*r}return}let h=this.getCurveValue(i);0==n?o.spacing=o.data.spacing+(h-o.data.spacing)*r:o.spacing+=(h-o.spacing)*r}},Tt=class extends Q{constructor(t,e,i){super(t,e,[Z+"|"+i]),this.pathConstraintIndex=0,this.pathConstraintIndex=i}getFrameEntries(){return 4}setFrame(t,e,i,s,r){let n=this.frames;n[t<<=2]=e,n[t+1]=i,n[t+2]=s,n[t+3]=r}apply(t,e,i,s,r,n,a){let o=t.pathConstraints[this.pathConstraintIndex];if(!o.active)return;let h,l,c,u=this.frames;if(i<u[0]){switch(n){case 0:return o.mixRotate=o.data.mixRotate,o.mixX=o.data.mixX,void(o.mixY=o.data.mixY);case 1:o.mixRotate+=(o.data.mixRotate-o.mixRotate)*r,o.mixX+=(o.data.mixX-o.mixX)*r,o.mixY+=(o.data.mixY-o.mixY)*r}return}let d=J.search(u,i,4),f=this.curves[d>>2];switch(f){case 0:let t=u[d];h=u[d+1],l=u[d+2],c=u[d+3];let e=(i-t)/(u[d+4]-t);h+=(u[d+4+1]-h)*e,l+=(u[d+4+2]-l)*e,c+=(u[d+4+3]-c)*e;break;case 1:h=u[d+1],l=u[d+2],c=u[d+3];break;default:h=this.getBezierValue(i,d,1,f-2),l=this.getBezierValue(i,d,2,f+18-2),c=this.getBezierValue(i,d,3,f+36-2)}if(0==n){let t=o.data;o.mixRotate=t.mixRotate+(h-t.mixRotate)*r,o.mixX=t.mixX+(l-t.mixX)*r,o.mixY=t.mixY+(c-t.mixY)*r}else o.mixRotate+=(h-o.mixRotate)*r,o.mixX+=(l-o.mixX)*r,o.mixY+=(c-o.mixY)*r}},It=class extends J{constructor(t,e,i){super(t,[K+"|"+e+"|"+i.sequence.id]),this.slotIndex=e,this.attachment=i}getFrameEntries(){return It.ENTRIES}getSlotIndex(){return this.slotIndex}getAttachment(){return this.attachment}setFrame(t,e,i,s,r){let n=this.frames;n[t*=It.ENTRIES]=e,n[t+It.MODE]=i|s<<4,n[t+It.DELAY]=r}apply(t,e,i,s,r,n,a){let o=t.slots[this.slotIndex];if(!o.bone.active)return;let h=o.attachment,l=this.attachment;if(!(h==l||h instanceof M&&h.timelineAttachment==l))return;let c=this.frames;if(i<c[0])return void(0!=n&&1!=n||(o.sequenceIndex=-1));let u=J.search(c,i,It.ENTRIES),d=c[u],f=c[u+It.MODE],p=c[u+It.DELAY];if(!this.attachment.sequence)return;let g=f>>4,m=this.attachment.sequence.regions.length,x=I[15&f];if(0!=x)switch(g+=(i-d)/p+1e-5|0,x){case 1:g=Math.min(m-1,g);break;case 2:g%=m;break;case 3:{let t=(m<<1)-2;g=0==t?0:g%t,g>=m&&(g=t-g);break}case 4:g=Math.max(m-1-g,0);break;case 5:g=m-1-g%m;break;case 6:{let t=(m<<1)-2;g=0==t?0:(g+m-1)%t,g>=m&&(g=t-g)}}o.sequenceIndex=g}},Ct=It;Ct.ENTRIES=3,Ct.MODE=1,Ct.DELAY=2;var Rt=class{constructor(t){this.tracks=new Array,this.timeScale=1,this.unkeyedState=0,this.events=new Array,this.listeners=new Array,this.queue=new Lt(this),this.propertyIDs=new a,this.animationsChanged=!1,this.trackEntryPool=new x((()=>new Ft)),this.data=t}static emptyAnimation(){return Rt._emptyAnimation}update(t){t*=this.timeScale;let e=this.tracks;for(let i=0,s=e.length;i<s;i++){let s=e[i];if(!s)continue;s.animationLast=s.nextAnimationLast,s.trackLast=s.nextTrackLast;let r=t*s.timeScale;if(s.delay>0){if(s.delay-=r,s.delay>0)continue;r=-s.delay,s.delay=0}let n=s.next;if(n){let e=s.trackLast-n.delay;if(e>=0){for(n.delay=0,n.trackTime+=0==s.timeScale?0:(e/s.timeScale+t)*n.timeScale,s.trackTime+=r,this.setCurrent(i,n,!0);n.mixingFrom;)n.mixTime+=t,n=n.mixingFrom;continue}}else if(s.trackLast>=s.trackEnd&&!s.mixingFrom){e[i]=null,this.queue.end(s),this.clearNext(s);continue}if(s.mixingFrom&&this.updateMixingFrom(s,t)){let t=s.mixingFrom;for(s.mixingFrom=null,t&&(t.mixingTo=null);t;)this.queue.end(t),t=t.mixingFrom}s.trackTime+=r}this.queue.drain()}updateMixingFrom(t,e){let i=t.mixingFrom;if(!i)return!0;let s=this.updateMixingFrom(i,e);return i.animationLast=i.nextAnimationLast,i.trackLast=i.nextTrackLast,t.mixTime>0&&t.mixTime>=t.mixDuration?(0!=i.totalAlpha&&0!=t.mixDuration||(t.mixingFrom=i.mixingFrom,i.mixingFrom&&(i.mixingFrom.mixingTo=t),t.interruptAlpha=i.interruptAlpha,this.queue.end(i)),s):(i.trackTime+=e*i.timeScale,t.mixTime+=e,!1)}apply(t){if(!t)throw new Error("skeleton cannot be null.");this.animationsChanged&&this._animationsChanged();let e=this.events,i=this.tracks,s=!1;for(let n=0,a=i.length;n<a;n++){let a=i[n];if(!a||a.delay>0)continue;s=!0;let o=0==n?1:a.mixBlend,h=a.alpha;a.mixingFrom?h*=this.applyMixingFrom(a,t,o):a.trackTime>=a.trackEnd&&!a.next&&(h=0);let l=a.animationLast,c=a.getAnimationTime(),u=c,d=e;a.reverse&&(u=a.animation.duration-u,d=null);let f=a.animation.timelines,p=f.length;if(0==n&&1==h||3==o)for(let e=0;e<p;e++){g.webkit602BugfixHelper(h,o);var r=f[e];r instanceof mt?this.applyAttachmentTimeline(r,t,u,o,!0):r.apply(t,l,u,d,h,o,0)}else{let e=a.timelineMode,i=a.shortestRotation,s=!i&&a.timelinesRotation.length!=p<<1;s&&(a.timelinesRotation.length=p<<1);for(let r=0;r<p;r++){let n=f[r],c=e[r]==Yt?o:0;!i&&n instanceof et?this.applyRotateTimeline(n,t,u,h,c,a.timelinesRotation,r<<1,s):n instanceof mt?this.applyAttachmentTimeline(n,t,u,o,!0):(g.webkit602BugfixHelper(h,o),n.apply(t,l,u,d,h,c,0))}}this.queueEvents(a,c),e.length=0,a.nextAnimationLast=c,a.nextTrackLast=a.trackTime}for(var n=this.unkeyedState+_t,a=t.slots,o=0,h=t.slots.length;o<h;o++){var l=a[o];if(l.attachmentState==n){var c=l.data.attachmentName;l.setAttachment(c?t.getAttachment(l.data.index,c):null)}}return this.unkeyedState+=2,this.queue.drain(),s}applyMixingFrom(t,e,i){let s=t.mixingFrom;s.mixingFrom&&this.applyMixingFrom(s,e,i);let r=0;0==t.mixDuration?(r=1,1==i&&(i=0)):(r=t.mixTime/t.mixDuration,r>1&&(r=1),1!=i&&(i=s.mixBlend));let n=r<s.attachmentThreshold,a=r<s.drawOrderThreshold,o=s.animation.timelines,h=o.length,l=s.alpha*t.interruptAlpha,c=l*(1-r),u=s.animationLast,d=s.getAnimationTime(),f=d,p=null;if(s.reverse?f=s.animation.duration-f:r<s.eventThreshold&&(p=this.events),3==i)for(let t=0;t<h;t++)o[t].apply(e,u,f,p,c,i,1);else{let t=s.timelineMode,r=s.timelineHoldMix,d=s.shortestRotation,m=!d&&s.timelinesRotation.length!=h<<1;m&&(s.timelinesRotation.length=h<<1),s.totalAlpha=0;for(let x=0;x<h;x++){let h,v=o[x],y=1,w=0;switch(t[x]){case Yt:if(!a&&v instanceof bt)continue;h=i,w=c;break;case Dt:h=0,w=c;break;case Xt:h=i,w=l;break;case Bt:h=0,w=l;break;default:h=0;let t=r[x];w=l*Math.max(0,1-t.mixTime/t.mixDuration)}s.totalAlpha+=w,!d&&v instanceof et?this.applyRotateTimeline(v,e,f,w,h,s.timelinesRotation,x<<1,m):v instanceof mt?this.applyAttachmentTimeline(v,e,f,h,n):(g.webkit602BugfixHelper(w,i),a&&v instanceof bt&&0==h&&(y=0),v.apply(e,u,f,p,w,h,y))}}return t.mixDuration>0&&this.queueEvents(s,d),this.events.length=0,s.nextAnimationLast=d,s.nextTrackLast=s.trackTime,r}applyAttachmentTimeline(t,e,i,s,r){var n=e.slots[t.slotIndex];n.bone.active&&(i<t.frames[0]?0!=s&&1!=s||this.setAttachment(e,n,n.data.attachmentName,r):this.setAttachment(e,n,t.attachmentNames[J.search1(t.frames,i)],r),n.attachmentState<=this.unkeyedState&&(n.attachmentState=this.unkeyedState+_t))}setAttachment(t,e,i,s){e.setAttachment(i?t.getAttachment(e.data.index,i):null),s&&(e.attachmentState=this.unkeyedState+Nt)}applyRotateTimeline(t,e,i,s,r,n,a,o){if(o&&(n[a]=0),1==s)return void t.apply(e,0,i,null,1,r,0);let h=e.bones[t.boneIndex];if(!h.active)return;let l=0,u=0;if(i<t.frames[0])switch(r){case 0:h.rotation=h.data.rotation;default:return;case 1:l=h.rotation,u=h.data.rotation}else l=0==r?h.data.rotation:h.rotation,u=h.data.rotation+t.getCurveValue(i);let d=0,f=u-l;if(f-=360*(16384-(16384.499999999996-f/360|0)),0==f)d=n[a];else{let t=0,e=0;o?(t=0,e=f):(t=n[a],e=n[a+1]);let i=f>0,s=t>=0;c.signum(e)!=c.signum(f)&&Math.abs(e)<=90&&(Math.abs(t)>180&&(t+=360*c.signum(t)),s=i),d=f+t-t%360,s!=i&&(d+=360*c.signum(t)),n[a]=d}n[a+1]=f,h.rotation=l+d*s}queueEvents(t,e){let i=t.animationStart,s=t.animationEnd,r=s-i,n=t.trackLast%r,a=this.events,o=0,h=a.length;for(;o<h;o++){let e=a[o];if(e.time<n)break;e.time>s||this.queue.event(t,e)}let l=!1;for(l=t.loop?0==r||n>t.trackTime%r:e>=s&&t.animationLast<s,l&&this.queue.complete(t);o<h;o++){let e=a[o];e.time<i||this.queue.event(t,e)}}clearTracks(){let t=this.queue.drainDisabled;this.queue.drainDisabled=!0;for(let t=0,e=this.tracks.length;t<e;t++)this.clearTrack(t);this.tracks.length=0,this.queue.drainDisabled=t,this.queue.drain()}clearTrack(t){if(t>=this.tracks.length)return;let e=this.tracks[t];if(!e)return;this.queue.end(e),this.clearNext(e);let i=e;for(;;){let t=i.mixingFrom;if(!t)break;this.queue.end(t),i.mixingFrom=null,i.mixingTo=null,i=t}this.tracks[e.trackIndex]=null,this.queue.drain()}setCurrent(t,e,i){let s=this.expandToIndex(t);this.tracks[t]=e,e.previous=null,s&&(i&&this.queue.interrupt(s),e.mixingFrom=s,s.mixingTo=e,e.mixTime=0,s.mixingFrom&&s.mixDuration>0&&(e.interruptAlpha*=Math.min(1,s.mixTime/s.mixDuration)),s.timelinesRotation.length=0),this.queue.start(e)}setAnimation(t,e,i=!1){let s=this.data.skeletonData.findAnimation(e);if(!s)throw new Error("Animation not found: "+e);return this.setAnimationWith(t,s,i)}setAnimationWith(t,e,i=!1){if(!e)throw new Error("animation cannot be null.");let s=!0,r=this.expandToIndex(t);r&&(-1==r.nextTrackLast?(this.tracks[t]=r.mixingFrom,this.queue.interrupt(r),this.queue.end(r),this.clearNext(r),r=r.mixingFrom,s=!1):this.clearNext(r));let n=this.trackEntry(t,e,i,r);return this.setCurrent(t,n,s),this.queue.drain(),n}addAnimation(t,e,i=!1,s=0){let r=this.data.skeletonData.findAnimation(e);if(!r)throw new Error("Animation not found: "+e);return this.addAnimationWith(t,r,i,s)}addAnimationWith(t,e,i=!1,s=0){if(!e)throw new Error("animation cannot be null.");let r=this.expandToIndex(t);if(r)for(;r.next;)r=r.next;let n=this.trackEntry(t,e,i,r);return r?(r.next=n,n.previous=r,s<=0&&(s+=r.getTrackComplete()-n.mixDuration)):(this.setCurrent(t,n,!0),this.queue.drain()),n.delay=s,n}setEmptyAnimation(t,e=0){let i=this.setAnimationWith(t,Rt.emptyAnimation(),!1);return i.mixDuration=e,i.trackEnd=e,i}addEmptyAnimation(t,e=0,i=0){let s=this.addAnimationWith(t,Rt.emptyAnimation(),!1,i);return i<=0&&(s.delay+=s.mixDuration-e),s.mixDuration=e,s.trackEnd=e,s}setEmptyAnimations(t=0){let e=this.queue.drainDisabled;this.queue.drainDisabled=!0;for(let e=0,i=this.tracks.length;e<i;e++){let i=this.tracks[e];i&&this.setEmptyAnimation(i.trackIndex,t)}this.queue.drainDisabled=e,this.queue.drain()}expandToIndex(t){return t<this.tracks.length?this.tracks[t]:(g.ensureArrayCapacity(this.tracks,t+1,null),this.tracks.length=t+1,null)}trackEntry(t,e,i,s){let r=this.trackEntryPool.obtain();return r.reset(),r.trackIndex=t,r.animation=e,r.loop=i,r.holdPrevious=!1,r.reverse=!1,r.shortestRotation=!1,r.eventThreshold=0,r.attachmentThreshold=0,r.drawOrderThreshold=0,r.animationStart=0,r.animationEnd=e.duration,r.animationLast=-1,r.nextAnimationLast=-1,r.delay=0,r.trackTime=0,r.trackLast=-1,r.nextTrackLast=-1,r.trackEnd=Number.MAX_VALUE,r.timeScale=1,r.alpha=1,r.mixTime=0,r.mixDuration=s?this.data.getMix(s.animation,e):0,r.interruptAlpha=1,r.totalAlpha=0,r.mixBlend=2,r}clearNext(t){let e=t.next;for(;e;)this.queue.dispose(e),e=e.next;t.next=null}_animationsChanged(){this.animationsChanged=!1,this.propertyIDs.clear();let t=this.tracks;for(let e=0,i=t.length;e<i;e++){let i=t[e];if(i){for(;i.mixingFrom;)i=i.mixingFrom;do{i.mixingTo&&3==i.mixBlend||this.computeHold(i),i=i.mixingTo}while(i)}}}computeHold(t){let e=t.mixingTo,i=t.animation.timelines,s=t.animation.timelines.length,r=t.timelineMode;r.length=s;let n=t.timelineHoldMix;n.length=0;let a=this.propertyIDs;if(e&&e.holdPrevious)for(let t=0;t<s;t++)r[t]=a.addAll(i[t].getPropertyIds())?Bt:Xt;else t:for(let o=0;o<s;o++){let s=i[o],h=s.getPropertyIds();if(a.addAll(h))if(!e||s instanceof mt||s instanceof bt||s instanceof yt||!e.animation.hasTimeline(h))r[o]=Dt;else{for(let i=e.mixingTo;i;i=i.mixingTo)if(!i.animation.hasTimeline(h)){if(t.mixDuration>0){r[o]=zt,n[o]=i;continue t}break}r[o]=Bt}else r[o]=Yt}}getCurrent(t){return t>=this.tracks.length?null:this.tracks[t]}addListener(t){if(!t)throw new Error("listener cannot be null.");this.listeners.push(t)}removeListener(t){let e=this.listeners.indexOf(t);e>=0&&this.listeners.splice(e,1)}clearListeners(){this.listeners.length=0}clearListenerNotifications(){this.queue.clear()}},kt=Rt;kt._emptyAnimation=new C("<empty>",[],0);var Ft=class{constructor(){this.animation=null,this.previous=null,this.next=null,this.mixingFrom=null,this.mixingTo=null,this.listener=null,this.trackIndex=0,this.loop=!1,this.holdPrevious=!1,this.reverse=!1,this.shortestRotation=!1,this.eventThreshold=0,this.attachmentThreshold=0,this.drawOrderThreshold=0,this.animationStart=0,this.animationEnd=0,this.animationLast=0,this.nextAnimationLast=0,this.delay=0,this.trackTime=0,this.trackLast=0,this.nextTrackLast=0,this.trackEnd=0,this.timeScale=0,this.alpha=0,this.mixTime=0,this.mixDuration=0,this.interruptAlpha=0,this.totalAlpha=0,this.mixBlend=2,this.timelineMode=new Array,this.timelineHoldMix=new Array,this.timelinesRotation=new Array}reset(){this.next=null,this.previous=null,this.mixingFrom=null,this.mixingTo=null,this.animation=null,this.listener=null,this.timelineMode.length=0,this.timelineHoldMix.length=0,this.timelinesRotation.length=0}getAnimationTime(){if(this.loop){let t=this.animationEnd-this.animationStart;return 0==t?this.animationStart:this.trackTime%t+this.animationStart}return Math.min(this.trackTime+this.animationStart,this.animationEnd)}setAnimationLast(t){this.animationLast=t,this.nextAnimationLast=t}isComplete(){return this.trackTime>=this.animationEnd-this.animationStart}resetRotationDirections(){this.timelinesRotation.length=0}getTrackComplete(){let t=this.animationEnd-this.animationStart;if(0!=t){if(this.loop)return t*(1+(this.trackTime/t|0));if(this.trackTime<t)return t}return this.trackTime}},Lt=class{constructor(t){this.objects=[],this.drainDisabled=!1,this.animState=t}start(t){this.objects.push(Pt.start),this.objects.push(t),this.animState.animationsChanged=!0}interrupt(t){this.objects.push(Pt.interrupt),this.objects.push(t)}end(t){this.objects.push(Pt.end),this.objects.push(t),this.animState.animationsChanged=!0}dispose(t){this.objects.push(Pt.dispose),this.objects.push(t)}complete(t){this.objects.push(Pt.complete),this.objects.push(t)}event(t,e){this.objects.push(Pt.event),this.objects.push(t),this.objects.push(e)}drain(){if(this.drainDisabled)return;this.drainDisabled=!0;let t=this.objects,e=this.animState.listeners;for(let i=0;i<t.length;i+=2){let s=t[i],r=t[i+1];switch(s){case Pt.start:r.listener&&r.listener.start&&r.listener.start(r);for(let t=0;t<e.length;t++){let i=e[t];i.start&&i.start(r)}break;case Pt.interrupt:r.listener&&r.listener.interrupt&&r.listener.interrupt(r);for(let t=0;t<e.length;t++){let i=e[t];i.interrupt&&i.interrupt(r)}break;case Pt.end:r.listener&&r.listener.end&&r.listener.end(r);for(let t=0;t<e.length;t++){let i=e[t];i.end&&i.end(r)}case Pt.dispose:r.listener&&r.listener.dispose&&r.listener.dispose(r);for(let t=0;t<e.length;t++){let i=e[t];i.dispose&&i.dispose(r)}this.animState.trackEntryPool.free(r);break;case Pt.complete:r.listener&&r.listener.complete&&r.listener.complete(r);for(let t=0;t<e.length;t++){let i=e[t];i.complete&&i.complete(r)}break;case Pt.event:let s=t[2+i++];r.listener&&r.listener.event&&r.listener.event(r,s);for(let t=0;t<e.length;t++){let i=e[t];i.event&&i.event(r,s)}}}this.clear(),this.drainDisabled=!1}clear(){this.objects.length=0}},Pt=(t=>(t[t.start=0]="start",t[t.interrupt=1]="interrupt",t[t.end=2]="end",t[t.dispose=3]="dispose",t[t.complete=4]="complete",t[t.event=5]="event",t))(Pt||{}),Ot=class{start(t){}interrupt(t){}end(t){}dispose(t){}complete(t){}event(t,e){}},Yt=0,Dt=1,Xt=2,Bt=3,zt=4,_t=1,Nt=2,Vt=class{constructor(t){if(this.animationToMixTime={},this.defaultMix=0,!t)throw new Error("skeletonData cannot be null.");this.skeletonData=t}setMix(t,e,i){let s=this.skeletonData.findAnimation(t);if(!s)throw new Error("Animation not found: "+t);let r=this.skeletonData.findAnimation(e);if(!r)throw new Error("Animation not found: "+e);this.setMixWith(s,r,i)}setMixWith(t,e,i){if(!t)throw new Error("from cannot be null.");if(!e)throw new Error("to cannot be null.");let s=t.name+"."+e.name;this.animationToMixTime[s]=i}getMix(t,e){let i=t.name+"."+e.name,s=this.animationToMixTime[i];return void 0===s?this.defaultMix:s}},Ut=class extends M{constructor(t){super(t),this.color=new h(1,1,1,1)}copy(){let t=new Ut(this.name);return this.copyTo(t),t.color.setFromColor(this.color),t}},Wt=class extends M{constructor(t){super(t),this.endSlot=null,this.color=new h(.2275,.2275,.8078,1)}copy(){let t=new Wt(this.name);return this.copyTo(t),t.endSlot=this.endSlot,t.color.setFromColor(this.color),t}},qt=class{constructor(t){this._image=t}getImage(){return this._image}},Gt=(t=>(t[t.Nearest=9728]="Nearest",t[t.Linear=9729]="Linear",t[t.MipMap=9987]="MipMap",t[t.MipMapNearestNearest=9984]="MipMapNearestNearest",t[t.MipMapLinearNearest=9985]="MipMapLinearNearest",t[t.MipMapNearestLinear=9986]="MipMapNearestLinear",t[t.MipMapLinearLinear=9987]="MipMapLinearLinear",t))(Gt||{}),jt=(t=>(t[t.MirroredRepeat=33648]="MirroredRepeat",t[t.ClampToEdge=33071]="ClampToEdge",t[t.Repeat=10497]="Repeat",t))(jt||{}),Ht=class{constructor(){this.u=0,this.v=0,this.u2=0,this.v2=0,this.width=0,this.height=0,this.degrees=0,this.offsetX=0,this.offsetY=0,this.originalWidth=0,this.originalHeight=0}},Zt=class extends qt{setFilters(t,e){}setWraps(t,e){}dispose(){}},Kt=class{constructor(t){this.pages=new Array,this.regions=new Array;let e=new Jt(t),i=new Array(4),s={size:t=>{t.width=parseInt(i[1]),t.height=parseInt(i[2])},format:()=>{},filter:t=>{t.minFilter=g.enumValue(Gt,i[1]),t.magFilter=g.enumValue(Gt,i[2])},repeat:t=>{-1!=i[1].indexOf("x")&&(t.uWrap=10497),-1!=i[1].indexOf("y")&&(t.vWrap=10497)},pma:t=>{t.pma="true"==i[1]}};var r={xy:t=>{t.x=parseInt(i[1]),t.y=parseInt(i[2])},size:t=>{t.width=parseInt(i[1]),t.height=parseInt(i[2])},bounds:t=>{t.x=parseInt(i[1]),t.y=parseInt(i[2]),t.width=parseInt(i[3]),t.height=parseInt(i[4])},offset:t=>{t.offsetX=parseInt(i[1]),t.offsetY=parseInt(i[2])},orig:t=>{t.originalWidth=parseInt(i[1]),t.originalHeight=parseInt(i[2])},offsets:t=>{t.offsetX=parseInt(i[1]),t.offsetY=parseInt(i[2]),t.originalWidth=parseInt(i[3]),t.originalHeight=parseInt(i[4])},rotate:t=>{let e=i[1];"true"==e?t.degrees=90:"false"!=e&&(t.degrees=parseInt(e))},index:t=>{t.index=parseInt(i[1])}};let n=e.readLine();for(;n&&0==n.trim().length;)n=e.readLine();for(;n&&0!=n.trim().length&&0!=e.readEntry(i,n);)n=e.readLine();let a=null,o=null,h=null;for(;null!==n;)if(0==n.trim().length)a=null,n=e.readLine();else if(a){let t=new $t(a,n);for(;;){let s=e.readEntry(i,n=e.readLine());if(0==s)break;let a=r[i[0]];if(a)a(t);else{o||(o=[]),h||(h=[]),o.push(i[0]);let t=[];for(let e=0;e<s;e++)t.push(parseInt(i[e+1]));h.push(t)}}0==t.originalWidth&&0==t.originalHeight&&(t.originalWidth=t.width,t.originalHeight=t.height),o&&o.length>0&&h&&h.length>0&&(t.names=o,t.values=h,o=null,h=null),t.u=t.x/a.width,t.v=t.y/a.height,90==t.degrees?(t.u2=(t.x+t.height)/a.width,t.v2=(t.y+t.width)/a.height):(t.u2=(t.x+t.width)/a.width,t.v2=(t.y+t.height)/a.height),this.regions.push(t)}else{for(a=new Qt(n.trim());0!=e.readEntry(i,n=e.readLine());){let t=s[i[0]];t&&t(a)}this.pages.push(a)}}findRegion(t){for(let e=0;e<this.regions.length;e++)if(this.regions[e].name==t)return this.regions[e];return null}setTextures(t,e=""){for(let i of this.pages)i.setTexture(t.get(e+i.name))}dispose(){var t;for(let e=0;e<this.pages.length;e++)null==(t=this.pages[e].texture)||t.dispose()}},Jt=class{constructor(t){this.index=0,this.lines=t.split(/\r\n|\r|\n/)}readLine(){return this.index>=this.lines.length?null:this.lines[this.index++]}readEntry(t,e){if(!e)return 0;if(0==(e=e.trim()).length)return 0;let i=e.indexOf(":");if(-1==i)return 0;t[0]=e.substr(0,i).trim();for(let s=1,r=i+1;;s++){let i=e.indexOf(",",r);if(-1==i)return t[s]=e.substr(r).trim(),s;if(t[s]=e.substr(r,i-r).trim(),r=i+1,4==s)return 4}}},Qt=class{constructor(t){this.minFilter=9728,this.magFilter=9728,this.uWrap=33071,this.vWrap=33071,this.texture=null,this.width=0,this.height=0,this.pma=!1,this.name=t}setTexture(t){this.texture=t,t.setFilters(this.minFilter,this.magFilter),t.setWraps(this.uWrap,this.vWrap)}},$t=class extends Ht{constructor(t,e){super(),this.x=0,this.y=0,this.offsetX=0,this.offsetY=0,this.originalWidth=0,this.originalHeight=0,this.index=0,this.degrees=0,this.names=null,this.values=null,this.page=t,this.name=e}},te=class extends M{constructor(t,e){super(t),this.region=null,this.regionUVs=[],this.uvs=[],this.triangles=[],this.color=new h(1,1,1,1),this.width=0,this.height=0,this.hullLength=0,this.edges=[],this.parentMesh=null,this.sequence=null,this.tempColor=new h(0,0,0,0),this.path=e}updateRegion(){if(!this.region)throw new Error("Region not set.");let t=this.regionUVs;this.uvs&&this.uvs.length==t.length||(this.uvs=g.newFloatArray(t.length));let e=this.uvs,i=this.uvs.length,s=this.region.u,r=this.region.v,n=0,a=0;if(this.region instanceof $t){let o=this.region,h=o.page.texture.getImage(),l=h.width,c=h.height;switch(o.degrees){case 90:s-=(o.originalHeight-o.offsetY-o.height)/l,r-=(o.originalWidth-o.offsetX-o.width)/c,n=o.originalHeight/l,a=o.originalWidth/c;for(let o=0;o<i;o+=2)e[o]=s+t[o+1]*n,e[o+1]=r+(1-t[o])*a;return;case 180:s-=(o.originalWidth-o.offsetX-o.width)/l,r-=o.offsetY/c,n=o.originalWidth/l,a=o.originalHeight/c;for(let o=0;o<i;o+=2)e[o]=s+(1-t[o])*n,e[o+1]=r+(1-t[o+1])*a;return;case 270:s-=o.offsetY/l,r-=o.offsetX/c,n=o.originalHeight/l,a=o.originalWidth/c;for(let o=0;o<i;o+=2)e[o]=s+(1-t[o+1])*n,e[o+1]=r+t[o]*a;return}s-=o.offsetX/l,r-=(o.originalHeight-o.offsetY-o.height)/c,n=o.originalWidth/l,a=o.originalHeight/c}else this.region?(n=this.region.u2-s,a=this.region.v2-r):(s=r=0,n=a=1);for(let o=0;o<i;o+=2)e[o]=s+t[o]*n,e[o+1]=r+t[o+1]*a}getParentMesh(){return this.parentMesh}setParentMesh(t){this.parentMesh=t,t&&(this.bones=t.bones,this.vertices=t.vertices,this.worldVerticesLength=t.worldVerticesLength,this.regionUVs=t.regionUVs,this.triangles=t.triangles,this.hullLength=t.hullLength,this.worldVerticesLength=t.worldVerticesLength)}copy(){if(this.parentMesh)return this.newLinkedMesh();let t=new te(this.name,this.path);return t.region=this.region,t.color.setFromColor(this.color),this.copyTo(t),t.regionUVs=new Array(this.regionUVs.length),g.arrayCopy(this.regionUVs,0,t.regionUVs,0,this.regionUVs.length),t.uvs=new Array(this.uvs.length),g.arrayCopy(this.uvs,0,t.uvs,0,this.uvs.length),t.triangles=new Array(this.triangles.length),g.arrayCopy(this.triangles,0,t.triangles,0,this.triangles.length),t.hullLength=this.hullLength,t.sequence=null!=this.sequence?this.sequence.copy():null,this.edges&&(t.edges=new Array(this.edges.length),g.arrayCopy(this.edges,0,t.edges,0,this.edges.length)),t.width=this.width,t.height=this.height,t}computeWorldVertices(t,e,i,s,r,n){null!=this.sequence&&this.sequence.apply(t,this),super.computeWorldVertices(t,e,i,s,r,n)}newLinkedMesh(){let t=new te(this.name,this.path);return t.region=this.region,t.color.setFromColor(this.color),t.timelineAttachment=this.timelineAttachment,t.setParentMesh(this.parentMesh?this.parentMesh:this),null!=t.region&&t.updateRegion(),t}},ee=class extends M{constructor(t){super(t),this.lengths=[],this.closed=!1,this.constantSpeed=!1,this.color=new h(1,1,1,1)}copy(){let t=new ee(this.name);return this.copyTo(t),t.lengths=new Array(this.lengths.length),g.arrayCopy(this.lengths,0,t.lengths,0,this.lengths.length),t.closed=closed,t.constantSpeed=this.constantSpeed,t.color.setFromColor(this.color),t}},ie=class extends M{constructor(t){super(t),this.x=0,this.y=0,this.rotation=0,this.color=new h(.38,.94,0,1)}computeWorldPosition(t,e){return e.x=this.x*t.a+this.y*t.b+t.worldX,e.y=this.x*t.c+this.y*t.d+t.worldY,e}computeWorldRotation(t){let e=c.cosDeg(this.rotation),i=c.sinDeg(this.rotation),s=e*t.a+i*t.b,r=e*t.c+i*t.d;return Math.atan2(r,s)*c.radDeg}copy(){let t=new ie(this.name);return t.x=this.x,t.y=this.y,t.rotation=this.rotation,t.color.setFromColor(this.color),t}},se=class extends b{constructor(t,e){super(t),this.x=0,this.y=0,this.scaleX=1,this.scaleY=1,this.rotation=0,this.width=0,this.height=0,this.color=new h(1,1,1,1),this.rendererObject=null,this.region=null,this.sequence=null,this.offset=g.newFloatArray(8),this.uvs=g.newFloatArray(8),this.tempColor=new h(1,1,1,1),this.path=e}updateRegion(){if(!this.region)throw new Error("Region not set.");let t=this.region,e=this.uvs;if(null==t)return e[0]=0,e[1]=0,e[2]=0,e[3]=1,e[4]=1,e[5]=1,e[6]=1,void(e[7]=0);let i=this.width/this.region.originalWidth*this.scaleX,s=this.height/this.region.originalHeight*this.scaleY,r=-this.width/2*this.scaleX+this.region.offsetX*i,n=-this.height/2*this.scaleY+this.region.offsetY*s,a=r+this.region.width*i,o=n+this.region.height*s,h=this.rotation*Math.PI/180,l=Math.cos(h),c=Math.sin(h),u=this.x,d=this.y,f=r*l+u,p=r*c,g=n*l+d,m=n*c,x=a*l+u,v=a*c,y=o*l+d,w=o*c,b=this.offset;b[0]=f-m,b[1]=g+p,b[2]=f-w,b[3]=y+p,b[4]=x-w,b[5]=y+v,b[6]=x-m,b[7]=g+v,90==t.degrees?(e[0]=t.u2,e[1]=t.v2,e[2]=t.u,e[3]=t.v2,e[4]=t.u,e[5]=t.v,e[6]=t.u2,e[7]=t.v):(e[0]=t.u,e[1]=t.v2,e[2]=t.u,e[3]=t.v,e[4]=t.u2,e[5]=t.v,e[6]=t.u2,e[7]=t.v2)}computeWorldVertices(t,e,i,s){null!=this.sequence&&this.sequence.apply(t,this);let r=t.bone,n=this.offset,a=r.worldX,o=r.worldY,h=r.a,l=r.b,c=r.c,u=r.d,d=0,f=0;d=n[0],f=n[1],e[i]=d*h+f*l+a,e[i+1]=d*c+f*u+o,i+=s,d=n[2],f=n[3],e[i]=d*h+f*l+a,e[i+1]=d*c+f*u+o,i+=s,d=n[4],f=n[5],e[i]=d*h+f*l+a,e[i+1]=d*c+f*u+o,i+=s,d=n[6],f=n[7],e[i]=d*h+f*l+a,e[i+1]=d*c+f*u+o}copy(){let t=new se(this.name,this.path);return t.region=this.region,t.rendererObject=this.rendererObject,t.x=this.x,t.y=this.y,t.scaleX=this.scaleX,t.scaleY=this.scaleY,t.rotation=this.rotation,t.width=this.width,t.height=this.height,g.arrayCopy(this.uvs,0,t.uvs,0,8),g.arrayCopy(this.offset,0,t.offset,0,8),t.color.setFromColor(this.color),t.sequence=null!=this.sequence?this.sequence.copy():null,t}},re=se;re.X1=0,re.Y1=1,re.C1R=2,re.C1G=3,re.C1B=4,re.C1A=5,re.U1=6,re.V1=7,re.X2=8,re.Y2=9,re.C2R=10,re.C2G=11,re.C2B=12,re.C2A=13,re.U2=14,re.V2=15,re.X3=16,re.Y3=17,re.C3R=18,re.C3G=19,re.C3B=20,re.C3A=21,re.U3=22,re.V3=23,re.X4=24,re.Y4=25,re.C4R=26,re.C4G=27,re.C4B=28,re.C4A=29,re.U4=30,re.V4=31;var ne=class{constructor(t){this.atlas=t}loadSequence(t,e,i){let s=i.regions;for(let r=0,n=s.length;r<n;r++){let n=i.getPath(e,r),a=this.atlas.findRegion(n);if(null==a)throw new Error("Region not found in atlas: "+n+" (sequence: "+t+")");s[r]=a,s[r].renderObject=s[r]}}newRegionAttachment(t,e,i,s){let r=new re(e,i);if(null!=s)this.loadSequence(e,i,s);else{let t=this.atlas.findRegion(i);if(!t)throw new Error("Region not found in atlas: "+i+" (region attachment: "+e+")");t.renderObject=t,r.region=t}return r}newMeshAttachment(t,e,i,s){let r=new te(e,i);if(null!=s)this.loadSequence(e,i,s);else{let t=this.atlas.findRegion(i);if(!t)throw new Error("Region not found in atlas: "+i+" (mesh attachment: "+e+")");t.renderObject=t,r.region=t}return r}newBoundingBoxAttachment(t,e){return new Ut(e)}newPathAttachment(t,e){return new ee(e)}newPointAttachment(t,e){return new ie(e)}newClippingAttachment(t,e){return new Wt(e)}},ae=class{constructor(t,e,i){if(this.index=0,this.parent=null,this.length=0,this.x=0,this.y=0,this.rotation=0,this.scaleX=1,this.scaleY=1,this.shearX=0,this.shearY=0,this.transformMode=oe.Normal,this.skinRequired=!1,this.color=new h,t<0)throw new Error("index must be >= 0.");if(!e)throw new Error("name cannot be null.");this.index=t,this.name=e,this.parent=i}},oe=(t=>(t[t.Normal=0]="Normal",t[t.OnlyTranslation=1]="OnlyTranslation",t[t.NoRotationOrReflection=2]="NoRotationOrReflection",t[t.NoScale=3]="NoScale",t[t.NoScaleOrReflection=4]="NoScaleOrReflection",t))(oe||{}),he=class{constructor(t,e,i){if(this.parent=null,this.children=new Array,this.x=0,this.y=0,this.rotation=0,this.scaleX=0,this.scaleY=0,this.shearX=0,this.shearY=0,this.ax=0,this.ay=0,this.arotation=0,this.ascaleX=0,this.ascaleY=0,this.ashearX=0,this.ashearY=0,this.a=0,this.b=0,this.c=0,this.d=0,this.worldY=0,this.worldX=0,this.sorted=!1,this.active=!1,!t)throw new Error("data cannot be null.");if(!e)throw new Error("skeleton cannot be null.");this.data=t,this.skeleton=e,this.parent=i,this.setToSetupPose()}isActive(){return this.active}update(){this.updateWorldTransformWith(this.ax,this.ay,this.arotation,this.ascaleX,this.ascaleY,this.ashearX,this.ashearY)}updateWorldTransform(){this.updateWorldTransformWith(this.x,this.y,this.rotation,this.scaleX,this.scaleY,this.shearX,this.shearY)}updateWorldTransformWith(t,e,i,s,r,n,a){this.ax=t,this.ay=e,this.arotation=i,this.ascaleX=s,this.ascaleY=r,this.ashearX=n,this.ashearY=a;let o=this.parent;if(!o){let o=this.skeleton,h=i+90+a,l=o.scaleX,u=o.scaleY;return this.a=c.cosDeg(i+n)*s*l,this.b=c.cosDeg(h)*r*l,this.c=c.sinDeg(i+n)*s*u,this.d=c.sinDeg(h)*r*u,this.worldX=t*l+o.x,void(this.worldY=e*u+o.y)}let h=o.a,l=o.b,u=o.c,d=o.d;switch(this.worldX=h*t+l*e+o.worldX,this.worldY=u*t+d*e+o.worldY,this.data.transformMode){case 0:{let t=i+90+a,e=c.cosDeg(i+n)*s,o=c.cosDeg(t)*r,f=c.sinDeg(i+n)*s,p=c.sinDeg(t)*r;return this.a=h*e+l*f,this.b=h*o+l*p,this.c=u*e+d*f,void(this.d=u*o+d*p)}case 1:{let t=i+90+a;this.a=c.cosDeg(i+n)*s,this.b=c.cosDeg(t)*r,this.c=c.sinDeg(i+n)*s,this.d=c.sinDeg(t)*r;break}case 2:{let t=h*h+u*u,e=0;t>1e-4?(t=Math.abs(h*d-l*u)/t,h/=this.skeleton.scaleX,u/=this.skeleton.scaleY,l=u*t,d=h*t,e=Math.atan2(u,h)*c.radDeg):(h=0,u=0,e=90-Math.atan2(d,l)*c.radDeg);let o=i+n-e,f=i+a-e+90,p=c.cosDeg(o)*s,g=c.cosDeg(f)*r,m=c.sinDeg(o)*s,x=c.sinDeg(f)*r;this.a=h*p-l*m,this.b=h*g-l*x,this.c=u*p+d*m,this.d=u*g+d*x;break}case 3:case 4:{let t=c.cosDeg(i),e=c.sinDeg(i),o=(h*t+l*e)/this.skeleton.scaleX,f=(u*t+d*e)/this.skeleton.scaleY,p=Math.sqrt(o*o+f*f);p>1e-5&&(p=1/p),o*=p,f*=p,p=Math.sqrt(o*o+f*f),3==this.data.transformMode&&h*d-l*u<0!=(this.skeleton.scaleX<0!=this.skeleton.scaleY<0)&&(p=-p);let g=Math.PI/2+Math.atan2(f,o),m=Math.cos(g)*p,x=Math.sin(g)*p,v=c.cosDeg(n)*s,y=c.cosDeg(90+a)*r,w=c.sinDeg(n)*s,b=c.sinDeg(90+a)*r;this.a=o*v+m*w,this.b=o*y+m*b,this.c=f*v+x*w,this.d=f*y+x*b;break}}this.a*=this.skeleton.scaleX,this.b*=this.skeleton.scaleX,this.c*=this.skeleton.scaleY,this.d*=this.skeleton.scaleY}setToSetupPose(){let t=this.data;this.x=t.x,this.y=t.y,this.rotation=t.rotation,this.scaleX=t.scaleX,this.scaleY=t.scaleY,this.shearX=t.shearX,this.shearY=t.shearY}getWorldRotationX(){return Math.atan2(this.c,this.a)*c.radDeg}getWorldRotationY(){return Math.atan2(this.d,this.b)*c.radDeg}getWorldScaleX(){return Math.sqrt(this.a*this.a+this.c*this.c)}getWorldScaleY(){return Math.sqrt(this.b*this.b+this.d*this.d)}updateAppliedTransform(){let t=this.parent;if(!t)return this.ax=this.worldX-this.skeleton.x,this.ay=this.worldY-this.skeleton.y,this.arotation=Math.atan2(this.c,this.a)*c.radDeg,this.ascaleX=Math.sqrt(this.a*this.a+this.c*this.c),this.ascaleY=Math.sqrt(this.b*this.b+this.d*this.d),this.ashearX=0,void(this.ashearY=Math.atan2(this.a*this.b+this.c*this.d,this.a*this.d-this.b*this.c)*c.radDeg);let e=t.a,i=t.b,s=t.c,r=t.d,n=1/(e*r-i*s),a=this.worldX-t.worldX,o=this.worldY-t.worldY;this.ax=a*r*n-o*i*n,this.ay=o*e*n-a*s*n;let h=n*r,l=n*e,u=n*i,d=n*s,f=h*this.a-u*this.c,p=h*this.b-u*this.d,g=l*this.c-d*this.a,m=l*this.d-d*this.b;if(this.ashearX=0,this.ascaleX=Math.sqrt(f*f+g*g),this.ascaleX>1e-4){let t=f*m-p*g;this.ascaleY=t/this.ascaleX,this.ashearY=Math.atan2(f*p+g*m,t)*c.radDeg,this.arotation=Math.atan2(g,f)*c.radDeg}else this.ascaleX=0,this.ascaleY=Math.sqrt(p*p+m*m),this.ashearY=0,this.arotation=90-Math.atan2(m,p)*c.radDeg}worldToLocal(t){let e=1/(this.a*this.d-this.b*this.c),i=t.x-this.worldX,s=t.y-this.worldY;return t.x=i*this.d*e-s*this.b*e,t.y=s*this.a*e-i*this.c*e,t}localToWorld(t){let e=t.x,i=t.y;return t.x=e*this.a+i*this.b+this.worldX,t.y=e*this.c+i*this.d+this.worldY,t}worldToLocalRotation(t){let e=c.sinDeg(t),i=c.cosDeg(t);return Math.atan2(this.a*e-this.c*i,this.d*i-this.b*e)*c.radDeg+this.rotation-this.shearX}localToWorldRotation(t){t-=this.rotation-this.shearX;let e=c.sinDeg(t),i=c.cosDeg(t);return Math.atan2(i*this.c+e*this.d,i*this.a+e*this.b)*c.radDeg}rotateWorld(t){let e=this.a,i=this.b,s=this.c,r=this.d,n=c.cosDeg(t),a=c.sinDeg(t);this.a=n*e-a*s,this.b=n*i-a*r,this.c=a*e+n*s,this.d=a*i+n*r}},le=class{constructor(t,e,i){this.name=t,this.order=e,this.skinRequired=i}},ce=class{constructor(t,e="",i=new ue){this.pathPrefix="",this.assets={},this.errors={},this.toLoad=0,this.loaded=0,this.textureLoader=t,this.pathPrefix=e,this.downloader=i}start(t){return this.toLoad++,this.pathPrefix+t}success(t,e,i){this.toLoad--,this.loaded++,this.assets[e]=i,t&&t(e,i)}error(t,e,i){this.toLoad--,this.loaded++,this.errors[e]=i,t&&t(e,i)}loadAll(){return new Promise(((t,e)=>{let i=()=>{this.isLoadingComplete()?this.hasErrors()?e(this.errors):t(this):requestAnimationFrame(i)};requestAnimationFrame(i)}))}setRawDataURI(t,e){this.downloader.rawDataUris[this.pathPrefix+t]=e}loadBinary(t,e=(()=>{}),i=(()=>{})){t=this.start(t),this.downloader.downloadBinary(t,(i=>{this.success(e,t,i)}),((e,s)=>{this.error(i,t,`Couldn't load binary ${t}: status ${e}, ${s}`)}))}loadText(t,e=(()=>{}),i=(()=>{})){t=this.start(t),this.downloader.downloadText(t,(i=>{this.success(e,t,i)}),((e,s)=>{this.error(i,t,`Couldn't load text ${t}: status ${e}, ${s}`)}))}loadJson(t,e=(()=>{}),i=(()=>{})){t=this.start(t),this.downloader.downloadJson(t,(i=>{this.success(e,t,i)}),((e,s)=>{this.error(i,t,`Couldn't load JSON ${t}: status ${e}, ${s}`)}))}loadTexture(t,e=(()=>{}),i=(()=>{})){if(t=this.start(t),!!("undefined"==typeof window||"undefined"==typeof navigator||!window.document))fetch(t,{mode:"cors"}).then((e=>e.ok?e.blob():(this.error(i,t,`Couldn't load image: ${t}`),null))).then((t=>t?createImageBitmap(t,{premultiplyAlpha:"none",colorSpaceConversion:"none"}):null)).then((i=>{i&&this.success(e,t,this.textureLoader(i))}));else{let s=new Image;s.crossOrigin="anonymous",s.onload=()=>{this.success(e,t,this.textureLoader(s))},s.onerror=()=>{this.error(i,t,`Couldn't load image: ${t}`)},this.downloader.rawDataUris[t]&&(t=this.downloader.rawDataUris[t]),s.src=t}}loadTextureAtlas(t,e=(()=>{}),i=(()=>{}),s){let r=t.lastIndexOf("/"),n=r>=0?t.substring(0,r+1):"";t=this.start(t),this.downloader.downloadText(t,(r=>{try{let a=new Kt(r),o=a.pages.length,h=!1;for(let r of a.pages)this.loadTexture(s?s[r.name]:n+r.name,((i,s)=>{h||(r.setTexture(s),0==--o&&this.success(e,t,a))}),((e,s)=>{h||this.error(i,t,`Couldn't load texture atlas ${t} page image: ${e}`),h=!0}))}catch(e){this.error(i,t,`Couldn't parse texture atlas ${t}: ${e.message}`)}}),((e,s)=>{this.error(i,t,`Couldn't load texture atlas ${t}: status ${e}, ${s}`)}))}get(t){return this.assets[this.pathPrefix+t]}require(t){t=this.pathPrefix+t;let e=this.assets[t];if(e)return e;let i=this.errors[t];throw Error("Asset not found: "+t+(i?"\n"+i:""))}remove(t){t=this.pathPrefix+t;let e=this.assets[t];return e.dispose&&e.dispose(),delete this.assets[t],e}removeAll(){for(let t in this.assets){let e=this.assets[t];e.dispose&&e.dispose()}this.assets={}}isLoadingComplete(){return 0==this.toLoad}getToLoad(){return this.toLoad}getLoaded(){return this.loaded}dispose(){this.removeAll()}hasErrors(){return Object.keys(this.errors).length>0}getErrors(){return this.errors}},ue=class{constructor(){this.callbacks={},this.rawDataUris={}}dataUriToString(t){if(!t.startsWith("data:"))throw new Error("Not a data URI.");let e=t.indexOf("base64,");return-1!=e?(e+=7,atob(t.substr(e))):t.substr(t.indexOf(",")+1)}base64ToUint8Array(t){for(var e=window.atob(t),i=e.length,s=new Uint8Array(i),r=0;r<i;r++)s[r]=e.charCodeAt(r);return s}dataUriToUint8Array(t){if(!t.startsWith("data:"))throw new Error("Not a data URI.");let e=t.indexOf("base64,");if(-1==e)throw new Error("Not a binary data URI.");return e+=7,this.base64ToUint8Array(t.substr(e))}downloadText(t,e,i){if(this.start(t,e,i))return;if(this.rawDataUris[t]){try{let e=this.rawDataUris[t];this.finish(t,200,this.dataUriToString(e))}catch(e){this.finish(t,400,JSON.stringify(e))}return}let s=new XMLHttpRequest;s.overrideMimeType("text/html"),s.open("GET",t,!0);let r=()=>{this.finish(t,s.status,s.responseText)};s.onload=r,s.onerror=r,s.send()}downloadJson(t,e,i){this.downloadText(t,(t=>{e(JSON.parse(t))}),i)}downloadBinary(t,e,i){if(this.start(t,e,i))return;if(this.rawDataUris[t]){try{let e=this.rawDataUris[t];this.finish(t,200,this.dataUriToUint8Array(e))}catch(e){this.finish(t,400,JSON.stringify(e))}return}let s=new XMLHttpRequest;s.open("GET",t,!0),s.responseType="arraybuffer";let r=()=>{this.finish(t,s.status,s.response)};s.onload=()=>{200==s.status||0==s.status?this.finish(t,200,new Uint8Array(s.response)):r()},s.onerror=r,s.send()}start(t,e,i){let s=this.callbacks[t];try{if(s)return!0;this.callbacks[t]=s=[]}finally{s.push(e,i)}}finish(t,e,i){let s=this.callbacks[t];delete this.callbacks[t];let r=200==e||0==e?[i]:[e,i];for(let t=r.length-1,e=s.length;t<e;t+=2)s[t].apply(null,r)}},de=class{constructor(t,e){if(this.intValue=0,this.floatValue=0,this.stringValue=null,this.time=0,this.volume=0,this.balance=0,!e)throw new Error("data cannot be null.");this.time=t,this.data=e}},fe=class{constructor(t){this.intValue=0,this.floatValue=0,this.stringValue=null,this.audioPath=null,this.volume=0,this.balance=0,this.name=t}},pe=class{constructor(t,e){if(this.bendDirection=0,this.compress=!1,this.stretch=!1,this.mix=1,this.softness=0,this.active=!1,!t)throw new Error("data cannot be null.");if(!e)throw new Error("skeleton cannot be null.");this.data=t,this.mix=t.mix,this.softness=t.softness,this.bendDirection=t.bendDirection,this.compress=t.compress,this.stretch=t.stretch,this.bones=new Array;for(let i=0;i<t.bones.length;i++){let s=e.findBone(t.bones[i].name);if(!s)throw new Error(`Couldn't find bone ${t.bones[i].name}`);this.bones.push(s)}let i=e.findBone(t.target.name);if(!i)throw new Error(`Couldn't find bone ${t.target.name}`);this.target=i}isActive(){return this.active}update(){if(0==this.mix)return;let t=this.target,e=this.bones;switch(e.length){case 1:this.apply1(e[0],t.worldX,t.worldY,this.compress,this.stretch,this.data.uniform,this.mix);break;case 2:this.apply2(e[0],e[1],t.worldX,t.worldY,this.bendDirection,this.stretch,this.data.uniform,this.softness,this.mix)}}apply1(t,e,i,s,r,n,a){let o=t.parent;if(!o)throw new Error("IK bone must have parent.");let h=o.a,l=o.b,u=o.c,d=o.d,f=-t.ashearX-t.arotation,p=0,g=0;switch(t.data.transformMode){case 1:p=e-t.worldX,g=i-t.worldY;break;case 2:let s=Math.abs(h*d-l*u)/(h*h+u*u),r=h/t.skeleton.scaleX,n=u/t.skeleton.scaleY;l=-n*s*t.skeleton.scaleX,d=r*s*t.skeleton.scaleY,f+=Math.atan2(n,r)*c.radDeg;default:let a=e-o.worldX,m=i-o.worldY,x=h*d-l*u;p=(a*d-m*l)/x-t.ax,g=(m*h-a*u)/x-t.ay}f+=Math.atan2(g,p)*c.radDeg,t.ascaleX<0&&(f+=180),f>180?f-=360:f<-180&&(f+=360);let m=t.ascaleX,x=t.ascaleY;if(s||r){switch(t.data.transformMode){case 3:case 4:p=e-t.worldX,g=i-t.worldY}let o=t.data.length*m,h=Math.sqrt(p*p+g*g);if(s&&h<o||r&&h>o&&o>1e-4){let t=(h/o-1)*a+1;m*=t,n&&(x*=t)}}t.updateWorldTransformWith(t.ax,t.ay,t.arotation+f*a,m,x,t.ashearX,t.ashearY)}apply2(t,e,i,s,r,n,a,o,h){let l=t.ax,u=t.ay,d=t.ascaleX,f=t.ascaleY,p=d,g=f,m=e.ascaleX,x=0,v=0,y=0;d<0?(d=-d,x=180,y=-1):(x=0,y=1),f<0&&(f=-f,y=-y),m<0?(m=-m,v=180):v=0;let w=e.ax,b=0,A=0,M=0,E=t.a,S=t.b,T=t.c,I=t.d,C=Math.abs(d-f)<=1e-4;!C||n?(b=0,A=E*w+t.worldX,M=T*w+t.worldY):(b=e.ay,A=E*w+S*b+t.worldX,M=T*w+I*b+t.worldY);let R=t.parent;if(!R)throw new Error("IK parent must itself have a parent.");E=R.a,S=R.b,T=R.c,I=R.d;let k,F,L=1/(E*I-S*T),P=A-R.worldX,O=M-R.worldY,Y=(P*I-O*S)*L-l,D=(O*E-P*T)*L-u,X=Math.sqrt(Y*Y+D*D),B=e.data.length*m;if(X<1e-4)return this.apply1(t,i,s,!1,n,!1,h),void e.updateWorldTransformWith(w,b,0,e.ascaleX,e.ascaleY,e.ashearX,e.ashearY);P=i-R.worldX,O=s-R.worldY;let z=(P*I-O*S)*L-l,_=(O*E-P*T)*L-u,N=z*z+_*_;if(0!=o){o*=d*(m+1)*.5;let t=Math.sqrt(N),e=t-X-B*d+o;if(e>0){let i=Math.min(1,e/(2*o))-1;i=(e-o*(1-i*i))/t,z-=i*z,_-=i*_,N=z*z+_*_}}t:if(C){B*=d;let t=(N-X*X-B*B)/(2*X*B);t<-1?(t=-1,F=Math.PI*r):t>1?(t=1,F=0,n&&(E=(Math.sqrt(N)/(X+B)-1)*h+1,p*=E,a&&(g*=E))):F=Math.acos(t)*r,E=X+B*t,S=B*Math.sin(F),k=Math.atan2(_*E-z*S,z*E+_*S)}else{E=d*B,S=f*B;let t=E*E,e=S*S,i=Math.atan2(_,z);T=e*X*X+t*N-t*e;let s=-2*e*X,n=e-t;if(I=s*s-4*n*T,I>=0){let t=Math.sqrt(I);s<0&&(t=-t),t=.5*-(s+t);let e=t/n,a=T/t,o=Math.abs(e)<Math.abs(a)?e:a;if(o*o<=N){O=Math.sqrt(N-o*o)*r,k=i-Math.atan2(O,o),F=Math.atan2(O/f,(o-X)/d);break t}}let a=c.PI,o=X-E,h=o*o,l=0,u=0,p=X+E,g=p*p,m=0;T=-E*X/(t-e),T>=-1&&T<=1&&(T=Math.acos(T),P=E*Math.cos(T)+X,O=S*Math.sin(T),I=P*P+O*O,I<h&&(a=T,h=I,o=P,l=O),I>g&&(u=T,g=I,p=P,m=O)),N<=.5*(h+g)?(k=i-Math.atan2(l*r,o),F=a*r):(k=i-Math.atan2(m*r,p),F=u*r)}let V=Math.atan2(b,w)*y,U=t.arotation;k=(k-V)*c.radDeg+x-U,k>180?k-=360:k<-180&&(k+=360),t.updateWorldTransformWith(l,u,U+k*h,p,g,0,0),U=e.arotation,F=((F+V)*c.radDeg-e.ashearX)*y+v-U,F>180?F-=360:F<-180&&(F+=360),e.updateWorldTransformWith(w,b,U+F*h,e.ascaleX,e.ascaleY,e.ashearX,e.ashearY)}},ge=class extends le{constructor(t){super(t,0,!1),this.bones=new Array,this._target=null,this.bendDirection=1,this.compress=!1,this.stretch=!1,this.uniform=!1,this.mix=1,this.softness=0}set target(t){this._target=t}get target(){if(this._target)return this._target;throw new Error("BoneData not set.")}},me=class extends le{constructor(t){super(t,0,!1),this.bones=new Array,this._target=null,this.positionMode=xe.Fixed,this.spacingMode=ve.Fixed,this.rotateMode=ye.Chain,this.offsetRotation=0,this.position=0,this.spacing=0,this.mixRotate=0,this.mixX=0,this.mixY=0}set target(t){this._target=t}get target(){if(this._target)return this._target;throw new Error("SlotData not set.")}},xe=(t=>(t[t.Fixed=0]="Fixed",t[t.Percent=1]="Percent",t))(xe||{}),ve=(t=>(t[t.Length=0]="Length",t[t.Fixed=1]="Fixed",t[t.Percent=2]="Percent",t[t.Proportional=3]="Proportional",t))(ve||{}),ye=(t=>(t[t.Tangent=0]="Tangent",t[t.Chain=1]="Chain",t[t.ChainScale=2]="ChainScale",t))(ye||{}),we=class{constructor(t,e){if(this.position=0,this.spacing=0,this.mixRotate=0,this.mixX=0,this.mixY=0,this.spaces=new Array,this.positions=new Array,this.world=new Array,this.curves=new Array,this.lengths=new Array,this.segments=new Array,this.active=!1,!t)throw new Error("data cannot be null.");if(!e)throw new Error("skeleton cannot be null.");this.data=t,this.bones=new Array;for(let i=0,s=t.bones.length;i<s;i++){let s=e.findBone(t.bones[i].name);if(!s)throw new Error(`Couldn't find bone ${t.bones[i].name}.`);this.bones.push(s)}let i=e.findSlot(t.target.name);if(!i)throw new Error(`Couldn't find target bone ${t.target.name}`);this.target=i,this.position=t.position,this.spacing=t.spacing,this.mixRotate=t.mixRotate,this.mixX=t.mixX,this.mixY=t.mixY}isActive(){return this.active}update(){let t=this.target.getAttachment();if(!(t instanceof ee))return;let e=this.mixRotate,i=this.mixX,s=this.mixY;if(0==e&&0==i&&0==s)return;let r=this.data,n=0==r.rotateMode,a=2==r.rotateMode,o=this.bones,h=o.length,l=n?h:h+1,u=g.setArraySize(this.spaces,l),d=a?this.lengths=g.setArraySize(this.lengths,h):[],f=this.spacing;switch(r.spacingMode){case 2:if(a)for(let t=0,e=l-1;t<e;t++){let e=o[t],i=e.data.length;if(i<we.epsilon)d[t]=0;else{let s=i*e.a,r=i*e.c;d[t]=Math.sqrt(s*s+r*r)}}g.arrayFill(u,1,l,f);break;case 3:let t=0;for(let e=0,i=l-1;e<i;){let i=o[e],s=i.data.length;if(s<we.epsilon)a&&(d[e]=0),u[++e]=f;else{let r=s*i.a,n=s*i.c,o=Math.sqrt(r*r+n*n);a&&(d[e]=o),u[++e]=o,t+=o}}if(t>0){t=l/t*f;for(let e=1;e<l;e++)u[e]*=t}break;default:let e=0==r.spacingMode;for(let t=0,i=l-1;t<i;){let i=o[t],s=i.data.length;if(s<we.epsilon)a&&(d[t]=0),u[++t]=f;else{let r=s*i.a,n=s*i.c,o=Math.sqrt(r*r+n*n);a&&(d[t]=o),u[++t]=(e?s+f:f)*o/s}}}let p=this.computeWorldPositions(t,l,n),m=p[0],x=p[1],v=r.offsetRotation,y=!1;if(0==v)y=1==r.rotateMode;else{y=!1;let t=this.target.bone;v*=t.a*t.d-t.b*t.c>0?c.degRad:-c.degRad}for(let t=0,r=3;t<h;t++,r+=3){let h=o[t];h.worldX+=(m-h.worldX)*i,h.worldY+=(x-h.worldY)*s;let l=p[r],f=p[r+1],g=l-m,w=f-x;if(a){let i=d[t];if(0!=i){let t=(Math.sqrt(g*g+w*w)/i-1)*e+1;h.a*=t,h.c*=t}}if(m=l,x=f,e>0){let i=h.a,s=h.b,a=h.c,o=h.d,l=0,d=0,f=0;if(l=n?p[r-1]:0==u[t+1]?p[r+2]:Math.atan2(w,g),l-=Math.atan2(a,i),y){d=Math.cos(l),f=Math.sin(l);let t=h.data.length;m+=(t*(d*i-f*a)-g)*e,x+=(t*(f*i+d*a)-w)*e}else l+=v;l>c.PI?l-=c.PI2:l<-c.PI&&(l+=c.PI2),l*=e,d=Math.cos(l),f=Math.sin(l),h.a=d*i-f*a,h.b=d*s-f*o,h.c=f*i+d*a,h.d=f*s+d*o}h.updateAppliedTransform()}}computeWorldPositions(t,e,i){let s=this.target,r=this.position,n=this.spaces,a=g.setArraySize(this.positions,3*e+2),o=this.world,h=t.closed,l=t.worldVerticesLength,c=l/6,u=we.NONE;if(!t.constantSpeed){let d=t.lengths;c-=h?1:2;let f,p=d[c];switch(1==this.data.positionMode&&(r*=p),this.data.spacingMode){case 2:f=p;break;case 3:f=p/e;break;default:f=1}o=g.setArraySize(this.world,8);for(let g=0,m=0,x=0;g<e;g++,m+=3){let e=n[g]*f;r+=e;let v=r;if(h)v%=p,v<0&&(v+=p),x=0;else{if(v<0){u!=we.BEFORE&&(u=we.BEFORE,t.computeWorldVertices(s,2,4,o,0,2)),this.addBeforePosition(v,o,0,a,m);continue}if(v>p){u!=we.AFTER&&(u=we.AFTER,t.computeWorldVertices(s,l-6,4,o,0,2)),this.addAfterPosition(v-p,o,0,a,m);continue}}for(;;x++){let t=d[x];if(!(v>t)){if(0==x)v/=t;else{let e=d[x-1];v=(v-e)/(t-e)}break}}x!=u&&(u=x,h&&x==c?(t.computeWorldVertices(s,l-4,4,o,0,2),t.computeWorldVertices(s,0,4,o,4,2)):t.computeWorldVertices(s,6*x+2,8,o,0,2)),this.addCurvePosition(v,o[0],o[1],o[2],o[3],o[4],o[5],o[6],o[7],a,m,i||g>0&&0==e)}return a}h?(l+=2,o=g.setArraySize(this.world,l),t.computeWorldVertices(s,2,l-4,o,0,2),t.computeWorldVertices(s,0,2,o,l-4,2),o[l-2]=o[0],o[l-1]=o[1]):(c--,l-=4,o=g.setArraySize(this.world,l),t.computeWorldVertices(s,2,l,o,0,2));let d,f=g.setArraySize(this.curves,c),p=0,m=o[0],x=o[1],v=0,y=0,w=0,b=0,A=0,M=0,E=0,S=0,T=0,I=0,C=0,R=0,k=0,F=0;for(let t=0,e=2;t<c;t++,e+=6)v=o[e],y=o[e+1],w=o[e+2],b=o[e+3],A=o[e+4],M=o[e+5],E=.1875*(m-2*v+w),S=.1875*(x-2*y+b),T=.09375*(3*(v-w)-m+A),I=.09375*(3*(y-b)-x+M),C=2*E+T,R=2*S+I,k=.75*(v-m)+E+.16666667*T,F=.75*(y-x)+S+.16666667*I,p+=Math.sqrt(k*k+F*F),k+=C,F+=R,C+=T,R+=I,p+=Math.sqrt(k*k+F*F),k+=C,F+=R,p+=Math.sqrt(k*k+F*F),k+=C+T,F+=R+I,p+=Math.sqrt(k*k+F*F),f[t]=p,m=A,x=M;switch(1==this.data.positionMode&&(r*=p),this.data.spacingMode){case 2:d=p;break;case 3:d=p/e;break;default:d=1}let L=this.segments,P=0;for(let t=0,s=0,c=0,g=0;t<e;t++,s+=3){let e=n[t]*d;r+=e;let O=r;if(h)O%=p,O<0&&(O+=p),c=0;else{if(O<0){this.addBeforePosition(O,o,0,a,s);continue}if(O>p){this.addAfterPosition(O-p,o,l-4,a,s);continue}}for(;;c++){let t=f[c];if(!(O>t)){if(0==c)O/=t;else{let e=f[c-1];O=(O-e)/(t-e)}break}}if(c!=u){u=c;let t=6*c;for(m=o[t],x=o[t+1],v=o[t+2],y=o[t+3],w=o[t+4],b=o[t+5],A=o[t+6],M=o[t+7],E=.03*(m-2*v+w),S=.03*(x-2*y+b),T=.006*(3*(v-w)-m+A),I=.006*(3*(y-b)-x+M),C=2*E+T,R=2*S+I,k=.3*(v-m)+E+.16666667*T,F=.3*(y-x)+S+.16666667*I,P=Math.sqrt(k*k+F*F),L[0]=P,t=1;t<8;t++)k+=C,F+=R,C+=T,R+=I,P+=Math.sqrt(k*k+F*F),L[t]=P;k+=C,F+=R,P+=Math.sqrt(k*k+F*F),L[8]=P,k+=C+T,F+=R+I,P+=Math.sqrt(k*k+F*F),L[9]=P,g=0}for(O*=P;;g++){let t=L[g];if(!(O>t)){if(0==g)O/=t;else{let e=L[g-1];O=g+(O-e)/(t-e)}break}}this.addCurvePosition(.1*O,m,x,v,y,w,b,A,M,a,s,i||t>0&&0==e)}return a}addBeforePosition(t,e,i,s,r){let n=e[i],a=e[i+1],o=e[i+2]-n,h=e[i+3]-a,l=Math.atan2(h,o);s[r]=n+t*Math.cos(l),s[r+1]=a+t*Math.sin(l),s[r+2]=l}addAfterPosition(t,e,i,s,r){let n=e[i+2],a=e[i+3],o=n-e[i],h=a-e[i+1],l=Math.atan2(h,o);s[r]=n+t*Math.cos(l),s[r+1]=a+t*Math.sin(l),s[r+2]=l}addCurvePosition(t,e,i,s,r,n,a,o,h,l,c,u){if(0==t||isNaN(t))return l[c]=e,l[c+1]=i,void(l[c+2]=Math.atan2(r-i,s-e));let d=t*t,f=d*t,p=1-t,g=p*p,m=g*p,x=p*t,v=3*x,y=p*v,w=v*t,b=e*m+s*y+n*w+o*f,A=i*m+r*y+a*w+h*f;l[c]=b,l[c+1]=A,u&&(l[c+2]=t<.001?Math.atan2(r-i,s-e):Math.atan2(A-(i*g+r*x*2+a*d),b-(e*g+s*x*2+n*d)))}},be=we;be.NONE=-1,be.BEFORE=-2,be.AFTER=-3,be.epsilon=1e-5;var Ae=class{constructor(t,e){if(this.darkColor=null,this.attachment=null,this.attachmentState=0,this.sequenceIndex=-1,this.deform=new Array,!t)throw new Error("data cannot be null.");if(!e)throw new Error("bone cannot be null.");this.data=t,this.bone=e,this.color=new h,this.darkColor=t.darkColor?new h:null,this.setToSetupPose()}getSkeleton(){return this.bone.skeleton}getAttachment(){return this.attachment}setAttachment(t){this.attachment!=t&&(t instanceof M&&this.attachment instanceof M&&t.timelineAttachment==this.attachment.timelineAttachment||(this.deform.length=0),this.attachment=t,this.sequenceIndex=-1)}setToSetupPose(){this.color.setFromColor(this.data.color),this.darkColor&&this.darkColor.setFromColor(this.data.darkColor),this.data.attachmentName?(this.attachment=null,this.setAttachment(this.bone.skeleton.getAttachment(this.data.index,this.data.attachmentName))):this.attachment=null}},Me=class{constructor(t,e){if(this.mixRotate=0,this.mixX=0,this.mixY=0,this.mixScaleX=0,this.mixScaleY=0,this.mixShearY=0,this.temp=new v,this.active=!1,!t)throw new Error("data cannot be null.");if(!e)throw new Error("skeleton cannot be null.");this.data=t,this.mixRotate=t.mixRotate,this.mixX=t.mixX,this.mixY=t.mixY,this.mixScaleX=t.mixScaleX,this.mixScaleY=t.mixScaleY,this.mixShearY=t.mixShearY,this.bones=new Array;for(let i=0;i<t.bones.length;i++){let s=e.findBone(t.bones[i].name);if(!s)throw new Error(`Couldn't find bone ${t.bones[i].name}.`);this.bones.push(s)}let i=e.findBone(t.target.name);if(!i)throw new Error(`Couldn't find target bone ${t.target.name}.`);this.target=i}isActive(){return this.active}update(){0==this.mixRotate&&0==this.mixX&&0==this.mixY&&0==this.mixScaleX&&0==this.mixScaleY&&0==this.mixShearY||(this.data.local?this.data.relative?this.applyRelativeLocal():this.applyAbsoluteLocal():this.data.relative?this.applyRelativeWorld():this.applyAbsoluteWorld())}applyAbsoluteWorld(){let t=this.mixRotate,e=this.mixX,i=this.mixY,s=this.mixScaleX,r=this.mixScaleY,n=this.mixShearY,a=0!=e||0!=i,o=this.target,h=o.a,l=o.b,u=o.c,d=o.d,f=h*d-l*u>0?c.degRad:-c.degRad,p=this.data.offsetRotation*f,g=this.data.offsetShearY*f,m=this.bones;for(let f=0,x=m.length;f<x;f++){let x=m[f];if(0!=t){let e=x.a,i=x.b,s=x.c,r=x.d,n=Math.atan2(u,h)-Math.atan2(s,e)+p;n>c.PI?n-=c.PI2:n<-c.PI&&(n+=c.PI2),n*=t;let a=Math.cos(n),o=Math.sin(n);x.a=a*e-o*s,x.b=a*i-o*r,x.c=o*e+a*s,x.d=o*i+a*r}if(a){let t=this.temp;o.localToWorld(t.set(this.data.offsetX,this.data.offsetY)),x.worldX+=(t.x-x.worldX)*e,x.worldY+=(t.y-x.worldY)*i}if(0!=s){let t=Math.sqrt(x.a*x.a+x.c*x.c);0!=t&&(t=(t+(Math.sqrt(h*h+u*u)-t+this.data.offsetScaleX)*s)/t),x.a*=t,x.c*=t}if(0!=r){let t=Math.sqrt(x.b*x.b+x.d*x.d);0!=t&&(t=(t+(Math.sqrt(l*l+d*d)-t+this.data.offsetScaleY)*r)/t),x.b*=t,x.d*=t}if(n>0){let t=x.b,e=x.d,i=Math.atan2(e,t),s=Math.atan2(d,l)-Math.atan2(u,h)-(i-Math.atan2(x.c,x.a));s>c.PI?s-=c.PI2:s<-c.PI&&(s+=c.PI2),s=i+(s+g)*n;let r=Math.sqrt(t*t+e*e);x.b=Math.cos(s)*r,x.d=Math.sin(s)*r}x.updateAppliedTransform()}}applyRelativeWorld(){let t=this.mixRotate,e=this.mixX,i=this.mixY,s=this.mixScaleX,r=this.mixScaleY,n=this.mixShearY,a=0!=e||0!=i,o=this.target,h=o.a,l=o.b,u=o.c,d=o.d,f=h*d-l*u>0?c.degRad:-c.degRad,p=this.data.offsetRotation*f,g=this.data.offsetShearY*f,m=this.bones;for(let f=0,x=m.length;f<x;f++){let x=m[f];if(0!=t){let e=x.a,i=x.b,s=x.c,r=x.d,n=Math.atan2(u,h)+p;n>c.PI?n-=c.PI2:n<-c.PI&&(n+=c.PI2),n*=t;let a=Math.cos(n),o=Math.sin(n);x.a=a*e-o*s,x.b=a*i-o*r,x.c=o*e+a*s,x.d=o*i+a*r}if(a){let t=this.temp;o.localToWorld(t.set(this.data.offsetX,this.data.offsetY)),x.worldX+=t.x*e,x.worldY+=t.y*i}if(0!=s){let t=(Math.sqrt(h*h+u*u)-1+this.data.offsetScaleX)*s+1;x.a*=t,x.c*=t}if(0!=r){let t=(Math.sqrt(l*l+d*d)-1+this.data.offsetScaleY)*r+1;x.b*=t,x.d*=t}if(n>0){let t=Math.atan2(d,l)-Math.atan2(u,h);t>c.PI?t-=c.PI2:t<-c.PI&&(t+=c.PI2);let e=x.b,i=x.d;t=Math.atan2(i,e)+(t-c.PI/2+g)*n;let s=Math.sqrt(e*e+i*i);x.b=Math.cos(t)*s,x.d=Math.sin(t)*s}x.updateAppliedTransform()}}applyAbsoluteLocal(){let t=this.mixRotate,e=this.mixX,i=this.mixY,s=this.mixScaleX,r=this.mixScaleY,n=this.mixShearY,a=this.target,o=this.bones;for(let h=0,l=o.length;h<l;h++){let l=o[h],c=l.arotation;if(0!=t){let e=a.arotation-c+this.data.offsetRotation;e-=360*(16384-(16384.499999999996-e/360|0)),c+=e*t}let u=l.ax,d=l.ay;u+=(a.ax-u+this.data.offsetX)*e,d+=(a.ay-d+this.data.offsetY)*i;let f=l.ascaleX,p=l.ascaleY;0!=s&&0!=f&&(f=(f+(a.ascaleX-f+this.data.offsetScaleX)*s)/f),0!=r&&0!=p&&(p=(p+(a.ascaleY-p+this.data.offsetScaleY)*r)/p);let g=l.ashearY;if(0!=n){let t=a.ashearY-g+this.data.offsetShearY;t-=360*(16384-(16384.499999999996-t/360|0)),g+=t*n}l.updateWorldTransformWith(u,d,c,f,p,l.ashearX,g)}}applyRelativeLocal(){let t=this.mixRotate,e=this.mixX,i=this.mixY,s=this.mixScaleX,r=this.mixScaleY,n=this.mixShearY,a=this.target,o=this.bones;for(let h=0,l=o.length;h<l;h++){let l=o[h],c=l.arotation+(a.arotation+this.data.offsetRotation)*t,u=l.ax+(a.ax+this.data.offsetX)*e,d=l.ay+(a.ay+this.data.offsetY)*i,f=l.ascaleX*((a.ascaleX-1+this.data.offsetScaleX)*s+1),p=l.ascaleY*((a.ascaleY-1+this.data.offsetScaleY)*r+1),g=l.ashearY+(a.ashearY+this.data.offsetShearY)*n;l.updateWorldTransformWith(u,d,c,f,p,l.ashearX,g)}}},Ee=class{constructor(t){if(this._updateCache=new Array,this.skin=null,this.scaleX=1,this._scaleY=1,this.x=0,this.y=0,!t)throw new Error("data cannot be null.");this.data=t,this.bones=new Array;for(let e=0;e<t.bones.length;e++){let i,s=t.bones[e];if(s.parent){let t=this.bones[s.parent.index];i=new he(s,this,t),t.children.push(i)}else i=new he(s,this,null);this.bones.push(i)}this.slots=new Array,this.drawOrder=new Array;for(let e=0;e<t.slots.length;e++){let i=t.slots[e],s=this.bones[i.boneData.index],r=new Ae(i,s);this.slots.push(r),this.drawOrder.push(r)}this.ikConstraints=new Array;for(let e=0;e<t.ikConstraints.length;e++){let i=t.ikConstraints[e];this.ikConstraints.push(new pe(i,this))}this.transformConstraints=new Array;for(let e=0;e<t.transformConstraints.length;e++){let i=t.transformConstraints[e];this.transformConstraints.push(new Me(i,this))}this.pathConstraints=new Array;for(let e=0;e<t.pathConstraints.length;e++){let i=t.pathConstraints[e];this.pathConstraints.push(new be(i,this))}this.color=new h(1,1,1,1),this.updateCache()}get scaleY(){return Ee.yDown?-this._scaleY:this._scaleY}set scaleY(t){this._scaleY=t}updateCache(){this._updateCache.length=0;let t=this.bones;for(let e=0,i=t.length;e<i;e++){let i=t[e];i.sorted=i.data.skinRequired,i.active=!i.sorted}if(this.skin){let t=this.skin.bones;for(let e=0,i=this.skin.bones.length;e<i;e++){let i=this.bones[t[e].index];do{i.sorted=!1,i.active=!0,i=i.parent}while(i)}}let e=this.ikConstraints,i=this.transformConstraints,s=this.pathConstraints,r=e.length,n=i.length,a=s.length,o=r+n+a;t:for(let t=0;t<o;t++){for(let i=0;i<r;i++){let s=e[i];if(s.data.order==t){this.sortIkConstraint(s);continue t}}for(let e=0;e<n;e++){let s=i[e];if(s.data.order==t){this.sortTransformConstraint(s);continue t}}for(let e=0;e<a;e++){let i=s[e];if(i.data.order==t){this.sortPathConstraint(i);continue t}}}for(let e=0,i=t.length;e<i;e++)this.sortBone(t[e])}sortIkConstraint(t){if(t.active=t.target.isActive()&&(!t.data.skinRequired||this.skin&&g.contains(this.skin.constraints,t.data,!0)),!t.active)return;let e=t.target;this.sortBone(e);let i=t.bones,s=i[0];if(this.sortBone(s),1==i.length)this._updateCache.push(t),this.sortReset(s.children);else{let e=i[i.length-1];this.sortBone(e),this._updateCache.push(t),this.sortReset(s.children),e.sorted=!0}}sortPathConstraint(t){if(t.active=t.target.bone.isActive()&&(!t.data.skinRequired||this.skin&&g.contains(this.skin.constraints,t.data,!0)),!t.active)return;let e=t.target,i=e.data.index,s=e.bone;this.skin&&this.sortPathConstraintAttachment(this.skin,i,s),this.data.defaultSkin&&this.data.defaultSkin!=this.skin&&this.sortPathConstraintAttachment(this.data.defaultSkin,i,s);for(let t=0,e=this.data.skins.length;t<e;t++)this.sortPathConstraintAttachment(this.data.skins[t],i,s);let r=e.getAttachment();r instanceof ee&&this.sortPathConstraintAttachmentWith(r,s);let n=t.bones,a=n.length;for(let t=0;t<a;t++)this.sortBone(n[t]);this._updateCache.push(t);for(let t=0;t<a;t++)this.sortReset(n[t].children);for(let t=0;t<a;t++)n[t].sorted=!0}sortTransformConstraint(t){if(t.active=t.target.isActive()&&(!t.data.skinRequired||this.skin&&g.contains(this.skin.constraints,t.data,!0)),!t.active)return;this.sortBone(t.target);let e=t.bones,i=e.length;if(t.data.local)for(let t=0;t<i;t++){let i=e[t];this.sortBone(i.parent),this.sortBone(i)}else for(let t=0;t<i;t++)this.sortBone(e[t]);this._updateCache.push(t);for(let t=0;t<i;t++)this.sortReset(e[t].children);for(let t=0;t<i;t++)e[t].sorted=!0}sortPathConstraintAttachment(t,e,i){let s=t.attachments[e];if(s)for(let t in s)this.sortPathConstraintAttachmentWith(s[t],i)}sortPathConstraintAttachmentWith(t,e){if(!(t instanceof ee))return;let i=t.bones;if(i){let t=this.bones;for(let e=0,s=i.length;e<s;){let s=i[e++];for(s+=e;e<s;)this.sortBone(t[i[e++]])}}else this.sortBone(e)}sortBone(t){if(!t)return;if(t.sorted)return;let e=t.parent;e&&this.sortBone(e),t.sorted=!0,this._updateCache.push(t)}sortReset(t){for(let e=0,i=t.length;e<i;e++){let i=t[e];i.active&&(i.sorted&&this.sortReset(i.children),i.sorted=!1)}}updateWorldTransform(){let t=this.bones;for(let e=0,i=t.length;e<i;e++){let i=t[e];i.ax=i.x,i.ay=i.y,i.arotation=i.rotation,i.ascaleX=i.scaleX,i.ascaleY=i.scaleY,i.ashearX=i.shearX,i.ashearY=i.shearY}let e=this._updateCache;for(let t=0,i=e.length;t<i;t++)e[t].update()}updateWorldTransformWith(t){let e=this.getRootBone();if(!e)throw new Error("Root bone must not be null.");let i=t.a,s=t.b,r=t.c,n=t.d;e.worldX=i*this.x+s*this.y+t.worldX,e.worldY=r*this.x+n*this.y+t.worldY;let a=e.rotation+90+e.shearY,o=c.cosDeg(e.rotation+e.shearX)*e.scaleX,h=c.cosDeg(a)*e.scaleY,l=c.sinDeg(e.rotation+e.shearX)*e.scaleX,u=c.sinDeg(a)*e.scaleY;e.a=(i*o+s*l)*this.scaleX,e.b=(i*h+s*u)*this.scaleX,e.c=(r*o+n*l)*this.scaleY,e.d=(r*h+n*u)*this.scaleY;let d=this._updateCache;for(let t=0,i=d.length;t<i;t++){let i=d[t];i!=e&&i.update()}}setToSetupPose(){this.setBonesToSetupPose(),this.setSlotsToSetupPose()}setBonesToSetupPose(){let t=this.bones;for(let e=0,i=t.length;e<i;e++)t[e].setToSetupPose();let e=this.ikConstraints;for(let t=0,i=e.length;t<i;t++){let i=e[t];i.mix=i.data.mix,i.softness=i.data.softness,i.bendDirection=i.data.bendDirection,i.compress=i.data.compress,i.stretch=i.data.stretch}let i=this.transformConstraints;for(let t=0,e=i.length;t<e;t++){let e=i[t],s=e.data;e.mixRotate=s.mixRotate,e.mixX=s.mixX,e.mixY=s.mixY,e.mixScaleX=s.mixScaleX,e.mixScaleY=s.mixScaleY,e.mixShearY=s.mixShearY}let s=this.pathConstraints;for(let t=0,e=s.length;t<e;t++){let e=s[t],i=e.data;e.position=i.position,e.spacing=i.spacing,e.mixRotate=i.mixRotate,e.mixX=i.mixX,e.mixY=i.mixY}}setSlotsToSetupPose(){let t=this.slots;g.arrayCopy(t,0,this.drawOrder,0,t.length);for(let e=0,i=t.length;e<i;e++)t[e].setToSetupPose()}getRootBone(){return 0==this.bones.length?null:this.bones[0]}findBone(t){if(!t)throw new Error("boneName cannot be null.");let e=this.bones;for(let i=0,s=e.length;i<s;i++){let s=e[i];if(s.data.name==t)return s}return null}findSlot(t){if(!t)throw new Error("slotName cannot be null.");let e=this.slots;for(let i=0,s=e.length;i<s;i++){let s=e[i];if(s.data.name==t)return s}return null}setSkinByName(t){let e=this.data.findSkin(t);if(!e)throw new Error("Skin not found: "+t);this.setSkin(e)}setSkin(t){if(t!=this.skin){if(t)if(this.skin)t.attachAll(this,this.skin);else{let e=this.slots;for(let i=0,s=e.length;i<s;i++){let s=e[i],r=s.data.attachmentName;if(r){let e=t.getAttachment(i,r);e&&s.setAttachment(e)}}}this.skin=t,this.updateCache()}}getAttachmentByName(t,e){let i=this.data.findSlot(t);if(!i)throw new Error(`Can't find slot with name ${t}`);return this.getAttachment(i.index,e)}getAttachment(t,e){if(!e)throw new Error("attachmentName cannot be null.");if(this.skin){let i=this.skin.getAttachment(t,e);if(i)return i}return this.data.defaultSkin?this.data.defaultSkin.getAttachment(t,e):null}setAttachment(t,e){if(!t)throw new Error("slotName cannot be null.");let i=this.slots;for(let s=0,r=i.length;s<r;s++){let r=i[s];if(r.data.name==t){let i=null;if(e&&(i=this.getAttachment(s,e),!i))throw new Error("Attachment not found: "+e+", for slot: "+t);return void r.setAttachment(i)}}throw new Error("Slot not found: "+t)}findIkConstraint(t){if(!t)throw new Error("constraintName cannot be null.");let e=this.ikConstraints;for(let i=0,s=e.length;i<s;i++){let s=e[i];if(s.data.name==t)return s}return null}findTransformConstraint(t){if(!t)throw new Error("constraintName cannot be null.");let e=this.transformConstraints;for(let i=0,s=e.length;i<s;i++){let s=e[i];if(s.data.name==t)return s}return null}findPathConstraint(t){if(!t)throw new Error("constraintName cannot be null.");let e=this.pathConstraints;for(let i=0,s=e.length;i<s;i++){let s=e[i];if(s.data.name==t)return s}return null}getBoundsRect(){let t=new v,e=new v;return this.getBounds(t,e),{x:t.x,y:t.y,width:e.x,height:e.y}}getBounds(t,e,i=new Array(2)){if(!t)throw new Error("offset cannot be null.");if(!e)throw new Error("size cannot be null.");let s=this.drawOrder,r=Number.POSITIVE_INFINITY,n=Number.POSITIVE_INFINITY,a=Number.NEGATIVE_INFINITY,o=Number.NEGATIVE_INFINITY;for(let t=0,e=s.length;t<e;t++){let e=s[t];if(!e.bone.active)continue;let h=0,l=null,c=e.getAttachment();if(c instanceof re)h=8,l=g.setArraySize(i,h,0),c.computeWorldVertices(e,l,0,2);else if(c instanceof te){let t=c;h=t.worldVerticesLength,l=g.setArraySize(i,h,0),t.computeWorldVertices(e,0,h,l,0,2)}if(l)for(let t=0,e=l.length;t<e;t+=2){let e=l[t],i=l[t+1];r=Math.min(r,e),n=Math.min(n,i),a=Math.max(a,e),o=Math.max(o,i)}}t.set(r,n),e.set(a-r,o-n)}},Se=Ee;Se.yDown=!1;var Te=class{constructor(){this.name=null,this.bones=new Array,this.slots=new Array,this.skins=new Array,this.defaultSkin=null,this.events=new Array,this.animations=new Array,this.ikConstraints=new Array,this.transformConstraints=new Array,this.pathConstraints=new Array,this.x=0,this.y=0,this.width=0,this.height=0,this.version=null,this.hash=null,this.fps=0,this.imagesPath=null,this.audioPath=null}findBone(t){if(!t)throw new Error("boneName cannot be null.");let e=this.bones;for(let i=0,s=e.length;i<s;i++){let s=e[i];if(s.name==t)return s}return null}findSlot(t){if(!t)throw new Error("slotName cannot be null.");let e=this.slots;for(let i=0,s=e.length;i<s;i++){let s=e[i];if(s.name==t)return s}return null}findSkin(t){if(!t)throw new Error("skinName cannot be null.");let e=this.skins;for(let i=0,s=e.length;i<s;i++){let s=e[i];if(s.name==t)return s}return null}findEvent(t){if(!t)throw new Error("eventDataName cannot be null.");let e=this.events;for(let i=0,s=e.length;i<s;i++){let s=e[i];if(s.name==t)return s}return null}findAnimation(t){if(!t)throw new Error("animationName cannot be null.");let e=this.animations;for(let i=0,s=e.length;i<s;i++){let s=e[i];if(s.name==t)return s}return null}findIkConstraint(t){if(!t)throw new Error("constraintName cannot be null.");let e=this.ikConstraints;for(let i=0,s=e.length;i<s;i++){let s=e[i];if(s.name==t)return s}return null}findTransformConstraint(t){if(!t)throw new Error("constraintName cannot be null.");let e=this.transformConstraints;for(let i=0,s=e.length;i<s;i++){let s=e[i];if(s.name==t)return s}return null}findPathConstraint(t){if(!t)throw new Error("constraintName cannot be null.");let e=this.pathConstraints;for(let i=0,s=e.length;i<s;i++){let s=e[i];if(s.name==t)return s}return null}},Ie=class{constructor(t=0,e,i){this.slotIndex=t,this.name=e,this.attachment=i}},Ce=class{constructor(t){if(this.attachments=new Array,this.bones=Array(),this.constraints=new Array,!t)throw new Error("name cannot be null.");this.name=t}setAttachment(t,e,i){if(!i)throw new Error("attachment cannot be null.");let s=this.attachments;t>=s.length&&(s.length=t+1),s[t]||(s[t]={}),s[t][e]=i}addSkin(t){for(let e=0;e<t.bones.length;e++){let i=t.bones[e],s=!1;for(let t=0;t<this.bones.length;t++)if(this.bones[t]==i){s=!0;break}s||this.bones.push(i)}for(let e=0;e<t.constraints.length;e++){let i=t.constraints[e],s=!1;for(let t=0;t<this.constraints.length;t++)if(this.constraints[t]==i){s=!0;break}s||this.constraints.push(i)}let e=t.getAttachments();for(let t=0;t<e.length;t++){var i=e[t];this.setAttachment(i.slotIndex,i.name,i.attachment)}}copySkin(t){for(let e=0;e<t.bones.length;e++){let i=t.bones[e],s=!1;for(let t=0;t<this.bones.length;t++)if(this.bones[t]==i){s=!0;break}s||this.bones.push(i)}for(let e=0;e<t.constraints.length;e++){let i=t.constraints[e],s=!1;for(let t=0;t<this.constraints.length;t++)if(this.constraints[t]==i){s=!0;break}s||this.constraints.push(i)}let e=t.getAttachments();for(let t=0;t<e.length;t++){var i=e[t];i.attachment&&(i.attachment instanceof te?(i.attachment=i.attachment.newLinkedMesh(),this.setAttachment(i.slotIndex,i.name,i.attachment)):(i.attachment=i.attachment.copy(),this.setAttachment(i.slotIndex,i.name,i.attachment)))}}getAttachment(t,e){let i=this.attachments[t];return i?i[e]:null}removeAttachment(t,e){let i=this.attachments[t];i&&delete i[e]}getAttachments(){let t=new Array;for(var e=0;e<this.attachments.length;e++){let i=this.attachments[e];if(i)for(let s in i){let r=i[s];r&&t.push(new Ie(e,s,r))}}return t}getAttachmentsForSlot(t,e){let i=this.attachments[t];if(i)for(let s in i){let r=i[s];r&&e.push(new Ie(t,s,r))}}clear(){this.attachments.length=0,this.bones.length=0,this.constraints.length=0}attachAll(t,e){let i=0;for(let s=0;s<t.slots.length;s++){let r=t.slots[s],n=r.getAttachment();if(n&&i<e.attachments.length){let t=e.attachments[i];for(let e in t){if(n==t[e]){let t=this.getAttachment(i,e);t&&r.setAttachment(t);break}}}i++}}},Re=class{constructor(t,e,i){if(this.index=0,this.color=new h(1,1,1,1),this.darkColor=null,this.attachmentName=null,this.blendMode=ke.Normal,t<0)throw new Error("index must be >= 0.");if(!e)throw new Error("name cannot be null.");if(!i)throw new Error("boneData cannot be null.");this.index=t,this.name=e,this.boneData=i}},ke=(t=>(t[t.Normal=0]="Normal",t[t.Additive=1]="Additive",t[t.Multiply=2]="Multiply",t[t.Screen=3]="Screen",t))(ke||{}),Fe=class extends le{constructor(t){super(t,0,!1),this.bones=new Array,this._target=null,this.mixRotate=0,this.mixX=0,this.mixY=0,this.mixScaleX=0,this.mixScaleY=0,this.mixShearY=0,this.offsetRotation=0,this.offsetX=0,this.offsetY=0,this.offsetScaleX=0,this.offsetScaleY=0,this.offsetShearY=0,this.relative=!1,this.local=!1}set target(t){this._target=t}get target(){if(this._target)return this._target;throw new Error("BoneData not set.")}},Le=class{constructor(t){this.scale=1,this.linkedMeshes=new Array,this.attachmentLoader=t}readSkeletonData(t){let e=this.scale,i=new Te;i.name="";let s=new Pe(t),r=s.readInt32(),n=s.readInt32();i.hash=0==n&&0==r?null:n.toString(16)+r.toString(16),i.version=s.readString(),i.x=s.readFloat(),i.y=s.readFloat(),i.width=s.readFloat(),i.height=s.readFloat();let a=s.readBoolean();a&&(i.fps=s.readFloat(),i.imagesPath=s.readString(),i.audioPath=s.readString());let o=0;o=s.readInt(!0);for(let t=0;t<o;t++){let t=s.readString();if(!t)throw new Error("String in string table must not be null.");s.strings.push(t)}o=s.readInt(!0);for(let t=0;t<o;t++){let r=s.readString();if(!r)throw new Error("Bone name must not be null.");let n=0==t?null:i.bones[s.readInt(!0)],o=new ae(t,r,n);o.rotation=s.readFloat(),o.x=s.readFloat()*e,o.y=s.readFloat()*e,o.scaleX=s.readFloat(),o.scaleY=s.readFloat(),o.shearX=s.readFloat(),o.shearY=s.readFloat(),o.length=s.readFloat()*e,o.transformMode=s.readInt(!0),o.skinRequired=s.readBoolean(),a&&h.rgba8888ToColor(o.color,s.readInt32()),i.bones.push(o)}o=s.readInt(!0);for(let t=0;t<o;t++){let e=s.readString();if(!e)throw new Error("Slot name must not be null.");let r=i.bones[s.readInt(!0)],n=new Re(t,e,r);h.rgba8888ToColor(n.color,s.readInt32());let a=s.readInt32();-1!=a&&h.rgb888ToColor(n.darkColor=new h,a),n.attachmentName=s.readStringRef(),n.blendMode=s.readInt(!0),i.slots.push(n)}o=s.readInt(!0);for(let t,r=0;r<o;r++){let r=s.readString();if(!r)throw new Error("IK constraint data name must not be null.");let n=new ge(r);n.order=s.readInt(!0),n.skinRequired=s.readBoolean(),t=s.readInt(!0);for(let e=0;e<t;e++)n.bones.push(i.bones[s.readInt(!0)]);n.target=i.bones[s.readInt(!0)],n.mix=s.readFloat(),n.softness=s.readFloat()*e,n.bendDirection=s.readByte(),n.compress=s.readBoolean(),n.stretch=s.readBoolean(),n.uniform=s.readBoolean(),i.ikConstraints.push(n)}o=s.readInt(!0);for(let t,r=0;r<o;r++){let r=s.readString();if(!r)throw new Error("Transform constraint data name must not be null.");let n=new Fe(r);n.order=s.readInt(!0),n.skinRequired=s.readBoolean(),t=s.readInt(!0);for(let e=0;e<t;e++)n.bones.push(i.bones[s.readInt(!0)]);n.target=i.bones[s.readInt(!0)],n.local=s.readBoolean(),n.relative=s.readBoolean(),n.offsetRotation=s.readFloat(),n.offsetX=s.readFloat()*e,n.offsetY=s.readFloat()*e,n.offsetScaleX=s.readFloat(),n.offsetScaleY=s.readFloat(),n.offsetShearY=s.readFloat(),n.mixRotate=s.readFloat(),n.mixX=s.readFloat(),n.mixY=s.readFloat(),n.mixScaleX=s.readFloat(),n.mixScaleY=s.readFloat(),n.mixShearY=s.readFloat(),i.transformConstraints.push(n)}o=s.readInt(!0);for(let t,r=0;r<o;r++){let r=s.readString();if(!r)throw new Error("Path constraint data name must not be null.");let n=new me(r);n.order=s.readInt(!0),n.skinRequired=s.readBoolean(),t=s.readInt(!0);for(let e=0;e<t;e++)n.bones.push(i.bones[s.readInt(!0)]);n.target=i.slots[s.readInt(!0)],n.positionMode=s.readInt(!0),n.spacingMode=s.readInt(!0),n.rotateMode=s.readInt(!0),n.offsetRotation=s.readFloat(),n.position=s.readFloat(),0==n.positionMode&&(n.position*=e),n.spacing=s.readFloat(),0!=n.spacingMode&&1!=n.spacingMode||(n.spacing*=e),n.mixRotate=s.readFloat(),n.mixX=s.readFloat(),n.mixY=s.readFloat(),i.pathConstraints.push(n)}let l=this.readSkin(s,i,!0,a);l&&(i.defaultSkin=l,i.skins.push(l));{let t=i.skins.length;for(g.setArraySize(i.skins,o=t+s.readInt(!0));t<o;t++){let e=this.readSkin(s,i,!1,a);if(!e)throw new Error("readSkin() should not have returned null.");i.skins[t]=e}}o=this.linkedMeshes.length;for(let t=0;t<o;t++){let e=this.linkedMeshes[t],s=e.skin?i.findSkin(e.skin):i.defaultSkin;if(!s)throw new Error("Not skin found for linked mesh.");if(!e.parent)throw new Error("Linked mesh parent must not be null");let r=s.getAttachment(e.slotIndex,e.parent);if(!r)throw new Error(`Parent mesh not found: ${e.parent}`);e.mesh.timelineAttachment=e.inheritTimeline?r:e.mesh,e.mesh.setParentMesh(r),null!=e.mesh.region&&e.mesh.updateRegion()}this.linkedMeshes.length=0,o=s.readInt(!0);for(let t=0;t<o;t++){let t=s.readStringRef();if(!t)throw new Error;let e=new fe(t);e.intValue=s.readInt(!1),e.floatValue=s.readFloat(),e.stringValue=s.readString(),e.audioPath=s.readString(),e.audioPath&&(e.volume=s.readFloat(),e.balance=s.readFloat()),i.events.push(e)}o=s.readInt(!0);for(let t=0;t<o;t++){let t=s.readString();if(!t)throw new Error("Animatio name must not be null.");i.animations.push(this.readAnimation(s,t,i))}return i}readSkin(t,e,i,s){let r=null,n=0;if(i){if(n=t.readInt(!0),0==n)return null;r=new Ce("default")}else{let i=t.readStringRef();if(!i)throw new Error("Skin name must not be null.");r=new Ce(i),r.bones.length=t.readInt(!0);for(let i=0,s=r.bones.length;i<s;i++)r.bones[i]=e.bones[t.readInt(!0)];for(let i=0,s=t.readInt(!0);i<s;i++)r.constraints.push(e.ikConstraints[t.readInt(!0)]);for(let i=0,s=t.readInt(!0);i<s;i++)r.constraints.push(e.transformConstraints[t.readInt(!0)]);for(let i=0,s=t.readInt(!0);i<s;i++)r.constraints.push(e.pathConstraints[t.readInt(!0)]);n=t.readInt(!0)}for(let i=0;i<n;i++){let i=t.readInt(!0);for(let n=0,a=t.readInt(!0);n<a;n++){let n=t.readStringRef();if(!n)throw new Error("Attachment name must not be null");let a=this.readAttachment(t,e,r,i,n,s);a&&r.setAttachment(i,n,a)}}return r}readAttachment(t,e,i,s,r,n){let a=this.scale,o=t.readStringRef();switch(o||(o=r),t.readByte()){case De.Region:{let e=t.readStringRef(),s=t.readFloat(),r=t.readFloat(),n=t.readFloat(),l=t.readFloat(),c=t.readFloat(),u=t.readFloat(),d=t.readFloat(),f=t.readInt32(),p=this.readSequence(t);e||(e=o);let g=this.attachmentLoader.newRegionAttachment(i,o,e,p);return g?(g.path=e,g.x=r*a,g.y=n*a,g.scaleX=l,g.scaleY=c,g.rotation=s,g.width=u*a,g.height=d*a,h.rgba8888ToColor(g.color,f),g.sequence=p,null==p&&g.updateRegion(),g):null}case De.BoundingBox:{let e=t.readInt(!0),s=this.readVertices(t,e),r=n?t.readInt32():0,a=this.attachmentLoader.newBoundingBoxAttachment(i,o);return a?(a.worldVerticesLength=e<<1,a.vertices=s.vertices,a.bones=s.bones,n&&h.rgba8888ToColor(a.color,r),a):null}case De.Mesh:{let e=t.readStringRef(),s=t.readInt32(),r=t.readInt(!0),l=this.readFloatArray(t,r<<1,1),c=this.readShortArray(t),u=this.readVertices(t,r),d=t.readInt(!0),f=this.readSequence(t),p=[],g=0,m=0;n&&(p=this.readShortArray(t),g=t.readFloat(),m=t.readFloat()),e||(e=o);let x=this.attachmentLoader.newMeshAttachment(i,o,e,f);return x?(x.path=e,h.rgba8888ToColor(x.color,s),x.bones=u.bones,x.vertices=u.vertices,x.worldVerticesLength=r<<1,x.triangles=c,x.regionUVs=l,null==f&&x.updateRegion(),x.hullLength=d<<1,x.sequence=f,n&&(x.edges=p,x.width=g*a,x.height=m*a),x):null}case De.LinkedMesh:{let e=t.readStringRef(),r=t.readInt32(),l=t.readStringRef(),c=t.readStringRef(),u=t.readBoolean(),d=this.readSequence(t),f=0,p=0;n&&(f=t.readFloat(),p=t.readFloat()),e||(e=o);let g=this.attachmentLoader.newMeshAttachment(i,o,e,d);return g?(g.path=e,h.rgba8888ToColor(g.color,r),g.sequence=d,n&&(g.width=f*a,g.height=p*a),this.linkedMeshes.push(new Oe(g,l,s,c,u)),g):null}case De.Path:{let e=t.readBoolean(),s=t.readBoolean(),r=t.readInt(!0),l=this.readVertices(t,r),c=g.newArray(r/3,0);for(let e=0,i=c.length;e<i;e++)c[e]=t.readFloat()*a;let u=n?t.readInt32():0,d=this.attachmentLoader.newPathAttachment(i,o);return d?(d.closed=e,d.constantSpeed=s,d.worldVerticesLength=r<<1,d.vertices=l.vertices,d.bones=l.bones,d.lengths=c,n&&h.rgba8888ToColor(d.color,u),d):null}case De.Point:{let e=t.readFloat(),s=t.readFloat(),r=t.readFloat(),l=n?t.readInt32():0,c=this.attachmentLoader.newPointAttachment(i,o);return c?(c.x=s*a,c.y=r*a,c.rotation=e,n&&h.rgba8888ToColor(c.color,l),c):null}case De.Clipping:{let s=t.readInt(!0),r=t.readInt(!0),a=this.readVertices(t,r),l=n?t.readInt32():0,c=this.attachmentLoader.newClippingAttachment(i,o);return c?(c.endSlot=e.slots[s],c.worldVerticesLength=r<<1,c.vertices=a.vertices,c.bones=a.bones,n&&h.rgba8888ToColor(c.color,l),c):null}}return null}readSequence(t){if(!t.readBoolean())return null;let e=new S(t.readInt(!0));return e.start=t.readInt(!0),e.digits=t.readInt(!0),e.setupIndex=t.readInt(!0),e}readVertices(t,e){let i=this.scale,s=e<<1,r=new Ye;if(!t.readBoolean())return r.vertices=this.readFloatArray(t,s,i),r;let n=new Array,a=new Array;for(let s=0;s<e;s++){let e=t.readInt(!0);a.push(e);for(let s=0;s<e;s++)a.push(t.readInt(!0)),n.push(t.readFloat()*i),n.push(t.readFloat()*i),n.push(t.readFloat())}return r.vertices=g.toFloatArray(n),r.bones=a,r}readFloatArray(t,e,i){let s=new Array(e);if(1==i)for(let i=0;i<e;i++)s[i]=t.readFloat();else for(let r=0;r<e;r++)s[r]=t.readFloat()*i;return s}readShortArray(t){let e=t.readInt(!0),i=new Array(e);for(let s=0;s<e;s++)i[s]=t.readShort();return i}readAnimation(t,e,i){t.readInt(!0);let s=new Array,r=this.scale;new h,new h;for(let e=0,i=t.readInt(!0);e<i;e++){let e=t.readInt(!0);for(let i=0,r=t.readInt(!0);i<r;i++){let i=t.readByte(),r=t.readInt(!0),n=r-1;switch(i){case Je:{let i=new mt(r,e);for(let e=0;e<r;e++)i.setFrame(e,t.readFloat(),t.readStringRef());s.push(i);break}case Qe:{let i=t.readInt(!0),a=new ut(r,i,e),o=t.readFloat(),h=t.readUnsignedByte()/255,l=t.readUnsignedByte()/255,c=t.readUnsignedByte()/255,u=t.readUnsignedByte()/255;for(let e=0,i=0;a.setFrame(e,o,h,l,c,u),e!=n;e++){let s=t.readFloat(),r=t.readUnsignedByte()/255,n=t.readUnsignedByte()/255,d=t.readUnsignedByte()/255,f=t.readUnsignedByte()/255;switch(t.readByte()){case hi:a.setStepped(e);break;case li:ze(t,a,i++,e,0,o,s,h,r,1),ze(t,a,i++,e,1,o,s,l,n,1),ze(t,a,i++,e,2,o,s,c,d,1),ze(t,a,i++,e,3,o,s,u,f,1)}o=s,h=r,l=n,c=d,u=f}s.push(a);break}case $e:{let i=t.readInt(!0),a=new dt(r,i,e),o=t.readFloat(),h=t.readUnsignedByte()/255,l=t.readUnsignedByte()/255,c=t.readUnsignedByte()/255;for(let e=0,i=0;a.setFrame(e,o,h,l,c),e!=n;e++){let s=t.readFloat(),r=t.readUnsignedByte()/255,n=t.readUnsignedByte()/255,u=t.readUnsignedByte()/255;switch(t.readByte()){case hi:a.setStepped(e);break;case li:ze(t,a,i++,e,0,o,s,h,r,1),ze(t,a,i++,e,1,o,s,l,n,1),ze(t,a,i++,e,2,o,s,c,u,1)}o=s,h=r,l=n,c=u}s.push(a);break}case ti:{let i=t.readInt(!0),a=new pt(r,i,e),o=t.readFloat(),h=t.readUnsignedByte()/255,l=t.readUnsignedByte()/255,c=t.readUnsignedByte()/255,u=t.readUnsignedByte()/255,d=t.readUnsignedByte()/255,f=t.readUnsignedByte()/255,p=t.readUnsignedByte()/255;for(let e=0,i=0;a.setFrame(e,o,h,l,c,u,d,f,p),e!=n;e++){let s=t.readFloat(),r=t.readUnsignedByte()/255,n=t.readUnsignedByte()/255,g=t.readUnsignedByte()/255,m=t.readUnsignedByte()/255,x=t.readUnsignedByte()/255,v=t.readUnsignedByte()/255,y=t.readUnsignedByte()/255;switch(t.readByte()){case hi:a.setStepped(e);break;case li:ze(t,a,i++,e,0,o,s,h,r,1),ze(t,a,i++,e,1,o,s,l,n,1),ze(t,a,i++,e,2,o,s,c,g,1),ze(t,a,i++,e,3,o,s,u,m,1),ze(t,a,i++,e,4,o,s,d,x,1),ze(t,a,i++,e,5,o,s,f,v,1),ze(t,a,i++,e,6,o,s,p,y,1)}o=s,h=r,l=n,c=g,u=m,d=x,f=v,p=y}s.push(a);break}case ei:{let i=t.readInt(!0),a=new gt(r,i,e),o=t.readFloat(),h=t.readUnsignedByte()/255,l=t.readUnsignedByte()/255,c=t.readUnsignedByte()/255,u=t.readUnsignedByte()/255,d=t.readUnsignedByte()/255,f=t.readUnsignedByte()/255;for(let e=0,i=0;a.setFrame(e,o,h,l,c,u,d,f),e!=n;e++){let s=t.readFloat(),r=t.readUnsignedByte()/255,n=t.readUnsignedByte()/255,p=t.readUnsignedByte()/255,g=t.readUnsignedByte()/255,m=t.readUnsignedByte()/255,x=t.readUnsignedByte()/255;switch(t.readByte()){case hi:a.setStepped(e);break;case li:ze(t,a,i++,e,0,o,s,h,r,1),ze(t,a,i++,e,1,o,s,l,n,1),ze(t,a,i++,e,2,o,s,c,p,1),ze(t,a,i++,e,3,o,s,u,g,1),ze(t,a,i++,e,4,o,s,d,m,1),ze(t,a,i++,e,5,o,s,f,x,1)}o=s,h=r,l=n,c=p,u=g,d=m,f=x}s.push(a);break}case ii:{let i=new ft(r,t.readInt(!0),e),a=t.readFloat(),o=t.readUnsignedByte()/255;for(let e=0,s=0;i.setFrame(e,a,o),e!=n;e++){let r=t.readFloat(),n=t.readUnsignedByte()/255;switch(t.readByte()){case hi:i.setStepped(e);break;case li:ze(t,i,s++,e,0,a,r,o,n,1)}a=r,o=n}s.push(i)}}}}for(let e=0,i=t.readInt(!0);e<i;e++){let e=t.readInt(!0);for(let i=0,n=t.readInt(!0);i<n;i++){let i=t.readByte(),n=t.readInt(!0),a=t.readInt(!0);switch(i){case Ne:s.push(Xe(t,new et(n,a,e),1));break;case Ve:s.push(Be(t,new it(n,a,e),r));break;case Ue:s.push(Xe(t,new st(n,a,e),r));break;case We:s.push(Xe(t,new rt(n,a,e),r));break;case qe:s.push(Be(t,new nt(n,a,e),1));break;case Ge:s.push(Xe(t,new at(n,a,e),1));break;case je:s.push(Xe(t,new ot(n,a,e),1));break;case He:s.push(Be(t,new ht(n,a,e),1));break;case Ze:s.push(Xe(t,new lt(n,a,e),1));break;case Ke:s.push(Xe(t,new ct(n,a,e),1))}}}for(let e=0,i=t.readInt(!0);e<i;e++){let e=t.readInt(!0),i=t.readInt(!0),n=i-1,a=new At(i,t.readInt(!0),e),o=t.readFloat(),h=t.readFloat(),l=t.readFloat()*r;for(let e=0,i=0;a.setFrame(e,o,h,l,t.readByte(),t.readBoolean(),t.readBoolean()),e!=n;e++){let s=t.readFloat(),n=t.readFloat(),c=t.readFloat()*r;switch(t.readByte()){case hi:a.setStepped(e);break;case li:ze(t,a,i++,e,0,o,s,h,n,1),ze(t,a,i++,e,1,o,s,l,c,r)}o=s,h=n,l=c}s.push(a)}for(let e=0,i=t.readInt(!0);e<i;e++){let e=t.readInt(!0),i=t.readInt(!0),r=i-1,n=new Mt(i,t.readInt(!0),e),a=t.readFloat(),o=t.readFloat(),h=t.readFloat(),l=t.readFloat(),c=t.readFloat(),u=t.readFloat(),d=t.readFloat();for(let e=0,i=0;n.setFrame(e,a,o,h,l,c,u,d),e!=r;e++){let s=t.readFloat(),r=t.readFloat(),f=t.readFloat(),p=t.readFloat(),g=t.readFloat(),m=t.readFloat(),x=t.readFloat();switch(t.readByte()){case hi:n.setStepped(e);break;case li:ze(t,n,i++,e,0,a,s,o,r,1),ze(t,n,i++,e,1,a,s,h,f,1),ze(t,n,i++,e,2,a,s,l,p,1),ze(t,n,i++,e,3,a,s,c,g,1),ze(t,n,i++,e,4,a,s,u,m,1),ze(t,n,i++,e,5,a,s,d,x,1)}a=s,o=r,h=f,l=p,c=g,u=m,d=x}s.push(n)}for(let e=0,n=t.readInt(!0);e<n;e++){let e=t.readInt(!0),n=i.pathConstraints[e];for(let i=0,a=t.readInt(!0);i<a;i++)switch(t.readByte()){case ni:s.push(Xe(t,new Et(t.readInt(!0),t.readInt(!0),e),0==n.positionMode?r:1));break;case ai:s.push(Xe(t,new St(t.readInt(!0),t.readInt(!0),e),0==n.spacingMode||1==n.spacingMode?r:1));break;case oi:let i=new Tt(t.readInt(!0),t.readInt(!0),e),a=t.readFloat(),o=t.readFloat(),h=t.readFloat(),l=t.readFloat();for(let e=0,s=0,r=i.getFrameCount()-1;i.setFrame(e,a,o,h,l),e!=r;e++){let r=t.readFloat(),n=t.readFloat(),c=t.readFloat(),u=t.readFloat();switch(t.readByte()){case hi:i.setStepped(e);break;case li:ze(t,i,s++,e,0,a,r,o,n,1),ze(t,i,s++,e,1,a,r,h,c,1),ze(t,i,s++,e,2,a,r,l,u,1)}a=r,o=n,h=c,l=u}s.push(i)}}for(let e=0,n=t.readInt(!0);e<n;e++){let e=i.skins[t.readInt(!0)];for(let i=0,n=t.readInt(!0);i<n;i++){let i=t.readInt(!0);for(let n=0,a=t.readInt(!0);n<a;n++){let n=t.readStringRef();if(!n)throw new Error("attachmentName must not be null.");let a=e.getAttachment(i,n),o=t.readByte(),h=t.readInt(!0),l=h-1;switch(o){case si:{let e=a,n=e.bones,o=e.vertices,c=n?o.length/3*2:o.length,u=t.readInt(!0),d=new xt(h,u,i,e),f=t.readFloat();for(let e=0,i=0;;e++){let s,a=t.readInt(!0);if(0==a)s=n?g.newFloatArray(c):o;else{s=g.newFloatArray(c);let e=t.readInt(!0);if(a+=e,1==r)for(let i=e;i<a;i++)s[i]=t.readFloat();else for(let i=e;i<a;i++)s[i]=t.readFloat()*r;if(!n)for(let t=0,e=s.length;t<e;t++)s[t]+=o[t]}if(d.setFrame(e,f,s),e==l)break;let h=t.readFloat();switch(t.readByte()){case hi:d.setStepped(e);break;case li:ze(t,d,i++,e,0,f,h,0,1,1)}f=h}s.push(d);break}case ri:{let e=new Ct(h,i,a);for(let i=0;i<h;i++){let s=t.readFloat(),r=t.readInt32();e.setFrame(i,s,I[15&r],r>>4,t.readFloat())}s.push(e);break}}}}}let n=t.readInt(!0);if(n>0){let e=new bt(n),r=i.slots.length;for(let i=0;i<n;i++){let s=t.readFloat(),n=t.readInt(!0),a=g.newArray(r,0);for(let t=r-1;t>=0;t--)a[t]=-1;let o=g.newArray(r-n,0),h=0,l=0;for(let e=0;e<n;e++){let e=t.readInt(!0);for(;h!=e;)o[l++]=h++;a[h+t.readInt(!0)]=h++}for(;h<r;)o[l++]=h++;for(let t=r-1;t>=0;t--)-1==a[t]&&(a[t]=o[--l]);e.setFrame(i,s,a)}s.push(e)}let a=t.readInt(!0);if(a>0){let e=new yt(a);for(let s=0;s<a;s++){let r=t.readFloat(),n=i.events[t.readInt(!0)],a=new de(r,n);a.intValue=t.readInt(!1),a.floatValue=t.readFloat(),a.stringValue=t.readBoolean()?t.readString():n.stringValue,a.data.audioPath&&(a.volume=t.readFloat(),a.balance=t.readFloat()),e.setFrame(s,a)}s.push(e)}let o=0;for(let t=0,e=s.length;t<e;t++)o=Math.max(o,s[t].getDuration());return new C(e,s,o)}},Pe=class{constructor(t,e=new Array,i=0,s=new DataView(t.buffer)){this.strings=e,this.index=i,this.buffer=s}readByte(){return this.buffer.getInt8(this.index++)}readUnsignedByte(){return this.buffer.getUint8(this.index++)}readShort(){let t=this.buffer.getInt16(this.index);return this.index+=2,t}readInt32(){let t=this.buffer.getInt32(this.index);return this.index+=4,t}readInt(t){let e=this.readByte(),i=127&e;return 0!=(128&e)&&(e=this.readByte(),i|=(127&e)<<7,0!=(128&e)&&(e=this.readByte(),i|=(127&e)<<14,0!=(128&e)&&(e=this.readByte(),i|=(127&e)<<21,0!=(128&e)&&(e=this.readByte(),i|=(127&e)<<28)))),t?i:i>>>1^-(1&i)}readStringRef(){let t=this.readInt(!0);return 0==t?null:this.strings[t-1]}readString(){let t=this.readInt(!0);switch(t){case 0:return null;case 1:return""}t--;let e="";for(let i=0;i<t;){let t=this.readUnsignedByte();switch(t>>4){case 12:case 13:e+=String.fromCharCode((31&t)<<6|63&this.readByte()),i+=2;break;case 14:e+=String.fromCharCode((15&t)<<12|(63&this.readByte())<<6|63&this.readByte()),i+=3;break;default:e+=String.fromCharCode(t),i++}}return e}readFloat(){let t=this.buffer.getFloat32(this.index);return this.index+=4,t}readBoolean(){return 0!=this.readByte()}},Oe=class{constructor(t,e,i,s,r){this.mesh=t,this.skin=e,this.slotIndex=i,this.parent=s,this.inheritTimeline=r}},Ye=class{constructor(t=null,e=null){this.bones=t,this.vertices=e}},De=(t=>(t[t.Region=0]="Region",t[t.BoundingBox=1]="BoundingBox",t[t.Mesh=2]="Mesh",t[t.LinkedMesh=3]="LinkedMesh",t[t.Path=4]="Path",t[t.Point=5]="Point",t[t.Clipping=6]="Clipping",t))(De||{});function Xe(t,e,i){let s=t.readFloat(),r=t.readFloat()*i;for(let n=0,a=0,o=e.getFrameCount()-1;e.setFrame(n,s,r),n!=o;n++){let o=t.readFloat(),h=t.readFloat()*i;switch(t.readByte()){case hi:e.setStepped(n);break;case li:ze(t,e,a++,n,0,s,o,r,h,i)}s=o,r=h}return e}function Be(t,e,i){let s=t.readFloat(),r=t.readFloat()*i,n=t.readFloat()*i;for(let a=0,o=0,h=e.getFrameCount()-1;e.setFrame(a,s,r,n),a!=h;a++){let h=t.readFloat(),l=t.readFloat()*i,c=t.readFloat()*i;switch(t.readByte()){case hi:e.setStepped(a);break;case li:ze(t,e,o++,a,0,s,h,r,l,i),ze(t,e,o++,a,1,s,h,n,c,i)}s=h,r=l,n=c}return e}function ze(t,e,i,s,r,n,a,o,h,l){e.setBezier(i,s,r,n,o,t.readFloat(),t.readFloat()*l,t.readFloat(),t.readFloat()*l,a,h)}var _e,Ne=0,Ve=1,Ue=2,We=3,qe=4,Ge=5,je=6,He=7,Ze=8,Ke=9,Je=0,Qe=1,$e=2,ti=3,ei=4,ii=5,si=0,ri=1,ni=0,ai=1,oi=2,hi=1,li=2,ci=class{constructor(){this.minX=0,this.minY=0,this.maxX=0,this.maxY=0,this.boundingBoxes=new Array,this.polygons=new Array,this.polygonPool=new x((()=>g.newFloatArray(16)))}update(t,e){if(!t)throw new Error("skeleton cannot be null.");let i=this.boundingBoxes,s=this.polygons,r=this.polygonPool,n=t.slots,a=n.length;i.length=0,r.freeAll(s),s.length=0;for(let t=0;t<a;t++){let e=n[t];if(!e.bone.active)continue;let a=e.getAttachment();if(a instanceof Ut){let t=a;i.push(t);let n=r.obtain();n.length!=t.worldVerticesLength&&(n=g.newFloatArray(t.worldVerticesLength)),s.push(n),t.computeWorldVertices(e,0,t.worldVerticesLength,n,0,2)}}e?this.aabbCompute():(this.minX=Number.POSITIVE_INFINITY,this.minY=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.maxY=Number.NEGATIVE_INFINITY)}aabbCompute(){let t=Number.POSITIVE_INFINITY,e=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY,s=Number.NEGATIVE_INFINITY,r=this.polygons;for(let n=0,a=r.length;n<a;n++){let a=r[n],o=a;for(let r=0,n=a.length;r<n;r+=2){let n=o[r],a=o[r+1];t=Math.min(t,n),e=Math.min(e,a),i=Math.max(i,n),s=Math.max(s,a)}}this.minX=t,this.minY=e,this.maxX=i,this.maxY=s}aabbContainsPoint(t,e){return t>=this.minX&&t<=this.maxX&&e>=this.minY&&e<=this.maxY}aabbIntersectsSegment(t,e,i,s){let r=this.minX,n=this.minY,a=this.maxX,o=this.maxY;if(t<=r&&i<=r||e<=n&&s<=n||t>=a&&i>=a||e>=o&&s>=o)return!1;let h=(s-e)/(i-t),l=h*(r-t)+e;if(l>n&&l<o)return!0;if(l=h*(a-t)+e,l>n&&l<o)return!0;let c=(n-e)/h+t;return c>r&&c<a||(c=(o-e)/h+t,c>r&&c<a)}aabbIntersectsSkeleton(t){return this.minX<t.maxX&&this.maxX>t.minX&&this.minY<t.maxY&&this.maxY>t.minY}containsPoint(t,e){let i=this.polygons;for(let s=0,r=i.length;s<r;s++)if(this.containsPointPolygon(i[s],t,e))return this.boundingBoxes[s];return null}containsPointPolygon(t,e,i){let s=t,r=t.length,n=r-2,a=!1;for(let t=0;t<r;t+=2){let r=s[t+1],o=s[n+1];if(r<i&&o>=i||o<i&&r>=i){let h=s[t];h+(i-r)/(o-r)*(s[n]-h)<e&&(a=!a)}n=t}return a}intersectsSegment(t,e,i,s){let r=this.polygons;for(let n=0,a=r.length;n<a;n++)if(this.intersectsSegmentPolygon(r[n],t,e,i,s))return this.boundingBoxes[n];return null}intersectsSegmentPolygon(t,e,i,s,r){let n=t,a=t.length,o=e-s,h=i-r,l=e*r-i*s,c=n[a-2],u=n[a-1];for(let t=0;t<a;t+=2){let a=n[t],d=n[t+1],f=c*d-u*a,p=c-a,g=u-d,m=o*g-h*p,x=(l*p-o*f)/m;if((x>=c&&x<=a||x>=a&&x<=c)&&(x>=e&&x<=s||x>=s&&x<=e)){let t=(l*g-h*f)/m;if((t>=u&&t<=d||t>=d&&t<=u)&&(t>=i&&t<=r||t>=r&&t<=i))return!0}c=a,u=d}return!1}getPolygon(t){if(!t)throw new Error("boundingBox cannot be null.");let e=this.boundingBoxes.indexOf(t);return-1==e?null:this.polygons[e]}getWidth(){return this.maxX-this.minX}getHeight(){return this.maxY-this.minY}},ui=class{constructor(){this.convexPolygons=new Array,this.convexPolygonsIndices=new Array,this.indicesArray=new Array,this.isConcaveArray=new Array,this.triangles=new Array,this.polygonPool=new x((()=>new Array)),this.polygonIndicesPool=new x((()=>new Array))}triangulate(t){let e=t,i=t.length>>1,s=this.indicesArray;s.length=0;for(let t=0;t<i;t++)s[t]=t;let r=this.isConcaveArray;r.length=0;for(let t=0,n=i;t<n;++t)r[t]=ui.isConcave(t,i,e,s);let n=this.triangles;for(n.length=0;i>3;){let t=i-1,a=0,o=1;for(;;){t:if(!r[a]){let n=s[t]<<1,h=s[a]<<1,l=s[o]<<1,c=e[n],u=e[n+1],d=e[h],f=e[h+1],p=e[l],g=e[l+1];for(let n=(o+1)%i;n!=t;n=(n+1)%i){if(!r[n])continue;let t=s[n]<<1,i=e[t],a=e[t+1];if(ui.positiveArea(p,g,c,u,i,a)&&ui.positiveArea(c,u,d,f,i,a)&&ui.positiveArea(d,f,p,g,i,a))break t}break}if(0==o){do{if(!r[a])break;a--}while(a>0);break}t=a,a=o,o=(o+1)%i}n.push(s[(i+a-1)%i]),n.push(s[a]),n.push(s[(a+1)%i]),s.splice(a,1),r.splice(a,1),i--;let h=(i+a-1)%i,l=a==i?0:a;r[h]=ui.isConcave(h,i,e,s),r[l]=ui.isConcave(l,i,e,s)}return 3==i&&(n.push(s[2]),n.push(s[0]),n.push(s[1])),n}decompose(t,e){let i=t,s=this.convexPolygons;this.polygonPool.freeAll(s),s.length=0;let r=this.convexPolygonsIndices;this.polygonIndicesPool.freeAll(r),r.length=0;let n=this.polygonIndicesPool.obtain();n.length=0;let a=this.polygonPool.obtain();a.length=0;let o=-1,h=0;for(let t=0,l=e.length;t<l;t+=3){let l=e[t]<<1,c=e[t+1]<<1,u=e[t+2]<<1,d=i[l],f=i[l+1],p=i[c],g=i[c+1],m=i[u],x=i[u+1],v=!1;if(o==l){let t=a.length-4,e=ui.winding(a[t],a[t+1],a[t+2],a[t+3],m,x),i=ui.winding(m,x,a[0],a[1],a[2],a[3]);e==h&&i==h&&(a.push(m),a.push(x),n.push(u),v=!0)}v||(a.length>0?(s.push(a),r.push(n)):(this.polygonPool.free(a),this.polygonIndicesPool.free(n)),a=this.polygonPool.obtain(),a.length=0,a.push(d),a.push(f),a.push(p),a.push(g),a.push(m),a.push(x),n=this.polygonIndicesPool.obtain(),n.length=0,n.push(l),n.push(c),n.push(u),h=ui.winding(d,f,p,g,m,x),o=l)}a.length>0&&(s.push(a),r.push(n));for(let t=0,e=s.length;t<e;t++){if(n=r[t],0==n.length)continue;let i=n[0],o=n[n.length-1];a=s[t];let h=a.length-4,l=a[h],c=a[h+1],u=a[h+2],d=a[h+3],f=a[0],p=a[1],g=a[2],m=a[3],x=ui.winding(l,c,u,d,f,p);for(let h=0;h<e;h++){if(h==t)continue;let e=r[h];if(3!=e.length)continue;let v=e[0],y=e[1],w=e[2],b=s[h],A=b[b.length-2],M=b[b.length-1];if(v!=i||y!=o)continue;let E=ui.winding(l,c,u,d,A,M),S=ui.winding(A,M,f,p,g,m);E==x&&S==x&&(b.length=0,e.length=0,a.push(A),a.push(M),n.push(w),l=u,c=d,u=A,d=M,h=0)}}for(let t=s.length-1;t>=0;t--)a=s[t],0==a.length&&(s.splice(t,1),this.polygonPool.free(a),n=r[t],r.splice(t,1),this.polygonIndicesPool.free(n));return s}static isConcave(t,e,i,s){let r=s[(e+t-1)%e]<<1,n=s[t]<<1,a=s[(t+1)%e]<<1;return!this.positiveArea(i[r],i[r+1],i[n],i[n+1],i[a],i[a+1])}static positiveArea(t,e,i,s,r,n){return t*(n-s)+i*(e-n)+r*(s-e)>=0}static winding(t,e,i,s,r,n){let a=i-t,o=s-e;return r*o-n*a+a*e-t*o>=0?1:-1}},di=class{constructor(){this.triangulator=new ui,this.clippingPolygon=new Array,this.clipOutput=new Array,this.clippedVertices=new Array,this.clippedTriangles=new Array,this.scratch=new Array,this.clipAttachment=null,this.clippingPolygons=null}clipStart(t,e){if(this.clipAttachment)return 0;this.clipAttachment=e;let i=e.worldVerticesLength,s=g.setArraySize(this.clippingPolygon,i);e.computeWorldVertices(t,0,i,s,0,2);let r=this.clippingPolygon;di.makeClockwise(r);let n=this.clippingPolygons=this.triangulator.decompose(r,this.triangulator.triangulate(r));for(let t=0,e=n.length;t<e;t++){let e=n[t];di.makeClockwise(e),e.push(e[0]),e.push(e[1])}return n.length}clipEndWithSlot(t){this.clipAttachment&&this.clipAttachment.endSlot==t.data&&this.clipEnd()}clipEnd(){this.clipAttachment&&(this.clipAttachment=null,this.clippingPolygons=null,this.clippedVertices.length=0,this.clippedTriangles.length=0,this.clippingPolygon.length=0)}isClipping(){return null!=this.clipAttachment}clipTriangles(t,e,i,s,r,n,a,o){let h=this.clipOutput,l=this.clippedVertices,c=this.clippedTriangles,u=this.clippingPolygons,d=u.length,f=o?12:8,p=0;l.length=0,c.length=0;t:for(let e=0;e<s;e+=3){let s=i[e]<<1,m=t[s],x=t[s+1],v=r[s],y=r[s+1];s=i[e+1]<<1;let w=t[s],b=t[s+1],A=r[s],M=r[s+1];s=i[e+2]<<1;let E=t[s],S=t[s+1],T=r[s],I=r[s+1];for(let t=0;t<d;t++){let e=l.length;if(!this.clip(m,x,w,b,E,S,u[t],h)){let t=g.setArraySize(l,e+3*f);t[e]=m,t[e+1]=x,t[e+2]=n.r,t[e+3]=n.g,t[e+4]=n.b,t[e+5]=n.a,o?(t[e+6]=v,t[e+7]=y,t[e+8]=a.r,t[e+9]=a.g,t[e+10]=a.b,t[e+11]=a.a,t[e+12]=w,t[e+13]=b,t[e+14]=n.r,t[e+15]=n.g,t[e+16]=n.b,t[e+17]=n.a,t[e+18]=A,t[e+19]=M,t[e+20]=a.r,t[e+21]=a.g,t[e+22]=a.b,t[e+23]=a.a,t[e+24]=E,t[e+25]=S,t[e+26]=n.r,t[e+27]=n.g,t[e+28]=n.b,t[e+29]=n.a,t[e+30]=T,t[e+31]=I,t[e+32]=a.r,t[e+33]=a.g,t[e+34]=a.b,t[e+35]=a.a):(t[e+6]=v,t[e+7]=y,t[e+8]=w,t[e+9]=b,t[e+10]=n.r,t[e+11]=n.g,t[e+12]=n.b,t[e+13]=n.a,t[e+14]=A,t[e+15]=M,t[e+16]=E,t[e+17]=S,t[e+18]=n.r,t[e+19]=n.g,t[e+20]=n.b,t[e+21]=n.a,t[e+22]=T,t[e+23]=I),e=c.length;let i=g.setArraySize(c,e+3);i[e]=p,i[e+1]=p+1,i[e+2]=p+2,p+=3;continue t}{let t=h.length;if(0==t)continue;let i=b-S,s=E-w,r=m-E,u=S-x,d=1/(i*r+s*(x-S)),C=t>>1,R=this.clipOutput,k=g.setArraySize(l,e+C*f);for(let h=0;h<t;h+=2){let t=R[h],l=R[h+1];k[e]=t,k[e+1]=l,k[e+2]=n.r,k[e+3]=n.g,k[e+4]=n.b,k[e+5]=n.a;let c=t-E,p=l-S,g=(i*c+s*p)*d,m=(u*c+r*p)*d,x=1-g-m;k[e+6]=v*g+A*m+T*x,k[e+7]=y*g+M*m+I*x,o&&(k[e+8]=a.r,k[e+9]=a.g,k[e+10]=a.b,k[e+11]=a.a),e+=f}e=c.length;let F=g.setArraySize(c,e+3*(C-2));C--;for(let t=1;t<C;t++)F[e]=p,F[e+1]=p+t,F[e+2]=p+t+1,e+=3;p+=C+1}}}}clip(t,e,i,s,r,n,a,o){let h,l=o,c=!1;a.length%4>=2?(h=o,o=this.scratch):h=this.scratch,h.length=0,h.push(t),h.push(e),h.push(i),h.push(s),h.push(r),h.push(n),h.push(t),h.push(e),o.length=0;let u=a,d=a.length-4;for(let t=0;;t+=2){let e=u[t],i=u[t+1],s=u[t+2],r=u[t+3],n=e-s,a=i-r,f=h,p=h.length-2,g=o.length;for(let t=0;t<p;t+=2){let h=f[t],l=f[t+1],u=f[t+2],d=f[t+3],p=n*(d-r)-a*(u-s)>0;if(n*(l-r)-a*(h-s)>0){if(p){o.push(u),o.push(d);continue}let t=d-l,n=u-h,a=t*(s-e)-n*(r-i);if(Math.abs(a)>1e-6){let c=(n*(i-l)-t*(e-h))/a;o.push(e+(s-e)*c),o.push(i+(r-i)*c)}else o.push(e),o.push(i)}else if(p){let t=d-l,n=u-h,a=t*(s-e)-n*(r-i);if(Math.abs(a)>1e-6){let c=(n*(i-l)-t*(e-h))/a;o.push(e+(s-e)*c),o.push(i+(r-i)*c)}else o.push(e),o.push(i);o.push(u),o.push(d)}c=!0}if(g==o.length)return l.length=0,!0;if(o.push(o[0]),o.push(o[1]),t==d)break;let m=o;(o=h).length=0,h=m}if(l!=o){l.length=0;for(let t=0,e=o.length-2;t<e;t++)l[t]=o[t]}else l.length=l.length-2;return c}static makeClockwise(t){let e=t,i=t.length,s=e[i-2]*e[1]-e[0]*e[i-1],r=0,n=0,a=0,o=0;for(let t=0,h=i-3;t<h;t+=2)r=e[t],n=e[t+1],a=e[t+2],o=e[t+3],s+=r*o-a*n;if(!(s<0))for(let t=0,s=i-2,r=i>>1;t<r;t+=2){let i=e[t],r=e[t+1],n=s-t;e[t]=e[n],e[t+1]=e[n+1],e[n]=i,e[n+1]=r}}},fi=class{constructor(t){this.scale=1,this.linkedMeshes=new Array,this.attachmentLoader=t}readSkeletonData(t){let e=this.scale,i=new Te,s="string"==typeof t?JSON.parse(t):t,r=s.skeleton;if(r&&(i.hash=r.hash,i.version=r.spine,i.x=r.x,i.y=r.y,i.width=r.width,i.height=r.height,i.fps=r.fps,i.imagesPath=r.images),s.bones)for(let t=0;t<s.bones.length;t++){let r=s.bones[t],n=null,a=vi(r,"parent",null);a&&(n=i.findBone(a));let o=new ae(i.bones.length,r.name,n);o.length=vi(r,"length",0)*e,o.x=vi(r,"x",0)*e,o.y=vi(r,"y",0)*e,o.rotation=vi(r,"rotation",0),o.scaleX=vi(r,"scaleX",1),o.scaleY=vi(r,"scaleY",1),o.shearX=vi(r,"shearX",0),o.shearY=vi(r,"shearY",0),o.transformMode=g.enumValue(oe,vi(r,"transform","Normal")),o.skinRequired=vi(r,"skin",!1);let h=vi(r,"color",null);h&&o.color.setFromString(h),i.bones.push(o)}if(s.slots)for(let t=0;t<s.slots.length;t++){let e=s.slots[t],r=i.findBone(e.bone);if(!r)throw new Error(`Couldn't find bone ${e.bone} for slot ${e.name}`);let n=new Re(i.slots.length,e.name,r),a=vi(e,"color",null);a&&n.color.setFromString(a);let o=vi(e,"dark",null);o&&(n.darkColor=h.fromString(o)),n.attachmentName=vi(e,"attachment",null),n.blendMode=g.enumValue(ke,vi(e,"blend","normal")),i.slots.push(n)}if(s.ik)for(let t=0;t<s.ik.length;t++){let r=s.ik[t],n=new ge(r.name);n.order=vi(r,"order",0),n.skinRequired=vi(r,"skin",!1);for(let t=0;t<r.bones.length;t++){let e=i.findBone(r.bones[t]);if(!e)throw new Error(`Couldn't find bone ${r.bones[t]} for IK constraint ${r.name}.`);n.bones.push(e)}let a=i.findBone(r.target);if(!a)throw new Error(`Couldn't find target bone ${r.target} for IK constraint ${r.name}.`);n.target=a,n.mix=vi(r,"mix",1),n.softness=vi(r,"softness",0)*e,n.bendDirection=vi(r,"bendPositive",!0)?1:-1,n.compress=vi(r,"compress",!1),n.stretch=vi(r,"stretch",!1),n.uniform=vi(r,"uniform",!1),i.ikConstraints.push(n)}if(s.transform)for(let t=0;t<s.transform.length;t++){let r=s.transform[t],n=new Fe(r.name);n.order=vi(r,"order",0),n.skinRequired=vi(r,"skin",!1);for(let t=0;t<r.bones.length;t++){let e=r.bones[t],s=i.findBone(e);if(!s)throw new Error(`Couldn't find bone ${e} for transform constraint ${r.name}.`);n.bones.push(s)}let a=r.target,o=i.findBone(a);if(!o)throw new Error(`Couldn't find target bone ${a} for transform constraint ${r.name}.`);n.target=o,n.local=vi(r,"local",!1),n.relative=vi(r,"relative",!1),n.offsetRotation=vi(r,"rotation",0),n.offsetX=vi(r,"x",0)*e,n.offsetY=vi(r,"y",0)*e,n.offsetScaleX=vi(r,"scaleX",0),n.offsetScaleY=vi(r,"scaleY",0),n.offsetShearY=vi(r,"shearY",0),n.mixRotate=vi(r,"mixRotate",1),n.mixX=vi(r,"mixX",1),n.mixY=vi(r,"mixY",n.mixX),n.mixScaleX=vi(r,"mixScaleX",1),n.mixScaleY=vi(r,"mixScaleY",n.mixScaleX),n.mixShearY=vi(r,"mixShearY",1),i.transformConstraints.push(n)}if(s.path)for(let t=0;t<s.path.length;t++){let r=s.path[t],n=new me(r.name);n.order=vi(r,"order",0),n.skinRequired=vi(r,"skin",!1);for(let t=0;t<r.bones.length;t++){let e=r.bones[t],s=i.findBone(e);if(!s)throw new Error(`Couldn't find bone ${e} for path constraint ${r.name}.`);n.bones.push(s)}let a=r.target,o=i.findSlot(a);if(!o)throw new Error(`Couldn't find target slot ${a} for path constraint ${r.name}.`);n.target=o,n.positionMode=g.enumValue(xe,vi(r,"positionMode","Percent")),n.spacingMode=g.enumValue(ve,vi(r,"spacingMode","Length")),n.rotateMode=g.enumValue(ye,vi(r,"rotateMode","Tangent")),n.offsetRotation=vi(r,"rotation",0),n.position=vi(r,"position",0),0==n.positionMode&&(n.position*=e),n.spacing=vi(r,"spacing",0),0!=n.spacingMode&&1!=n.spacingMode||(n.spacing*=e),n.mixRotate=vi(r,"mixRotate",1),n.mixX=vi(r,"mixX",1),n.mixY=vi(r,"mixY",n.mixX),i.pathConstraints.push(n)}if(s.skins)for(let t=0;t<s.skins.length;t++){let e=s.skins[t],r=new Ce(e.name);if(e.bones)for(let t=0;t<e.bones.length;t++){let s=e.bones[t],n=i.findBone(s);if(!n)throw new Error(`Couldn't find bone ${s} for skin ${e.name}.`);r.bones.push(n)}if(e.ik)for(let t=0;t<e.ik.length;t++){let s=e.ik[t],n=i.findIkConstraint(s);if(!n)throw new Error(`Couldn't find IK constraint ${s} for skin ${e.name}.`);r.constraints.push(n)}if(e.transform)for(let t=0;t<e.transform.length;t++){let s=e.transform[t],n=i.findTransformConstraint(s);if(!n)throw new Error(`Couldn't find transform constraint ${s} for skin ${e.name}.`);r.constraints.push(n)}if(e.path)for(let t=0;t<e.path.length;t++){let s=e.path[t],n=i.findPathConstraint(s);if(!n)throw new Error(`Couldn't find path constraint ${s} for skin ${e.name}.`);r.constraints.push(n)}for(let t in e.attachments){let s=i.findSlot(t);if(!s)throw new Error(`Couldn't find slot ${t} for skin ${e.name}.`);let n=e.attachments[t];for(let t in n){let e=this.readAttachment(n[t],r,s.index,t,i);e&&r.setAttachment(s.index,t,e)}}i.skins.push(r),"default"==r.name&&(i.defaultSkin=r)}for(let t=0,e=this.linkedMeshes.length;t<e;t++){let e=this.linkedMeshes[t],s=e.skin?i.findSkin(e.skin):i.defaultSkin;if(!s)throw new Error(`Skin not found: ${e.skin}`);let r=s.getAttachment(e.slotIndex,e.parent);if(!r)throw new Error(`Parent mesh not found: ${e.parent}`);e.mesh.timelineAttachment=e.inheritTimeline?r:e.mesh,e.mesh.setParentMesh(r),null!=e.mesh.region&&e.mesh.updateRegion()}if(this.linkedMeshes.length=0,s.events)for(let t in s.events){let e=s.events[t],r=new fe(t);r.intValue=vi(e,"int",0),r.floatValue=vi(e,"float",0),r.stringValue=vi(e,"string",""),r.audioPath=vi(e,"audio",null),r.audioPath&&(r.volume=vi(e,"volume",1),r.balance=vi(e,"balance",0)),i.events.push(r)}if(s.animations)for(let t in s.animations){let e=s.animations[t];this.readAnimation(e,t,i)}return i}readAttachment(t,e,i,s,r){let n=this.scale;switch(s=vi(t,"name",s),vi(t,"type","region")){case"region":{let i=vi(t,"path",s),r=this.readSequence(vi(t,"sequence",null)),a=this.attachmentLoader.newRegionAttachment(e,s,i,r);if(!a)return null;a.path=i,a.x=vi(t,"x",0)*n,a.y=vi(t,"y",0)*n,a.scaleX=vi(t,"scaleX",1),a.scaleY=vi(t,"scaleY",1),a.rotation=vi(t,"rotation",0),a.width=t.width*n,a.height=t.height*n,a.sequence=r;let o=vi(t,"color",null);return o&&a.color.setFromString(o),null!=a.region&&a.updateRegion(),a}case"boundingbox":{let i=this.attachmentLoader.newBoundingBoxAttachment(e,s);if(!i)return null;this.readVertices(t,i,t.vertexCount<<1);let r=vi(t,"color",null);return r&&i.color.setFromString(r),i}case"mesh":case"linkedmesh":{let r=vi(t,"path",s),a=this.readSequence(vi(t,"sequence",null)),o=this.attachmentLoader.newMeshAttachment(e,s,r,a);if(!o)return null;o.path=r;let h=vi(t,"color",null);h&&o.color.setFromString(h),o.width=vi(t,"width",0)*n,o.height=vi(t,"height",0)*n,o.sequence=a;let l=vi(t,"parent",null);if(l)return this.linkedMeshes.push(new pi(o,vi(t,"skin",null),i,l,vi(t,"timelines",!0))),o;let c=t.uvs;return this.readVertices(t,o,c.length),o.triangles=t.triangles,o.regionUVs=c,null!=o.region&&o.updateRegion(),o.edges=vi(t,"edges",null),o.hullLength=2*vi(t,"hull",0),o}case"path":{let i=this.attachmentLoader.newPathAttachment(e,s);if(!i)return null;i.closed=vi(t,"closed",!1),i.constantSpeed=vi(t,"constantSpeed",!0);let r=t.vertexCount;this.readVertices(t,i,r<<1);let a=g.newArray(r/3,0);for(let e=0;e<t.lengths.length;e++)a[e]=t.lengths[e]*n;i.lengths=a;let o=vi(t,"color",null);return o&&i.color.setFromString(o),i}case"point":{let i=this.attachmentLoader.newPointAttachment(e,s);if(!i)return null;i.x=vi(t,"x",0)*n,i.y=vi(t,"y",0)*n,i.rotation=vi(t,"rotation",0);let r=vi(t,"color",null);return r&&i.color.setFromString(r),i}case"clipping":{let i=this.attachmentLoader.newClippingAttachment(e,s);if(!i)return null;let n=vi(t,"end",null);n&&(i.endSlot=r.findSlot(n));let a=t.vertexCount;this.readVertices(t,i,a<<1);let o=vi(t,"color",null);return o&&i.color.setFromString(o),i}}return null}readSequence(t){if(null==t)return null;let e=new S(vi(t,"count",0));return e.start=vi(t,"start",1),e.digits=vi(t,"digits",0),e.setupIndex=vi(t,"setup",0),e}readVertices(t,e,i){let s=this.scale;e.worldVerticesLength=i;let r=t.vertices;if(i==r.length){let t=g.toFloatArray(r);if(1!=s)for(let e=0,i=r.length;e<i;e++)t[e]*=s;return void(e.vertices=t)}let n=new Array,a=new Array;for(let t=0,e=r.length;t<e;){let e=r[t++];a.push(e);for(let i=t+4*e;t<i;t+=4)a.push(r[t]),n.push(r[t+1]*s),n.push(r[t+2]*s),n.push(r[t+3])}e.bones=a,e.vertices=g.toFloatArray(n)}readAnimation(t,e,i){let s=this.scale,r=new Array;if(t.slots)for(let e in t.slots){let s=t.slots[e],n=i.findSlot(e);if(!n)throw new Error("Slot not found: "+e);let a=n.index;for(let t in s){let e=s[t];if(!e)continue;let i=e.length;if("attachment"==t){let t=new mt(i,a);for(let s=0;s<i;s++){let i=e[s];t.setFrame(s,vi(i,"time",0),vi(i,"name",null))}r.push(t)}else if("rgba"==t){let t=new ut(i,i<<2,a),s=e[0],n=vi(s,"time",0),o=h.fromString(s.color);for(let i=0,r=0;;i++){t.setFrame(i,n,o.r,o.g,o.b,o.a);let a=e[i+1];if(!a){t.shrink(r);break}let l=vi(a,"time",0),c=h.fromString(a.color),u=s.curve;u&&(r=xi(u,t,r,i,0,n,l,o.r,c.r,1),r=xi(u,t,r,i,1,n,l,o.g,c.g,1),r=xi(u,t,r,i,2,n,l,o.b,c.b,1),r=xi(u,t,r,i,3,n,l,o.a,c.a,1)),n=l,o=c,s=a}r.push(t)}else if("rgb"==t){let t=new dt(i,3*i,a),s=e[0],n=vi(s,"time",0),o=h.fromString(s.color);for(let i=0,r=0;;i++){t.setFrame(i,n,o.r,o.g,o.b);let a=e[i+1];if(!a){t.shrink(r);break}let l=vi(a,"time",0),c=h.fromString(a.color),u=s.curve;u&&(r=xi(u,t,r,i,0,n,l,o.r,c.r,1),r=xi(u,t,r,i,1,n,l,o.g,c.g,1),r=xi(u,t,r,i,2,n,l,o.b,c.b,1)),n=l,o=c,s=a}r.push(t)}else if("alpha"==t)r.push(gi(e,new ft(i,i,a),0,1));else if("rgba2"==t){let t=new pt(i,7*i,a),s=e[0],n=vi(s,"time",0),o=h.fromString(s.light),l=h.fromString(s.dark);for(let i=0,r=0;;i++){t.setFrame(i,n,o.r,o.g,o.b,o.a,l.r,l.g,l.b);let a=e[i+1];if(!a){t.shrink(r);break}let c=vi(a,"time",0),u=h.fromString(a.light),d=h.fromString(a.dark),f=s.curve;f&&(r=xi(f,t,r,i,0,n,c,o.r,u.r,1),r=xi(f,t,r,i,1,n,c,o.g,u.g,1),r=xi(f,t,r,i,2,n,c,o.b,u.b,1),r=xi(f,t,r,i,3,n,c,o.a,u.a,1),r=xi(f,t,r,i,4,n,c,l.r,d.r,1),r=xi(f,t,r,i,5,n,c,l.g,d.g,1),r=xi(f,t,r,i,6,n,c,l.b,d.b,1)),n=c,o=u,l=d,s=a}r.push(t)}else if("rgb2"==t){let t=new gt(i,6*i,a),s=e[0],n=vi(s,"time",0),o=h.fromString(s.light),l=h.fromString(s.dark);for(let i=0,r=0;;i++){t.setFrame(i,n,o.r,o.g,o.b,l.r,l.g,l.b);let a=e[i+1];if(!a){t.shrink(r);break}let c=vi(a,"time",0),u=h.fromString(a.light),d=h.fromString(a.dark),f=s.curve;f&&(r=xi(f,t,r,i,0,n,c,o.r,u.r,1),r=xi(f,t,r,i,1,n,c,o.g,u.g,1),r=xi(f,t,r,i,2,n,c,o.b,u.b,1),r=xi(f,t,r,i,3,n,c,l.r,d.r,1),r=xi(f,t,r,i,4,n,c,l.g,d.g,1),r=xi(f,t,r,i,5,n,c,l.b,d.b,1)),n=c,o=u,l=d,s=a}r.push(t)}}}if(t.bones)for(let e in t.bones){let n=t.bones[e],a=i.findBone(e);if(!a)throw new Error("Bone not found: "+e);let o=a.index;for(let t in n){let e=n[t],i=e.length;if(0!=i)if("rotate"===t)r.push(gi(e,new et(i,i,o),0,1));else if("translate"===t){let t=new it(i,i<<1,o);r.push(mi(e,t,"x","y",0,s))}else if("translatex"===t){let t=new st(i,i,o);r.push(gi(e,t,0,s))}else if("translatey"===t){let t=new rt(i,i,o);r.push(gi(e,t,0,s))}else if("scale"===t){let t=new nt(i,i<<1,o);r.push(mi(e,t,"x","y",1,1))}else if("scalex"===t){let t=new at(i,i,o);r.push(gi(e,t,1,1))}else if("scaley"===t){let t=new ot(i,i,o);r.push(gi(e,t,1,1))}else if("shear"===t){let t=new ht(i,i<<1,o);r.push(mi(e,t,"x","y",0,1))}else if("shearx"===t){let t=new lt(i,i,o);r.push(gi(e,t,0,1))}else if("sheary"===t){let t=new ct(i,i,o);r.push(gi(e,t,0,1))}}}if(t.ik)for(let e in t.ik){let n=t.ik[e],a=n[0];if(!a)continue;let o=i.findIkConstraint(e);if(!o)throw new Error("IK Constraint not found: "+e);let h=i.ikConstraints.indexOf(o),l=new At(n.length,n.length<<1,h),c=vi(a,"time",0),u=vi(a,"mix",1),d=vi(a,"softness",0)*s;for(let t=0,e=0;;t++){l.setFrame(t,c,u,d,vi(a,"bendPositive",!0)?1:-1,vi(a,"compress",!1),vi(a,"stretch",!1));let i=n[t+1];if(!i){l.shrink(e);break}let r=vi(i,"time",0),o=vi(i,"mix",1),h=vi(i,"softness",0)*s,f=a.curve;f&&(e=xi(f,l,e,t,0,c,r,u,o,1),e=xi(f,l,e,t,1,c,r,d,h,s)),c=r,u=o,d=h,a=i}r.push(l)}if(t.transform)for(let e in t.transform){let s=t.transform[e],n=s[0];if(!n)continue;let a=i.findTransformConstraint(e);if(!a)throw new Error("Transform constraint not found: "+e);let o=i.transformConstraints.indexOf(a),h=new Mt(s.length,6*s.length,o),l=vi(n,"time",0),c=vi(n,"mixRotate",1),u=vi(n,"mixX",1),d=vi(n,"mixY",u),f=vi(n,"mixScaleX",1),p=vi(n,"mixScaleY",f),g=vi(n,"mixShearY",1);for(let t=0,e=0;;t++){h.setFrame(t,l,c,u,d,f,p,g);let i=s[t+1];if(!i){h.shrink(e);break}let r=vi(i,"time",0),a=vi(i,"mixRotate",1),o=vi(i,"mixX",1),m=vi(i,"mixY",o),x=vi(i,"mixScaleX",1),v=vi(i,"mixScaleY",x),y=vi(i,"mixShearY",1),w=n.curve;w&&(e=xi(w,h,e,t,0,l,r,c,a,1),e=xi(w,h,e,t,1,l,r,u,o,1),e=xi(w,h,e,t,2,l,r,d,m,1),e=xi(w,h,e,t,3,l,r,f,x,1),e=xi(w,h,e,t,4,l,r,p,v,1),e=xi(w,h,e,t,5,l,r,g,y,1)),l=r,c=a,u=o,d=m,f=x,p=v,f=x,n=i}r.push(h)}if(t.path)for(let e in t.path){let n=t.path[e],a=i.findPathConstraint(e);if(!a)throw new Error("Path constraint not found: "+e);let o=i.pathConstraints.indexOf(a);for(let t in n){let e=n[t],i=e[0];if(!i)continue;let h=e.length;if("position"===t){let t=new Et(h,h,o);r.push(gi(e,t,0,0==a.positionMode?s:1))}else if("spacing"===t){let t=new St(h,h,o);r.push(gi(e,t,0,0==a.spacingMode||1==a.spacingMode?s:1))}else if("mix"===t){let t=new Tt(h,3*h,o),s=vi(i,"time",0),n=vi(i,"mixRotate",1),a=vi(i,"mixX",1),l=vi(i,"mixY",a);for(let r=0,o=0;;r++){t.setFrame(r,s,n,a,l);let h=e[r+1];if(!h){t.shrink(o);break}let c=vi(h,"time",0),u=vi(h,"mixRotate",1),d=vi(h,"mixX",1),f=vi(h,"mixY",d),p=i.curve;p&&(o=xi(p,t,o,r,0,s,c,n,u,1),o=xi(p,t,o,r,1,s,c,a,d,1),o=xi(p,t,o,r,2,s,c,l,f,1)),s=c,n=u,a=d,l=f,i=h}r.push(t)}}}if(t.attachments)for(let e in t.attachments){let n=t.attachments[e],a=i.findSkin(e);if(!a)throw new Error("Skin not found: "+e);for(let t in n){let e=n[t],o=i.findSlot(t);if(!o)throw new Error("Slot not found: "+t);let h=o.index;for(let t in e){let i=e[t],n=a.getAttachment(h,t);for(let t in i){let e=i[t],a=e[0];if(a)if("deform"==t){let t=n.bones,i=n.vertices,o=t?i.length/3*2:i.length,l=new xt(e.length,e.length,h,n),c=vi(a,"time",0);for(let r=0,n=0;;r++){let h,u=vi(a,"vertices",null);if(u){h=g.newFloatArray(o);let e=vi(a,"offset",0);if(g.arrayCopy(u,0,h,e,u.length),1!=s)for(let t=e,i=t+u.length;t<i;t++)h[t]*=s;if(!t)for(let t=0;t<o;t++)h[t]+=i[t]}else h=t?g.newFloatArray(o):i;l.setFrame(r,c,h);let d=e[r+1];if(!d){l.shrink(n);break}let f=vi(d,"time",0),p=a.curve;p&&(n=xi(p,l,n,r,0,c,f,0,1,1)),c=f,a=d}r.push(l)}else if("sequence"==t){let t=new Ct(e.length,h,n),i=0;for(let s=0;s<e.length;s++){let r=vi(a,"delay",i),n=vi(a,"time",0),o=T[vi(a,"mode","hold")],h=vi(a,"index",0);t.setFrame(s,n,o,h,r),i=r,a=e[s+1]}r.push(t)}}}}}if(t.drawOrder){let e=new bt(t.drawOrder.length),s=i.slots.length,n=0;for(let r=0;r<t.drawOrder.length;r++,n++){let a=t.drawOrder[r],o=null,h=vi(a,"offsets",null);if(h){o=g.newArray(s,-1);let t=g.newArray(s-h.length,0),e=0,r=0;for(let s=0;s<h.length;s++){let n=h[s],a=i.findSlot(n.slot);if(!a)throw new Error("Slot not found: "+a);let l=a.index;for(;e!=l;)t[r++]=e++;o[e+n.offset]=e++}for(;e<s;)t[r++]=e++;for(let e=s-1;e>=0;e--)-1==o[e]&&(o[e]=t[--r])}e.setFrame(n,vi(a,"time",0),o)}r.push(e)}if(t.events){let e=new yt(t.events.length),s=0;for(let r=0;r<t.events.length;r++,s++){let n=t.events[r],a=i.findEvent(n.name);if(!a)throw new Error("Event not found: "+n.name);let o=new de(g.toSinglePrecision(vi(n,"time",0)),a);o.intValue=vi(n,"int",a.intValue),o.floatValue=vi(n,"float",a.floatValue),o.stringValue=vi(n,"string",a.stringValue),o.data.audioPath&&(o.volume=vi(n,"volume",1),o.balance=vi(n,"balance",0)),e.setFrame(s,o)}r.push(e)}let n=0;for(let t=0,e=r.length;t<e;t++)n=Math.max(n,r[t].getDuration());i.animations.push(new C(e,r,n))}},pi=class{constructor(t,e,i,s,r){this.mesh=t,this.skin=e,this.slotIndex=i,this.parent=s,this.inheritTimeline=r}};function gi(t,e,i,s){let r=t[0],n=vi(r,"time",0),a=vi(r,"value",i)*s,o=0;for(let h=0;;h++){e.setFrame(h,n,a);let l=t[h+1];if(!l)return e.shrink(o),e;let c=vi(l,"time",0),u=vi(l,"value",i)*s;r.curve&&(o=xi(r.curve,e,o,h,0,n,c,a,u,s)),n=c,a=u,r=l}}function mi(t,e,i,s,r,n){let a=t[0],o=vi(a,"time",0),h=vi(a,i,r)*n,l=vi(a,s,r)*n,c=0;for(let u=0;;u++){e.setFrame(u,o,h,l);let d=t[u+1];if(!d)return e.shrink(c),e;let f=vi(d,"time",0),p=vi(d,i,r)*n,g=vi(d,s,r)*n,m=a.curve;m&&(c=xi(m,e,c,u,0,o,f,h,p,n),c=xi(m,e,c,u,1,o,f,l,g,n)),o=f,h=p,l=g,a=d}}function xi(t,e,i,s,r,n,a,o,h,l){if("stepped"==t)return e.setStepped(s),i;let c=r<<2,u=t[c],d=t[c+1]*l,f=t[c+2],p=t[c+3]*l;return e.setBezier(i,s,r,n,o,u,d,f,p,a,h),i+1}function vi(t,e,i){return void 0!==t[e]?t[e]:i}void 0===Math.fround&&(Math.fround=(_e=new Float32Array(1),function(t){return _e[0]=t,_e[0]}));var yi=class{constructor(t,e={alpha:"true"}){if(this.restorables=new Array,t instanceof WebGLRenderingContext||"undefined"!=typeof WebGL2RenderingContext&&t instanceof WebGL2RenderingContext)this.gl=t,this.canvas=this.gl.canvas;else{let i=t;this.gl=i.getContext("webgl2",e)||i.getContext("webgl",e),this.canvas=i,i.addEventListener("webglcontextlost",(t=>{t&&t.preventDefault()})),i.addEventListener("webglcontextrestored",(t=>{for(let t=0,e=this.restorables.length;t<e;t++)this.restorables[t].restore()}))}}addRestorable(t){this.restorables.push(t)}removeRestorable(t){let e=this.restorables.indexOf(t);e>-1&&this.restorables.splice(e,1)}},wi=771,bi=class{static getDestGLBlendMode(t){switch(t){case 0:case 2:case 3:return wi;case 1:return 1;default:throw new Error("Unknown blend mode: "+t)}}static getSourceColorGLBlendMode(t,e=!1){switch(t){case 0:case 1:return e?1:770;case 2:return 774;case 3:return 1;default:throw new Error("Unknown blend mode: "+t)}}static getSourceAlphaGLBlendMode(t){switch(t){case 0:case 1:return 1;case 2:return wi;case 3:return 769;default:throw new Error("Unknown blend mode: "+t)}}},Ai=class extends qt{constructor(t,e,i=!1){super(e),this.texture=null,this.boundUnit=0,this.useMipMaps=!1,this.context=t instanceof yi?t:new yi(t),this.useMipMaps=i,this.restore(),this.context.addRestorable(this)}setFilters(t,e){let i=this.context.gl;this.bind(),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_MIN_FILTER,t),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_MAG_FILTER,Ai.validateMagFilter(e)),this.useMipMaps=Ai.usesMipMaps(t),this.useMipMaps&&i.generateMipmap(i.TEXTURE_2D)}static validateMagFilter(t){switch(t){case 9987:case 9987:case 9985:case 9986:case 9984:return 9729;default:return t}}static usesMipMaps(t){switch(t){case 9987:case 9987:case 9985:case 9986:case 9984:return!0;default:return!1}}setWraps(t,e){let i=this.context.gl;this.bind(),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_WRAP_S,t),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_WRAP_T,e)}update(t){let e=this.context.gl;this.texture||(this.texture=this.context.gl.createTexture()),this.bind(),Ai.DISABLE_UNPACK_PREMULTIPLIED_ALPHA_WEBGL&&e.pixelStorei(e.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,this._image),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,t?e.LINEAR_MIPMAP_LINEAR:e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),t&&e.generateMipmap(e.TEXTURE_2D)}restore(){this.texture=null,this.update(this.useMipMaps)}bind(t=0){let e=this.context.gl;this.boundUnit=t,e.activeTexture(e.TEXTURE0+t),e.bindTexture(e.TEXTURE_2D,this.texture)}unbind(){let t=this.context.gl;t.activeTexture(t.TEXTURE0+this.boundUnit),t.bindTexture(t.TEXTURE_2D,null)}dispose(){this.context.removeRestorable(this),this.context.gl.deleteTexture(this.texture)}},Mi=Ai;Mi.DISABLE_UNPACK_PREMULTIPLIED_ALPHA_WEBGL=!1;var Ei=class extends ce{constructor(t,e="",i=new ue){super((e=>new Mi(t,e)),e,i)}},Si=class{constructor(t=0,e=0,i=0){this.x=0,this.y=0,this.z=0,this.x=t,this.y=e,this.z=i}setFrom(t){return this.x=t.x,this.y=t.y,this.z=t.z,this}set(t,e,i){return this.x=t,this.y=e,this.z=i,this}add(t){return this.x+=t.x,this.y+=t.y,this.z+=t.z,this}sub(t){return this.x-=t.x,this.y-=t.y,this.z-=t.z,this}scale(t){return this.x*=t,this.y*=t,this.z*=t,this}normalize(){let t=this.length();return 0==t||(t=1/t,this.x*=t,this.y*=t,this.z*=t),this}cross(t){return this.set(this.y*t.z-this.z*t.y,this.z*t.x-this.x*t.z,this.x*t.y-this.y*t.x)}multiply(t){let e=t.values;return this.set(this.x*e[Ti]+this.y*e[Ii]+this.z*e[Ci]+e[Ri],this.x*e[ki]+this.y*e[Fi]+this.z*e[Li]+e[Pi],this.x*e[Oi]+this.y*e[Yi]+this.z*e[Di]+e[Xi])}project(t){let e=t.values,i=1/(this.x*e[Bi]+this.y*e[zi]+this.z*e[_i]+e[Ni]);return this.set((this.x*e[Ti]+this.y*e[Ii]+this.z*e[Ci]+e[Ri])*i,(this.x*e[ki]+this.y*e[Fi]+this.z*e[Li]+e[Pi])*i,(this.x*e[Oi]+this.y*e[Yi]+this.z*e[Di]+e[Xi])*i)}dot(t){return this.x*t.x+this.y*t.y+this.z*t.z}length(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)}distance(t){let e=t.x-this.x,i=t.y-this.y,s=t.z-this.z;return Math.sqrt(e*e+i*i+s*s)}},Ti=0,Ii=4,Ci=8,Ri=12,ki=1,Fi=5,Li=9,Pi=13,Oi=2,Yi=6,Di=10,Xi=14,Bi=3,zi=7,_i=11,Ni=15,Vi=class{constructor(){this.temp=new Float32Array(16),this.values=new Float32Array(16);let t=this.values;t[Ti]=1,t[Fi]=1,t[Di]=1,t[Ni]=1}set(t){return this.values.set(t),this}transpose(){let t=this.temp,e=this.values;return t[Ti]=e[Ti],t[Ii]=e[ki],t[Ci]=e[Oi],t[Ri]=e[Bi],t[ki]=e[Ii],t[Fi]=e[Fi],t[Li]=e[Yi],t[Pi]=e[zi],t[Oi]=e[Ci],t[Yi]=e[Li],t[Di]=e[Di],t[Xi]=e[_i],t[Bi]=e[Ri],t[zi]=e[Pi],t[_i]=e[Xi],t[Ni]=e[Ni],this.set(t)}identity(){let t=this.values;return t[Ti]=1,t[Ii]=0,t[Ci]=0,t[Ri]=0,t[ki]=0,t[Fi]=1,t[Li]=0,t[Pi]=0,t[Oi]=0,t[Yi]=0,t[Di]=1,t[Xi]=0,t[Bi]=0,t[zi]=0,t[_i]=0,t[Ni]=1,this}invert(){let t=this.values,e=this.temp,i=t[Bi]*t[Yi]*t[Li]*t[Ri]-t[Oi]*t[zi]*t[Li]*t[Ri]-t[Bi]*t[Fi]*t[Di]*t[Ri]+t[ki]*t[zi]*t[Di]*t[Ri]+t[Oi]*t[Fi]*t[_i]*t[Ri]-t[ki]*t[Yi]*t[_i]*t[Ri]-t[Bi]*t[Yi]*t[Ci]*t[Pi]+t[Oi]*t[zi]*t[Ci]*t[Pi]+t[Bi]*t[Ii]*t[Di]*t[Pi]-t[Ti]*t[zi]*t[Di]*t[Pi]-t[Oi]*t[Ii]*t[_i]*t[Pi]+t[Ti]*t[Yi]*t[_i]*t[Pi]+t[Bi]*t[Fi]*t[Ci]*t[Xi]-t[ki]*t[zi]*t[Ci]*t[Xi]-t[Bi]*t[Ii]*t[Li]*t[Xi]+t[Ti]*t[zi]*t[Li]*t[Xi]+t[ki]*t[Ii]*t[_i]*t[Xi]-t[Ti]*t[Fi]*t[_i]*t[Xi]-t[Oi]*t[Fi]*t[Ci]*t[Ni]+t[ki]*t[Yi]*t[Ci]*t[Ni]+t[Oi]*t[Ii]*t[Li]*t[Ni]-t[Ti]*t[Yi]*t[Li]*t[Ni]-t[ki]*t[Ii]*t[Di]*t[Ni]+t[Ti]*t[Fi]*t[Di]*t[Ni];if(0==i)throw new Error("non-invertible matrix");let s=1/i;return e[Ti]=t[Li]*t[Xi]*t[zi]-t[Pi]*t[Di]*t[zi]+t[Pi]*t[Yi]*t[_i]-t[Fi]*t[Xi]*t[_i]-t[Li]*t[Yi]*t[Ni]+t[Fi]*t[Di]*t[Ni],e[Ii]=t[Ri]*t[Di]*t[zi]-t[Ci]*t[Xi]*t[zi]-t[Ri]*t[Yi]*t[_i]+t[Ii]*t[Xi]*t[_i]+t[Ci]*t[Yi]*t[Ni]-t[Ii]*t[Di]*t[Ni],e[Ci]=t[Ci]*t[Pi]*t[zi]-t[Ri]*t[Li]*t[zi]+t[Ri]*t[Fi]*t[_i]-t[Ii]*t[Pi]*t[_i]-t[Ci]*t[Fi]*t[Ni]+t[Ii]*t[Li]*t[Ni],e[Ri]=t[Ri]*t[Li]*t[Yi]-t[Ci]*t[Pi]*t[Yi]-t[Ri]*t[Fi]*t[Di]+t[Ii]*t[Pi]*t[Di]+t[Ci]*t[Fi]*t[Xi]-t[Ii]*t[Li]*t[Xi],e[ki]=t[Pi]*t[Di]*t[Bi]-t[Li]*t[Xi]*t[Bi]-t[Pi]*t[Oi]*t[_i]+t[ki]*t[Xi]*t[_i]+t[Li]*t[Oi]*t[Ni]-t[ki]*t[Di]*t[Ni],e[Fi]=t[Ci]*t[Xi]*t[Bi]-t[Ri]*t[Di]*t[Bi]+t[Ri]*t[Oi]*t[_i]-t[Ti]*t[Xi]*t[_i]-t[Ci]*t[Oi]*t[Ni]+t[Ti]*t[Di]*t[Ni],e[Li]=t[Ri]*t[Li]*t[Bi]-t[Ci]*t[Pi]*t[Bi]-t[Ri]*t[ki]*t[_i]+t[Ti]*t[Pi]*t[_i]+t[Ci]*t[ki]*t[Ni]-t[Ti]*t[Li]*t[Ni],e[Pi]=t[Ci]*t[Pi]*t[Oi]-t[Ri]*t[Li]*t[Oi]+t[Ri]*t[ki]*t[Di]-t[Ti]*t[Pi]*t[Di]-t[Ci]*t[ki]*t[Xi]+t[Ti]*t[Li]*t[Xi],e[Oi]=t[Fi]*t[Xi]*t[Bi]-t[Pi]*t[Yi]*t[Bi]+t[Pi]*t[Oi]*t[zi]-t[ki]*t[Xi]*t[zi]-t[Fi]*t[Oi]*t[Ni]+t[ki]*t[Yi]*t[Ni],e[Yi]=t[Ri]*t[Yi]*t[Bi]-t[Ii]*t[Xi]*t[Bi]-t[Ri]*t[Oi]*t[zi]+t[Ti]*t[Xi]*t[zi]+t[Ii]*t[Oi]*t[Ni]-t[Ti]*t[Yi]*t[Ni],e[Di]=t[Ii]*t[Pi]*t[Bi]-t[Ri]*t[Fi]*t[Bi]+t[Ri]*t[ki]*t[zi]-t[Ti]*t[Pi]*t[zi]-t[Ii]*t[ki]*t[Ni]+t[Ti]*t[Fi]*t[Ni],e[Xi]=t[Ri]*t[Fi]*t[Oi]-t[Ii]*t[Pi]*t[Oi]-t[Ri]*t[ki]*t[Yi]+t[Ti]*t[Pi]*t[Yi]+t[Ii]*t[ki]*t[Xi]-t[Ti]*t[Fi]*t[Xi],e[Bi]=t[Li]*t[Yi]*t[Bi]-t[Fi]*t[Di]*t[Bi]-t[Li]*t[Oi]*t[zi]+t[ki]*t[Di]*t[zi]+t[Fi]*t[Oi]*t[_i]-t[ki]*t[Yi]*t[_i],e[zi]=t[Ii]*t[Di]*t[Bi]-t[Ci]*t[Yi]*t[Bi]+t[Ci]*t[Oi]*t[zi]-t[Ti]*t[Di]*t[zi]-t[Ii]*t[Oi]*t[_i]+t[Ti]*t[Yi]*t[_i],e[_i]=t[Ci]*t[Fi]*t[Bi]-t[Ii]*t[Li]*t[Bi]-t[Ci]*t[ki]*t[zi]+t[Ti]*t[Li]*t[zi]+t[Ii]*t[ki]*t[_i]-t[Ti]*t[Fi]*t[_i],e[Ni]=t[Ii]*t[Li]*t[Oi]-t[Ci]*t[Fi]*t[Oi]+t[Ci]*t[ki]*t[Yi]-t[Ti]*t[Li]*t[Yi]-t[Ii]*t[ki]*t[Di]+t[Ti]*t[Fi]*t[Di],t[Ti]=e[Ti]*s,t[Ii]=e[Ii]*s,t[Ci]=e[Ci]*s,t[Ri]=e[Ri]*s,t[ki]=e[ki]*s,t[Fi]=e[Fi]*s,t[Li]=e[Li]*s,t[Pi]=e[Pi]*s,t[Oi]=e[Oi]*s,t[Yi]=e[Yi]*s,t[Di]=e[Di]*s,t[Xi]=e[Xi]*s,t[Bi]=e[Bi]*s,t[zi]=e[zi]*s,t[_i]=e[_i]*s,t[Ni]=e[Ni]*s,this}determinant(){let t=this.values;return t[Bi]*t[Yi]*t[Li]*t[Ri]-t[Oi]*t[zi]*t[Li]*t[Ri]-t[Bi]*t[Fi]*t[Di]*t[Ri]+t[ki]*t[zi]*t[Di]*t[Ri]+t[Oi]*t[Fi]*t[_i]*t[Ri]-t[ki]*t[Yi]*t[_i]*t[Ri]-t[Bi]*t[Yi]*t[Ci]*t[Pi]+t[Oi]*t[zi]*t[Ci]*t[Pi]+t[Bi]*t[Ii]*t[Di]*t[Pi]-t[Ti]*t[zi]*t[Di]*t[Pi]-t[Oi]*t[Ii]*t[_i]*t[Pi]+t[Ti]*t[Yi]*t[_i]*t[Pi]+t[Bi]*t[Fi]*t[Ci]*t[Xi]-t[ki]*t[zi]*t[Ci]*t[Xi]-t[Bi]*t[Ii]*t[Li]*t[Xi]+t[Ti]*t[zi]*t[Li]*t[Xi]+t[ki]*t[Ii]*t[_i]*t[Xi]-t[Ti]*t[Fi]*t[_i]*t[Xi]-t[Oi]*t[Fi]*t[Ci]*t[Ni]+t[ki]*t[Yi]*t[Ci]*t[Ni]+t[Oi]*t[Ii]*t[Li]*t[Ni]-t[Ti]*t[Yi]*t[Li]*t[Ni]-t[ki]*t[Ii]*t[Di]*t[Ni]+t[Ti]*t[Fi]*t[Di]*t[Ni]}translate(t,e,i){let s=this.values;return s[Ri]+=t,s[Pi]+=e,s[Xi]+=i,this}copy(){return(new Vi).set(this.values)}projection(t,e,i,s){this.identity();let r=1/Math.tan(i*(Math.PI/180)/2),n=(e+t)/(t-e),a=2*e*t/(t-e),o=this.values;return o[Ti]=r/s,o[ki]=0,o[Oi]=0,o[Bi]=0,o[Ii]=0,o[Fi]=r,o[Yi]=0,o[zi]=0,o[Ci]=0,o[Li]=0,o[Di]=n,o[_i]=-1,o[Ri]=0,o[Pi]=0,o[Xi]=a,o[Ni]=0,this}ortho2d(t,e,i,s){return this.ortho(t,t+i,e,e+s,0,1)}ortho(t,e,i,s,r,n){this.identity();let a=2/(e-t),o=2/(s-i),h=-2/(n-r),l=-(e+t)/(e-t),c=-(s+i)/(s-i),u=-(n+r)/(n-r),d=this.values;return d[Ti]=a,d[ki]=0,d[Oi]=0,d[Bi]=0,d[Ii]=0,d[Fi]=o,d[Yi]=0,d[zi]=0,d[Ci]=0,d[Li]=0,d[Di]=h,d[_i]=0,d[Ri]=l,d[Pi]=c,d[Xi]=u,d[Ni]=1,this}multiply(t){let e=this.temp,i=this.values,s=t.values;return e[Ti]=i[Ti]*s[Ti]+i[Ii]*s[ki]+i[Ci]*s[Oi]+i[Ri]*s[Bi],e[Ii]=i[Ti]*s[Ii]+i[Ii]*s[Fi]+i[Ci]*s[Yi]+i[Ri]*s[zi],e[Ci]=i[Ti]*s[Ci]+i[Ii]*s[Li]+i[Ci]*s[Di]+i[Ri]*s[_i],e[Ri]=i[Ti]*s[Ri]+i[Ii]*s[Pi]+i[Ci]*s[Xi]+i[Ri]*s[Ni],e[ki]=i[ki]*s[Ti]+i[Fi]*s[ki]+i[Li]*s[Oi]+i[Pi]*s[Bi],e[Fi]=i[ki]*s[Ii]+i[Fi]*s[Fi]+i[Li]*s[Yi]+i[Pi]*s[zi],e[Li]=i[ki]*s[Ci]+i[Fi]*s[Li]+i[Li]*s[Di]+i[Pi]*s[_i],e[Pi]=i[ki]*s[Ri]+i[Fi]*s[Pi]+i[Li]*s[Xi]+i[Pi]*s[Ni],e[Oi]=i[Oi]*s[Ti]+i[Yi]*s[ki]+i[Di]*s[Oi]+i[Xi]*s[Bi],e[Yi]=i[Oi]*s[Ii]+i[Yi]*s[Fi]+i[Di]*s[Yi]+i[Xi]*s[zi],e[Di]=i[Oi]*s[Ci]+i[Yi]*s[Li]+i[Di]*s[Di]+i[Xi]*s[_i],e[Xi]=i[Oi]*s[Ri]+i[Yi]*s[Pi]+i[Di]*s[Xi]+i[Xi]*s[Ni],e[Bi]=i[Bi]*s[Ti]+i[zi]*s[ki]+i[_i]*s[Oi]+i[Ni]*s[Bi],e[zi]=i[Bi]*s[Ii]+i[zi]*s[Fi]+i[_i]*s[Yi]+i[Ni]*s[zi],e[_i]=i[Bi]*s[Ci]+i[zi]*s[Li]+i[_i]*s[Di]+i[Ni]*s[_i],e[Ni]=i[Bi]*s[Ri]+i[zi]*s[Pi]+i[_i]*s[Xi]+i[Ni]*s[Ni],this.set(this.temp)}multiplyLeft(t){let e=this.temp,i=this.values,s=t.values;return e[Ti]=s[Ti]*i[Ti]+s[Ii]*i[ki]+s[Ci]*i[Oi]+s[Ri]*i[Bi],e[Ii]=s[Ti]*i[Ii]+s[Ii]*i[Fi]+s[Ci]*i[Yi]+s[Ri]*i[zi],e[Ci]=s[Ti]*i[Ci]+s[Ii]*i[Li]+s[Ci]*i[Di]+s[Ri]*i[_i],e[Ri]=s[Ti]*i[Ri]+s[Ii]*i[Pi]+s[Ci]*i[Xi]+s[Ri]*i[Ni],e[ki]=s[ki]*i[Ti]+s[Fi]*i[ki]+s[Li]*i[Oi]+s[Pi]*i[Bi],e[Fi]=s[ki]*i[Ii]+s[Fi]*i[Fi]+s[Li]*i[Yi]+s[Pi]*i[zi],e[Li]=s[ki]*i[Ci]+s[Fi]*i[Li]+s[Li]*i[Di]+s[Pi]*i[_i],e[Pi]=s[ki]*i[Ri]+s[Fi]*i[Pi]+s[Li]*i[Xi]+s[Pi]*i[Ni],e[Oi]=s[Oi]*i[Ti]+s[Yi]*i[ki]+s[Di]*i[Oi]+s[Xi]*i[Bi],e[Yi]=s[Oi]*i[Ii]+s[Yi]*i[Fi]+s[Di]*i[Yi]+s[Xi]*i[zi],e[Di]=s[Oi]*i[Ci]+s[Yi]*i[Li]+s[Di]*i[Di]+s[Xi]*i[_i],e[Xi]=s[Oi]*i[Ri]+s[Yi]*i[Pi]+s[Di]*i[Xi]+s[Xi]*i[Ni],e[Bi]=s[Bi]*i[Ti]+s[zi]*i[ki]+s[_i]*i[Oi]+s[Ni]*i[Bi],e[zi]=s[Bi]*i[Ii]+s[zi]*i[Fi]+s[_i]*i[Yi]+s[Ni]*i[zi],e[_i]=s[Bi]*i[Ci]+s[zi]*i[Li]+s[_i]*i[Di]+s[Ni]*i[_i],e[Ni]=s[Bi]*i[Ri]+s[zi]*i[Pi]+s[_i]*i[Xi]+s[Ni]*i[Ni],this.set(this.temp)}lookAt(t,e,i){let s=Vi.xAxis,r=Vi.yAxis,n=Vi.zAxis;n.setFrom(e).normalize(),s.setFrom(e).normalize(),s.cross(i).normalize(),r.setFrom(s).cross(n).normalize(),this.identity();let a=this.values;return a[Ti]=s.x,a[Ii]=s.y,a[Ci]=s.z,a[ki]=r.x,a[Fi]=r.y,a[Li]=r.z,a[Oi]=-n.x,a[Yi]=-n.y,a[Di]=-n.z,Vi.tmpMatrix.identity(),Vi.tmpMatrix.values[Ri]=-t.x,Vi.tmpMatrix.values[Pi]=-t.y,Vi.tmpMatrix.values[Xi]=-t.z,this.multiply(Vi.tmpMatrix),this}},Ui=Vi;Ui.xAxis=new Si,Ui.yAxis=new Si,Ui.zAxis=new Si,Ui.tmpMatrix=new Vi;var Wi=class{constructor(t,e){this.position=new Si(0,0,0),this.direction=new Si(0,0,-1),this.up=new Si(0,1,0),this.near=0,this.far=100,this.zoom=1,this.viewportWidth=0,this.viewportHeight=0,this.projectionView=new Ui,this.inverseProjectionView=new Ui,this.projection=new Ui,this.view=new Ui,this.viewportWidth=t,this.viewportHeight=e,this.update()}update(){let t=this.projection,e=this.view,i=this.projectionView,s=this.inverseProjectionView,r=this.zoom,n=this.viewportWidth,a=this.viewportHeight;t.ortho(r*(-n/2),r*(n/2),r*(-a/2),r*(a/2),this.near,this.far),e.lookAt(this.position,this.direction,this.up),i.set(t.values),i.multiply(e),s.set(i.values).invert()}screenToWorld(t,e,i){let s=t.x,r=i-t.y-1;return t.x=2*s/e-1,t.y=2*r/i-1,t.z=2*t.z-1,t.project(this.inverseProjectionView),t}worldToScreen(t,e,i){return t.project(this.projectionView),t.x=e*(t.x+1)/2,t.y=i*(t.y+1)/2,t.z=(t.z+1)/2,t}setViewport(t,e){this.viewportWidth=t,this.viewportHeight=e}},qi=class{constructor(t){this.mouseX=0,this.mouseY=0,this.buttonDown=!1,this.touch0=null,this.touch1=null,this.initialPinchDistance=0,this.listeners=new Array,this.eventListeners=[],this.element=t,this.setupCallbacks(t)}setupCallbacks(t){let e=e=>{if(e instanceof MouseEvent){let i=t.getBoundingClientRect();this.mouseX=e.clientX-i.left,this.mouseY=e.clientY-i.top,this.listeners.map((t=>{this.buttonDown?t.dragged&&t.dragged(this.mouseX,this.mouseY):t.moved&&t.moved(this.mouseX,this.mouseY)}))}},i=s=>{if(s instanceof MouseEvent){let r=t.getBoundingClientRect();this.mouseX=s.clientX-r.left,this.mouseY=s.clientY-r.top,this.buttonDown=!1,this.listeners.map((t=>{t.up&&t.up(this.mouseX,this.mouseY)})),document.removeEventListener("mousemove",e),document.removeEventListener("mouseup",i)}};t.addEventListener("mousedown",(s=>{if(s instanceof MouseEvent){let r=t.getBoundingClientRect();this.mouseX=s.clientX-r.left,this.mouseY=s.clientY-r.top,this.buttonDown=!0,this.listeners.map((t=>{t.down&&t.down(this.mouseX,this.mouseY)})),document.addEventListener("mousemove",e),document.addEventListener("mouseup",i)}}),!0),t.addEventListener("mousemove",e,!0),t.addEventListener("mouseup",i,!0),t.addEventListener("wheel",(t=>{t.preventDefault();let e=t.deltaY;t.deltaMode==WheelEvent.DOM_DELTA_LINE&&(e*=8),t.deltaMode==WheelEvent.DOM_DELTA_PAGE&&(e*=24),this.listeners.map((e=>{e.wheel&&e.wheel(t.deltaY)}))}),!0),t.addEventListener("touchstart",(e=>{if(!this.touch0||!this.touch1){let i=e.changedTouches.item(0);if(!i)return;let s=t.getBoundingClientRect(),r=i.clientX-s.left,n=i.clientY-s.top,a=new Gi(i.identifier,r,n);if(this.mouseX=r,this.mouseY=n,this.buttonDown=!0,this.touch0){if(!this.touch1){this.touch1=a;let t=this.touch1.x-this.touch0.x,e=this.touch1.x-this.touch0.x;this.initialPinchDistance=Math.sqrt(t*t+e*e),this.listeners.map((t=>{t.zoom&&t.zoom(this.initialPinchDistance,this.initialPinchDistance)}))}}else this.touch0=a,this.listeners.map((t=>{t.down&&t.down(a.x,a.y)}))}e.preventDefault()}),!1),t.addEventListener("touchmove",(e=>{if(this.touch0){var i=e.changedTouches;let n=t.getBoundingClientRect();for(var s=0;s<i.length;s++){var r=i[s];let t=r.clientX-n.left,e=r.clientY-n.top;this.touch0.identifier===r.identifier&&(this.touch0.x=this.mouseX=t,this.touch0.y=this.mouseY=e,this.listeners.map((i=>{i.dragged&&i.dragged(t,e)}))),this.touch1&&this.touch1.identifier===r.identifier&&(this.touch1.x=this.mouseX=t,this.touch1.y=this.mouseY=e)}if(this.touch0&&this.touch1){let t=this.touch1.x-this.touch0.x,e=this.touch1.x-this.touch0.x,i=Math.sqrt(t*t+e*e);this.listeners.map((t=>{t.zoom&&t.zoom(this.initialPinchDistance,i)}))}}e.preventDefault()}),!1);let s=e=>{if(this.touch0){var i=e.changedTouches;let n=t.getBoundingClientRect();for(var s=0;s<i.length;s++){var r=i[s];let t=r.clientX-n.left,e=r.clientY-n.top;if(this.touch0.identifier===r.identifier){if(this.touch0=null,this.mouseX=t,this.mouseY=e,this.listeners.map((i=>{i.up&&i.up(t,e)})),!this.touch1){this.buttonDown=!1;break}this.touch0=this.touch1,this.touch1=null,this.mouseX=this.touch0.x,this.mouseX=this.touch0.x,this.buttonDown=!0,this.listeners.map((t=>{t.down&&t.down(this.touch0.x,this.touch0.y)}))}this.touch1&&this.touch1.identifier&&(this.touch1=null)}}e.preventDefault()};t.addEventListener("touchend",s,!1),t.addEventListener("touchcancel",s)}addListener(t){this.listeners.push(t)}removeListener(t){let e=this.listeners.indexOf(t);e>-1&&this.listeners.splice(e,1)}},Gi=class{constructor(t,e,i){this.identifier=t,this.x=e,this.y=i}},ji=class{constructor(t,e){this.canvas=t,this.camera=e;let i=0,s=0,r=0,n=0,a=0,o=0,h=0;new qi(t).addListener({down:(t,l)=>{i=e.position.x,s=e.position.y,r=a=t,n=o=l,h=e.zoom},dragged:(h,l)=>{let c=h-r,u=l-n,d=e.screenToWorld(new Si(0,0),t.clientWidth,t.clientHeight),f=e.screenToWorld(new Si(c,u),t.clientWidth,t.clientHeight).sub(d);e.position.set(i-f.x,s-f.y,0),e.update(),a=h,o=l},wheel:i=>{let s=i/200*e.zoom,r=e.zoom+s;if(r>0){let s=0,n=0;if(i<0)s=a,n=o;else{let e=new Si(t.clientWidth/2+15,t.clientHeight/2),i=a-e.x,r=t.clientHeight-1-o-e.y;s=e.x-i,n=t.clientHeight-1-e.y+r}let h=e.screenToWorld(new Si(s,n),t.clientWidth,t.clientHeight);e.zoom=r,e.update();let l=e.screenToWorld(new Si(s,n),t.clientWidth,t.clientHeight);e.position.add(h.sub(l)),e.update()}},zoom:(t,i)=>{let s=t/i;e.zoom=h*s},up:(t,e)=>{a=t,o=e},moved:(t,e)=>{a=t,o=e}})}},Hi=class{constructor(t,e,i){this.vertexShader=e,this.fragmentShader=i,this.vs=null,this.fs=null,this.program=null,this.tmp2x2=new Float32Array(4),this.tmp3x3=new Float32Array(9),this.tmp4x4=new Float32Array(16),this.vsSource=e,this.fsSource=i,this.context=t instanceof yi?t:new yi(t),this.context.addRestorable(this),this.compile()}getProgram(){return this.program}getVertexShader(){return this.vertexShader}getFragmentShader(){return this.fragmentShader}getVertexShaderSource(){return this.vsSource}getFragmentSource(){return this.fsSource}compile(){let t=this.context.gl;try{if(this.vs=this.compileShader(t.VERTEX_SHADER,this.vertexShader),!this.vs)throw new Error("Couldn't compile vertex shader.");if(this.fs=this.compileShader(t.FRAGMENT_SHADER,this.fragmentShader),!this.fs)throw new Error("Couldn#t compile fragment shader.");this.program=this.compileProgram(this.vs,this.fs)}catch(t){throw this.dispose(),t}}compileShader(t,e){let i=this.context.gl,s=i.createShader(t);if(!s)throw new Error("Couldn't create shader.");if(i.shaderSource(s,e),i.compileShader(s),!i.getShaderParameter(s,i.COMPILE_STATUS)){let t="Couldn't compile shader: "+i.getShaderInfoLog(s);if(i.deleteShader(s),!i.isContextLost())throw new Error(t)}return s}compileProgram(t,e){let i=this.context.gl,s=i.createProgram();if(!s)throw new Error("Couldn't compile program.");if(i.attachShader(s,t),i.attachShader(s,e),i.linkProgram(s),!i.getProgramParameter(s,i.LINK_STATUS)){let t="Couldn't compile shader program: "+i.getProgramInfoLog(s);if(i.deleteProgram(s),!i.isContextLost())throw new Error(t)}return s}restore(){this.compile()}bind(){this.context.gl.useProgram(this.program)}unbind(){this.context.gl.useProgram(null)}setUniformi(t,e){this.context.gl.uniform1i(this.getUniformLocation(t),e)}setUniformf(t,e){this.context.gl.uniform1f(this.getUniformLocation(t),e)}setUniform2f(t,e,i){this.context.gl.uniform2f(this.getUniformLocation(t),e,i)}setUniform3f(t,e,i,s){this.context.gl.uniform3f(this.getUniformLocation(t),e,i,s)}setUniform4f(t,e,i,s,r){this.context.gl.uniform4f(this.getUniformLocation(t),e,i,s,r)}setUniform2x2f(t,e){let i=this.context.gl;this.tmp2x2.set(e),i.uniformMatrix2fv(this.getUniformLocation(t),!1,this.tmp2x2)}setUniform3x3f(t,e){let i=this.context.gl;this.tmp3x3.set(e),i.uniformMatrix3fv(this.getUniformLocation(t),!1,this.tmp3x3)}setUniform4x4f(t,e){let i=this.context.gl;this.tmp4x4.set(e),i.uniformMatrix4fv(this.getUniformLocation(t),!1,this.tmp4x4)}getUniformLocation(t){let e=this.context.gl;if(!this.program)throw new Error("Shader not compiled.");let i=e.getUniformLocation(this.program,t);if(!i&&!e.isContextLost())throw new Error(`Couldn't find location for uniform ${t}`);return i}getAttributeLocation(t){let e=this.context.gl;if(!this.program)throw new Error("Shader not compiled.");let i=e.getAttribLocation(this.program,t);if(-1==i&&!e.isContextLost())throw new Error(`Couldn't find location for attribute ${t}`);return i}dispose(){this.context.removeRestorable(this);let t=this.context.gl;this.vs&&(t.deleteShader(this.vs),this.vs=null),this.fs&&(t.deleteShader(this.fs),this.fs=null),this.program&&(t.deleteProgram(this.program),this.program=null)}static newColoredTextured(t){let e=`\n\t\t\t\tattribute vec4 ${Hi.POSITION};\n\t\t\t\tattribute vec4 ${Hi.COLOR};\n\t\t\t\tattribute vec2 ${Hi.TEXCOORDS};\n\t\t\t\tuniform mat4 ${Hi.MVP_MATRIX};\n\t\t\t\tvarying vec4 v_color;\n\t\t\t\tvarying vec2 v_texCoords;\n\n\t\t\t\tvoid main () {\n\t\t\t\t\tv_color = ${Hi.COLOR};\n\t\t\t\t\tv_texCoords = ${Hi.TEXCOORDS};\n\t\t\t\t\tgl_Position = ${Hi.MVP_MATRIX} * ${Hi.POSITION};\n\t\t\t\t}\n\t\t\t`;return new Hi(t,e,"\n\t\t\t\t#ifdef GL_ES\n\t\t\t\t\t#define LOWP lowp\n\t\t\t\t\tprecision mediump float;\n\t\t\t\t#else\n\t\t\t\t\t#define LOWP\n\t\t\t\t#endif\n\t\t\t\tvarying LOWP vec4 v_color;\n\t\t\t\tvarying vec2 v_texCoords;\n\t\t\t\tuniform sampler2D u_texture;\n\n\t\t\t\tvoid main () {\n\t\t\t\t\tgl_FragColor = v_color * texture2D(u_texture, v_texCoords);\n\t\t\t\t}\n\t\t\t")}static newTwoColoredTextured(t){let e=`\n\t\t\t\tattribute vec4 ${Hi.POSITION};\n\t\t\t\tattribute vec4 ${Hi.COLOR};\n\t\t\t\tattribute vec4 ${Hi.COLOR2};\n\t\t\t\tattribute vec2 ${Hi.TEXCOORDS};\n\t\t\t\tuniform mat4 ${Hi.MVP_MATRIX};\n\t\t\t\tvarying vec4 v_light;\n\t\t\t\tvarying vec4 v_dark;\n\t\t\t\tvarying vec2 v_texCoords;\n\n\t\t\t\tvoid main () {\n\t\t\t\t\tv_light = ${Hi.COLOR};\n\t\t\t\t\tv_dark = ${Hi.COLOR2};\n\t\t\t\t\tv_texCoords = ${Hi.TEXCOORDS};\n\t\t\t\t\tgl_Position = ${Hi.MVP_MATRIX} * ${Hi.POSITION};\n\t\t\t\t}\n\t\t\t`;return new Hi(t,e,"\n\t\t\t\t#ifdef GL_ES\n\t\t\t\t\t#define LOWP lowp\n\t\t\t\t\tprecision mediump float;\n\t\t\t\t#else\n\t\t\t\t\t#define LOWP\n\t\t\t\t#endif\n\t\t\t\tvarying LOWP vec4 v_light;\n\t\t\t\tvarying LOWP vec4 v_dark;\n\t\t\t\tvarying vec2 v_texCoords;\n\t\t\t\tuniform sampler2D u_texture;\n\n\t\t\t\tvoid main () {\n\t\t\t\t\tvec4 texColor = texture2D(u_texture, v_texCoords);\n\t\t\t\t\tgl_FragColor.a = texColor.a * v_light.a;\n\t\t\t\t\tgl_FragColor.rgb = ((texColor.a - 1.0) * v_dark.a + 1.0 - texColor.rgb) * v_dark.rgb + texColor.rgb * v_light.rgb;\n\t\t\t\t}\n\t\t\t")}static newColored(t){let e=`\n\t\t\t\tattribute vec4 ${Hi.POSITION};\n\t\t\t\tattribute vec4 ${Hi.COLOR};\n\t\t\t\tuniform mat4 ${Hi.MVP_MATRIX};\n\t\t\t\tvarying vec4 v_color;\n\n\t\t\t\tvoid main () {\n\t\t\t\t\tv_color = ${Hi.COLOR};\n\t\t\t\t\tgl_Position = ${Hi.MVP_MATRIX} * ${Hi.POSITION};\n\t\t\t\t}\n\t\t\t`;return new Hi(t,e,"\n\t\t\t\t#ifdef GL_ES\n\t\t\t\t\t#define LOWP lowp\n\t\t\t\t\tprecision mediump float;\n\t\t\t\t#else\n\t\t\t\t\t#define LOWP\n\t\t\t\t#endif\n\t\t\t\tvarying LOWP vec4 v_color;\n\n\t\t\t\tvoid main () {\n\t\t\t\t\tgl_FragColor = v_color;\n\t\t\t\t}\n\t\t\t")}},Zi=Hi;Zi.MVP_MATRIX="u_projTrans",Zi.POSITION="a_position",Zi.COLOR="a_color",Zi.COLOR2="a_color2",Zi.TEXCOORDS="a_texCoords",Zi.SAMPLER="u_texture";var Ki=class{constructor(t,e,i,s){this.attributes=e,this.verticesBuffer=null,this.verticesLength=0,this.dirtyVertices=!1,this.indicesBuffer=null,this.indicesLength=0,this.dirtyIndices=!1,this.elementsPerVertex=0,this.context=t instanceof yi?t:new yi(t),this.elementsPerVertex=0;for(let t=0;t<e.length;t++)this.elementsPerVertex+=e[t].numElements;this.vertices=new Float32Array(i*this.elementsPerVertex),this.indices=new Uint16Array(s),this.context.addRestorable(this)}getAttributes(){return this.attributes}maxVertices(){return this.vertices.length/this.elementsPerVertex}numVertices(){return this.verticesLength/this.elementsPerVertex}setVerticesLength(t){this.dirtyVertices=!0,this.verticesLength=t}getVertices(){return this.vertices}maxIndices(){return this.indices.length}numIndices(){return this.indicesLength}setIndicesLength(t){this.dirtyIndices=!0,this.indicesLength=t}getIndices(){return this.indices}getVertexSizeInFloats(){let t=0;for(var e=0;e<this.attributes.length;e++){t+=this.attributes[e].numElements}return t}setVertices(t){if(this.dirtyVertices=!0,t.length>this.vertices.length)throw Error("Mesh can't store more than "+this.maxVertices()+" vertices");this.vertices.set(t,0),this.verticesLength=t.length}setIndices(t){if(this.dirtyIndices=!0,t.length>this.indices.length)throw Error("Mesh can't store more than "+this.maxIndices()+" indices");this.indices.set(t,0),this.indicesLength=t.length}draw(t,e){this.drawWithOffset(t,e,0,this.indicesLength>0?this.indicesLength:this.verticesLength/this.elementsPerVertex)}drawWithOffset(t,e,i,s){let r=this.context.gl;(this.dirtyVertices||this.dirtyIndices)&&this.update(),this.bind(t),this.indicesLength>0?r.drawElements(e,s,r.UNSIGNED_SHORT,2*i):r.drawArrays(e,i,s),this.unbind(t)}bind(t){let e=this.context.gl;e.bindBuffer(e.ARRAY_BUFFER,this.verticesBuffer);let i=0;for(let s=0;s<this.attributes.length;s++){let r=this.attributes[s],n=t.getAttributeLocation(r.name);e.enableVertexAttribArray(n),e.vertexAttribPointer(n,r.numElements,e.FLOAT,!1,4*this.elementsPerVertex,4*i),i+=r.numElements}this.indicesLength>0&&e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,this.indicesBuffer)}unbind(t){let e=this.context.gl;for(let i=0;i<this.attributes.length;i++){let s=this.attributes[i],r=t.getAttributeLocation(s.name);e.disableVertexAttribArray(r)}e.bindBuffer(e.ARRAY_BUFFER,null),this.indicesLength>0&&e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,null)}update(){let t=this.context.gl;this.dirtyVertices&&(this.verticesBuffer||(this.verticesBuffer=t.createBuffer()),t.bindBuffer(t.ARRAY_BUFFER,this.verticesBuffer),t.bufferData(t.ARRAY_BUFFER,this.vertices.subarray(0,this.verticesLength),t.DYNAMIC_DRAW),this.dirtyVertices=!1),this.dirtyIndices&&(this.indicesBuffer||(this.indicesBuffer=t.createBuffer()),t.bindBuffer(t.ELEMENT_ARRAY_BUFFER,this.indicesBuffer),t.bufferData(t.ELEMENT_ARRAY_BUFFER,this.indices.subarray(0,this.indicesLength),t.DYNAMIC_DRAW),this.dirtyIndices=!1)}restore(){this.verticesBuffer=null,this.indicesBuffer=null,this.update()}dispose(){this.context.removeRestorable(this);let t=this.context.gl;t.deleteBuffer(this.verticesBuffer),t.deleteBuffer(this.indicesBuffer)}},Ji=class{constructor(t,e,i){this.name=t,this.type=e,this.numElements=i}},Qi=class extends Ji{constructor(){super(Zi.POSITION,ss.Float,2)}},$i=class extends Ji{constructor(){super(Zi.POSITION,ss.Float,3)}},ts=class extends Ji{constructor(t=0){super(Zi.TEXCOORDS+(0==t?"":t),ss.Float,2)}},es=class extends Ji{constructor(){super(Zi.COLOR,ss.Float,4)}},is=class extends Ji{constructor(){super(Zi.COLOR2,ss.Float,4)}},ss=(t=>(t[t.Float=0]="Float",t))(ss||{}),rs=class{constructor(t,e=!0,i=10920){if(this.drawCalls=0,this.isDrawing=!1,this.shader=null,this.lastTexture=null,this.verticesLength=0,this.indicesLength=0,this.cullWasEnabled=!1,i>10920)throw new Error("Can't have more than 10920 triangles per batch: "+i);this.context=t instanceof yi?t:new yi(t);let s=e?[new Qi,new es,new ts,new is]:[new Qi,new es,new ts];this.mesh=new Ki(t,s,i,3*i);let r=this.context.gl;this.srcColorBlend=r.SRC_ALPHA,this.srcAlphaBlend=r.ONE,this.dstBlend=r.ONE_MINUS_SRC_ALPHA}begin(t){if(this.isDrawing)throw new Error("PolygonBatch is already drawing. Call PolygonBatch.end() before calling PolygonBatch.begin()");this.drawCalls=0,this.shader=t,this.lastTexture=null,this.isDrawing=!0;let e=this.context.gl;e.enable(e.BLEND),e.blendFuncSeparate(this.srcColorBlend,this.dstBlend,this.srcAlphaBlend,this.dstBlend),this.cullWasEnabled=e.isEnabled(e.CULL_FACE),this.cullWasEnabled&&e.disable(e.CULL_FACE)}setBlendMode(t,e,i){if((this.srcColorBlend!=t||this.srcAlphaBlend!=e||this.dstBlend!=i)&&(this.srcColorBlend=t,this.srcAlphaBlend=e,this.dstBlend=i,this.isDrawing)){this.flush(),this.context.gl.blendFuncSeparate(t,i,e,i)}}draw(t,e,i){t!=this.lastTexture?(this.flush(),this.lastTexture=t):(this.verticesLength+e.length>this.mesh.getVertices().length||this.indicesLength+i.length>this.mesh.getIndices().length)&&this.flush();let s=this.mesh.numVertices();this.mesh.getVertices().set(e,this.verticesLength),this.verticesLength+=e.length,this.mesh.setVerticesLength(this.verticesLength);let r=this.mesh.getIndices();for(let t=this.indicesLength,e=0;e<i.length;t++,e++)r[t]=i[e]+s;this.indicesLength+=i.length,this.mesh.setIndicesLength(this.indicesLength)}flush(){if(0!=this.verticesLength){if(!this.lastTexture)throw new Error("No texture set.");if(!this.shader)throw new Error("No shader set.");this.lastTexture.bind(),this.mesh.draw(this.shader,this.context.gl.TRIANGLES),this.verticesLength=0,this.indicesLength=0,this.mesh.setVerticesLength(0),this.mesh.setIndicesLength(0),this.drawCalls++,rs.globalDrawCalls++}}end(){if(!this.isDrawing)throw new Error("PolygonBatch is not drawing. Call PolygonBatch.begin() before calling PolygonBatch.end()");(this.verticesLength>0||this.indicesLength>0)&&this.flush(),this.shader=null,this.lastTexture=null,this.isDrawing=!1;let t=this.context.gl;t.disable(t.BLEND),this.cullWasEnabled&&t.enable(t.CULL_FACE)}getDrawCalls(){return this.drawCalls}static getAndResetGlobalDrawCalls(){let t=rs.globalDrawCalls;return rs.globalDrawCalls=0,t}dispose(){this.mesh.dispose()}},ns=rs;ns.globalDrawCalls=0;var as=class{constructor(t,e=10920){if(this.isDrawing=!1,this.shapeType=os.Filled,this.color=new h(1,1,1,1),this.shader=null,this.vertexIndex=0,this.tmp=new v,e>10920)throw new Error("Can't have more than 10920 triangles per batch: "+e);this.context=t instanceof yi?t:new yi(t),this.mesh=new Ki(t,[new Qi,new es],e,0);let i=this.context.gl;this.srcColorBlend=i.SRC_ALPHA,this.srcAlphaBlend=i.ONE,this.dstBlend=i.ONE_MINUS_SRC_ALPHA}begin(t){if(this.isDrawing)throw new Error("ShapeRenderer.begin() has already been called");this.shader=t,this.vertexIndex=0,this.isDrawing=!0;let e=this.context.gl;e.enable(e.BLEND),e.blendFuncSeparate(this.srcColorBlend,this.dstBlend,this.srcAlphaBlend,this.dstBlend)}setBlendMode(t,e,i){if(this.srcColorBlend=t,this.srcAlphaBlend=e,this.dstBlend=i,this.isDrawing){this.flush(),this.context.gl.blendFuncSeparate(t,i,e,i)}}setColor(t){this.color.setFromColor(t)}setColorWith(t,e,i,s){this.color.set(t,e,i,s)}point(t,e,i){this.check(os.Point,1),i||(i=this.color),this.vertex(t,e,i)}line(t,e,i,s,r){this.check(os.Line,2);this.mesh.getVertices(),this.vertexIndex;r||(r=this.color),this.vertex(t,e,r),this.vertex(i,s,r)}triangle(t,e,i,s,r,n,a,o,h,l){this.check(t?os.Filled:os.Line,3);this.mesh.getVertices(),this.vertexIndex;o||(o=this.color),h||(h=this.color),l||(l=this.color),t?(this.vertex(e,i,o),this.vertex(s,r,h),this.vertex(n,a,l)):(this.vertex(e,i,o),this.vertex(s,r,h),this.vertex(s,r,o),this.vertex(n,a,h),this.vertex(n,a,o),this.vertex(e,i,h))}quad(t,e,i,s,r,n,a,o,h,l,c,u,d){this.check(t?os.Filled:os.Line,3);this.mesh.getVertices(),this.vertexIndex;l||(l=this.color),c||(c=this.color),u||(u=this.color),d||(d=this.color),t?(this.vertex(e,i,l),this.vertex(s,r,c),this.vertex(n,a,u),this.vertex(n,a,u),this.vertex(o,h,d),this.vertex(e,i,l)):(this.vertex(e,i,l),this.vertex(s,r,c),this.vertex(s,r,c),this.vertex(n,a,u),this.vertex(n,a,u),this.vertex(o,h,d),this.vertex(o,h,d),this.vertex(e,i,l))}rect(t,e,i,s,r,n){this.quad(t,e,i,e+s,i,e+s,i+r,e,i+r,n,n,n,n)}rectLine(t,e,i,s,r,n,a){this.check(t?os.Filled:os.Line,8),a||(a=this.color);let o=this.tmp.set(r-i,e-s);o.normalize(),n*=.5;let h=o.x*n,l=o.y*n;t?(this.vertex(e+h,i+l,a),this.vertex(e-h,i-l,a),this.vertex(s+h,r+l,a),this.vertex(s-h,r-l,a),this.vertex(s+h,r+l,a),this.vertex(e-h,i-l,a)):(this.vertex(e+h,i+l,a),this.vertex(e-h,i-l,a),this.vertex(s+h,r+l,a),this.vertex(s-h,r-l,a),this.vertex(s+h,r+l,a),this.vertex(e+h,i+l,a),this.vertex(s-h,r-l,a),this.vertex(e-h,i-l,a))}x(t,e,i){this.line(t-i,e-i,t+i,e+i),this.line(t-i,e+i,t+i,e-i)}polygon(t,e,i,s){if(i<3)throw new Error("Polygon must contain at least 3 vertices");this.check(os.Line,2*i),s||(s=this.color);this.mesh.getVertices(),this.vertexIndex;i<<=1;let r=t[e<<=1],n=t[e+1],a=e+i;for(let o=e,h=e+i-2;o<h;o+=2){let e=t[o],i=t[o+1],h=0,l=0;o+2>=a?(h=r,l=n):(h=t[o+2],l=t[o+3]),this.vertex(e,i,s),this.vertex(h,l,s)}}circle(t,e,i,s,r,n=0){if(0==n&&(n=Math.max(1,6*c.cbrt(s)|0)),n<=0)throw new Error("segments must be > 0.");r||(r=this.color);let a=2*c.PI/n,o=Math.cos(a),h=Math.sin(a),l=s,u=0;if(t){this.check(os.Filled,3*n+3),n--;for(let t=0;t<n;t++){this.vertex(e,i,r),this.vertex(e+l,i+u,r);let t=l;l=o*l-h*u,u=h*t+o*u,this.vertex(e+l,i+u,r)}this.vertex(e,i,r),this.vertex(e+l,i+u,r)}else{this.check(os.Line,2*n+2);for(let t=0;t<n;t++){this.vertex(e+l,i+u,r);let t=l;l=o*l-h*u,u=h*t+o*u,this.vertex(e+l,i+u,r)}this.vertex(e+l,i+u,r)}l=s,u=0,this.vertex(e+l,i+u,r)}curve(t,e,i,s,r,n,a,o,h,l){this.check(os.Line,2*h+2),l||(l=this.color);let c=1/h,u=c*c,d=c*c*c,f=3*c,p=3*u,g=6*u,m=6*d,x=t-2*i+r,v=e-2*s+n,y=3*(i-r)-t+a,w=3*(s-n)-e+o,b=t,A=e,M=(i-t)*f+x*p+y*d,E=(s-e)*f+v*p+w*d,S=x*g+y*m,T=v*g+w*m,I=y*m,C=w*m;for(;h-- >0;)this.vertex(b,A,l),b+=M,A+=E,M+=S,E+=T,S+=I,T+=C,this.vertex(b,A,l);this.vertex(b,A,l),this.vertex(a,o,l)}vertex(t,e,i){let s=this.vertexIndex,r=this.mesh.getVertices();r[s++]=t,r[s++]=e,r[s++]=i.r,r[s++]=i.g,r[s++]=i.b,r[s++]=i.a,this.vertexIndex=s}end(){if(!this.isDrawing)throw new Error("ShapeRenderer.begin() has not been called");this.flush();let t=this.context.gl;t.disable(t.BLEND),this.isDrawing=!1}flush(){if(0!=this.vertexIndex){if(!this.shader)throw new Error("No shader set.");this.mesh.setVerticesLength(this.vertexIndex),this.mesh.draw(this.shader,this.shapeType),this.vertexIndex=0}}check(t,e){if(!this.isDrawing)throw new Error("ShapeRenderer.begin() has not been called");if(this.shapeType==t){if(!(this.mesh.maxVertices()-this.mesh.numVertices()<e))return;this.flush()}else this.flush(),this.shapeType=t}dispose(){this.mesh.dispose()}},os=(t=>(t[t.Point=0]="Point",t[t.Line=1]="Line",t[t.Filled=4]="Filled",t))(os||{}),hs=class{constructor(t){this.boneLineColor=new h(1,0,0,1),this.boneOriginColor=new h(0,1,0,1),this.attachmentLineColor=new h(0,0,1,.5),this.triangleLineColor=new h(1,.64,0,.5),this.pathColor=(new h).setFromString("FF7F00"),this.clipColor=new h(.8,0,0,2),this.aabbColor=new h(0,1,0,.5),this.drawBones=!0,this.drawRegionAttachments=!0,this.drawBoundingBoxes=!0,this.drawMeshHull=!0,this.drawMeshTriangles=!0,this.drawPaths=!0,this.drawSkeletonXY=!1,this.drawClipping=!0,this.premultipliedAlpha=!1,this.scale=1,this.boneWidth=2,this.bounds=new ci,this.temp=new Array,this.vertices=g.newFloatArray(2048),this.context=t instanceof yi?t:new yi(t)}draw(t,e,i){let s=e.x,r=e.y,n=this.context.gl,a=this.premultipliedAlpha?n.ONE:n.SRC_ALPHA;t.setBlendMode(a,n.ONE,n.ONE_MINUS_SRC_ALPHA);let o=e.bones;if(this.drawBones){t.setColor(this.boneLineColor);for(let e=0,s=o.length;e<s;e++){let s=o[e];if(i&&i.indexOf(s.data.name)>-1)continue;if(!s.parent)continue;let r=s.data.length*s.a+s.worldX,n=s.data.length*s.c+s.worldY;t.rectLine(!0,s.worldX,s.worldY,r,n,this.boneWidth*this.scale)}this.drawSkeletonXY&&t.x(s,r,4*this.scale)}if(this.drawRegionAttachments){t.setColor(this.attachmentLineColor);let i=e.slots;for(let e=0,s=i.length;e<s;e++){let s=i[e],r=s.getAttachment();if(r instanceof re){let e=r,i=this.vertices;e.computeWorldVertices(s,i,0,2),t.line(i[0],i[1],i[2],i[3]),t.line(i[2],i[3],i[4],i[5]),t.line(i[4],i[5],i[6],i[7]),t.line(i[6],i[7],i[0],i[1])}}}if(this.drawMeshHull||this.drawMeshTriangles){let i=e.slots;for(let e=0,s=i.length;e<s;e++){let s=i[e];if(!s.bone.active)continue;let r=s.getAttachment();if(!(r instanceof te))continue;let n=r,a=this.vertices;n.computeWorldVertices(s,0,n.worldVerticesLength,a,0,2);let o=n.triangles,h=n.hullLength;if(this.drawMeshTriangles){t.setColor(this.triangleLineColor);for(let e=0,i=o.length;e<i;e+=3){let i=2*o[e],s=2*o[e+1],r=2*o[e+2];t.triangle(!1,a[i],a[i+1],a[s],a[s+1],a[r],a[r+1])}}if(this.drawMeshHull&&h>0){t.setColor(this.attachmentLineColor),h=2*(h>>1);let e=a[h-2],i=a[h-1];for(let s=0,r=h;s<r;s+=2){let r=a[s],n=a[s+1];t.line(r,n,e,i),e=r,i=n}}}}if(this.drawBoundingBoxes){let i=this.bounds;i.update(e,!0),t.setColor(this.aabbColor),t.rect(!1,i.minX,i.minY,i.getWidth(),i.getHeight());let s=i.polygons,r=i.boundingBoxes;for(let e=0,i=s.length;e<i;e++){let i=s[e];t.setColor(r[e].color),t.polygon(i,0,i.length)}}if(this.drawPaths){let i=e.slots;for(let e=0,s=i.length;e<s;e++){let s=i[e];if(!s.bone.active)continue;let r=s.getAttachment();if(!(r instanceof ee))continue;let n=r,a=n.worldVerticesLength,o=this.temp=g.setArraySize(this.temp,a,0);n.computeWorldVertices(s,0,a,o,0,2);let h=this.pathColor,l=o[2],c=o[3],u=0,d=0;if(n.closed){t.setColor(h);let e=o[0],i=o[1],s=o[a-2],r=o[a-1];u=o[a-4],d=o[a-3],t.curve(l,c,e,i,s,r,u,d,32),t.setColor(hs.LIGHT_GRAY),t.line(l,c,e,i),t.line(u,d,s,r)}a-=4;for(let e=4;e<a;e+=6){let i=o[e],s=o[e+1],r=o[e+2],n=o[e+3];u=o[e+4],d=o[e+5],t.setColor(h),t.curve(l,c,i,s,r,n,u,d,32),t.setColor(hs.LIGHT_GRAY),t.line(l,c,i,s),t.line(u,d,r,n),l=u,c=d}}}if(this.drawBones){t.setColor(this.boneOriginColor);for(let e=0,s=o.length;e<s;e++){let s=o[e];i&&i.indexOf(s.data.name)>-1||t.circle(!0,s.worldX,s.worldY,3*this.scale,this.boneOriginColor,8)}}if(this.drawClipping){let i=e.slots;t.setColor(this.clipColor);for(let e=0,s=i.length;e<s;e++){let s=i[e];if(!s.bone.active)continue;let r=s.getAttachment();if(!(r instanceof Wt))continue;let n=r,a=n.worldVerticesLength,o=this.temp=g.setArraySize(this.temp,a,0);n.computeWorldVertices(s,0,a,o,0,2);for(let e=0,i=o.length;e<i;e+=2){let i=o[e],s=o[e+1],r=o[(e+2)%o.length],n=o[(e+3)%o.length];t.line(i,s,r,n)}}}}dispose(){}},ls=hs;ls.LIGHT_GRAY=new h(192/255,192/255,192/255,1),ls.GREEN=new h(0,1,0,1);var cs=class{constructor(t,e,i){this.vertices=t,this.numVertices=e,this.numFloats=i}},us=class{constructor(t,e=!0){this.premultipliedAlpha=!1,this.tempColor=new h,this.tempColor2=new h,this.vertexSize=8,this.twoColorTint=!1,this.renderable=new cs([],0,0),this.clipper=new di,this.temp=new v,this.temp2=new v,this.temp3=new h,this.temp4=new h,this.twoColorTint=e,e&&(this.vertexSize+=4),this.vertices=g.newFloatArray(1024*this.vertexSize)}draw(t,e,i=-1,s=-1,r=null){let n,a,o,h=this.clipper,l=this.premultipliedAlpha,c=this.twoColorTint,u=null,d=(this.temp,this.temp2,this.temp3,this.temp4,this.renderable),f=e.drawOrder,p=e.color,m=c?12:8,x=!1;-1==i&&(x=!0);for(let e=0,v=f.length;e<v;e++){let v=h.isClipping()?2:m,y=f[e];if(!y.bone.active){h.clipEndWithSlot(y);continue}if(i>=0&&i==y.data.index&&(x=!0),!x){h.clipEndWithSlot(y);continue}s>=0&&s==y.data.index&&(x=!1);let w,b=y.getAttachment();if(b instanceof re){let t=b;d.vertices=this.vertices,d.numVertices=4,d.numFloats=v<<2,t.computeWorldVertices(y,d.vertices,0,v),a=us.QUAD_TRIANGLES,n=t.uvs,w=t.region.renderObject.page.texture,o=t.color}else{if(!(b instanceof te)){if(b instanceof Wt){let t=b;h.clipStart(y,t);continue}h.clipEndWithSlot(y);continue}{let t=b;d.vertices=this.vertices,d.numVertices=t.worldVerticesLength>>1,d.numFloats=d.numVertices*v,d.numFloats>d.vertices.length&&(d.vertices=this.vertices=g.newFloatArray(d.numFloats)),t.computeWorldVertices(y,0,t.worldVerticesLength,d.vertices,0,v),a=t.triangles,w=t.region.renderObject.page.texture,n=t.uvs,o=t.color}}if(w){let e=y.color,i=this.tempColor;i.r=p.r*e.r*o.r,i.g=p.g*e.g*o.g,i.b=p.b*e.b*o.b,i.a=p.a*e.a*o.a,l&&(i.r*=i.a,i.g*=i.a,i.b*=i.a);let s=this.tempColor2;y.darkColor?(l?(s.r=y.darkColor.r*i.a,s.g=y.darkColor.g*i.a,s.b=y.darkColor.b*i.a):s.setFromColor(y.darkColor),s.a=l?1:0):s.set(0,0,0,1);let f=y.data.blendMode;if(f!=u&&(u=f,t.setBlendMode(bi.getSourceColorGLBlendMode(u,l),bi.getSourceAlphaGLBlendMode(u),bi.getDestGLBlendMode(u))),h.isClipping()){h.clipTriangles(d.vertices,d.numFloats,a,a.length,n,i,s,c);let e=new Float32Array(h.clippedVertices),o=h.clippedTriangles;r&&r(d.vertices,d.numFloats,m),t.draw(w,e,o)}else{let e=d.vertices;if(c)for(let t=2,r=0,a=d.numFloats;t<a;t+=m,r+=2)e[t]=i.r,e[t+1]=i.g,e[t+2]=i.b,e[t+3]=i.a,e[t+4]=n[r],e[t+5]=n[r+1],e[t+6]=s.r,e[t+7]=s.g,e[t+8]=s.b,e[t+9]=s.a;else for(let t=2,s=0,r=d.numFloats;t<r;t+=m,s+=2)e[t]=i.r,e[t+1]=i.g,e[t+2]=i.b,e[t+3]=i.a,e[t+4]=n[s],e[t+5]=n[s+1];let o=d.vertices.subarray(0,d.numFloats);r&&r(d.vertices,d.numFloats,m),t.draw(w,o,a)}}h.clipEndWithSlot(y)}h.clipEnd()}},ds=us;ds.QUAD_TRIANGLES=[0,1,2,2,3,0];var fs,ps,gs,ms=[0,0,1,1,1,1,0,0,0,0,1,1,1,1,0,0,0,0,1,1,1,1,0,0,0,0,1,1,1,1,0,0],xs=[0,1,2,2,3,0],vs=new h(1,1,1,1),ys=class{constructor(t,e,i=!0){this.twoColorTint=!1,this.activeRenderer=null,this.canvas=t,this.context=e instanceof yi?e:new yi(e),this.twoColorTint=i,this.camera=new Wi(t.width,t.height),this.batcherShader=i?Zi.newTwoColoredTextured(this.context):Zi.newColoredTextured(this.context),this.batcher=new ns(this.context,i),this.shapesShader=Zi.newColored(this.context),this.shapes=new as(this.context),this.skeletonRenderer=new ds(this.context,i),this.skeletonDebugRenderer=new ls(this.context)}dispose(){this.batcher.dispose(),this.batcherShader.dispose(),this.shapes.dispose(),this.shapesShader.dispose(),this.skeletonDebugRenderer.dispose()}begin(){this.camera.update(),this.enableRenderer(this.batcher)}drawSkeleton(t,e=!1,i=-1,s=-1,r=null){this.enableRenderer(this.batcher),this.skeletonRenderer.premultipliedAlpha=e,this.skeletonRenderer.draw(this.batcher,t,i,s,r)}drawSkeletonDebug(t,e=!1,i){this.enableRenderer(this.shapes),this.skeletonDebugRenderer.premultipliedAlpha=e,this.skeletonDebugRenderer.draw(this.shapes,t,i)}drawTexture(t,e,i,s,r,n){this.enableRenderer(this.batcher),n||(n=vs);var a=0;ms[a++]=e,ms[a++]=i,ms[a++]=n.r,ms[a++]=n.g,ms[a++]=n.b,ms[a++]=n.a,ms[a++]=0,ms[a++]=1,this.twoColorTint&&(ms[a++]=0,ms[a++]=0,ms[a++]=0,ms[a++]=0),ms[a++]=e+s,ms[a++]=i,ms[a++]=n.r,ms[a++]=n.g,ms[a++]=n.b,ms[a++]=n.a,ms[a++]=1,ms[a++]=1,this.twoColorTint&&(ms[a++]=0,ms[a++]=0,ms[a++]=0,ms[a++]=0),ms[a++]=e+s,ms[a++]=i+r,ms[a++]=n.r,ms[a++]=n.g,ms[a++]=n.b,ms[a++]=n.a,ms[a++]=1,ms[a++]=0,this.twoColorTint&&(ms[a++]=0,ms[a++]=0,ms[a++]=0,ms[a++]=0),ms[a++]=e,ms[a++]=i+r,ms[a++]=n.r,ms[a++]=n.g,ms[a++]=n.b,ms[a++]=n.a,ms[a++]=0,ms[a++]=0,this.twoColorTint&&(ms[a++]=0,ms[a++]=0,ms[a++]=0,ms[a]=0),this.batcher.draw(t,ms,xs)}drawTextureUV(t,e,i,s,r,n,a,o,h,l){this.enableRenderer(this.batcher),l||(l=vs);var c=0;ms[c++]=e,ms[c++]=i,ms[c++]=l.r,ms[c++]=l.g,ms[c++]=l.b,ms[c++]=l.a,ms[c++]=n,ms[c++]=a,this.twoColorTint&&(ms[c++]=0,ms[c++]=0,ms[c++]=0,ms[c++]=0),ms[c++]=e+s,ms[c++]=i,ms[c++]=l.r,ms[c++]=l.g,ms[c++]=l.b,ms[c++]=l.a,ms[c++]=o,ms[c++]=a,this.twoColorTint&&(ms[c++]=0,ms[c++]=0,ms[c++]=0,ms[c++]=0),ms[c++]=e+s,ms[c++]=i+r,ms[c++]=l.r,ms[c++]=l.g,ms[c++]=l.b,ms[c++]=l.a,ms[c++]=o,ms[c++]=h,this.twoColorTint&&(ms[c++]=0,ms[c++]=0,ms[c++]=0,ms[c++]=0),ms[c++]=e,ms[c++]=i+r,ms[c++]=l.r,ms[c++]=l.g,ms[c++]=l.b,ms[c++]=l.a,ms[c++]=n,ms[c++]=h,this.twoColorTint&&(ms[c++]=0,ms[c++]=0,ms[c++]=0,ms[c]=0),this.batcher.draw(t,ms,xs)}drawTextureRotated(t,e,i,s,r,n,a,o,h){this.enableRenderer(this.batcher),h||(h=vs);let l=e+n,u=i+a,d=-n,f=-a,p=s-n,g=r-a,m=d,x=f,v=d,y=g,w=p,b=g,A=p,M=f,E=0,S=0,T=0,I=0,C=0,R=0,k=0,F=0;if(0!=o){let t=c.cosDeg(o),e=c.sinDeg(o);E=t*m-e*x,S=e*m+t*x,k=t*v-e*y,F=e*v+t*y,C=t*w-e*b,R=e*w+t*b,T=C+(E-k),I=R+(S-F)}else E=m,S=x,k=v,F=y,C=w,R=b,T=A,I=M;E+=l,S+=u,T+=l,I+=u,C+=l,R+=u,k+=l,F+=u;var L=0;ms[L++]=E,ms[L++]=S,ms[L++]=h.r,ms[L++]=h.g,ms[L++]=h.b,ms[L++]=h.a,ms[L++]=0,ms[L++]=1,this.twoColorTint&&(ms[L++]=0,ms[L++]=0,ms[L++]=0,ms[L++]=0),ms[L++]=T,ms[L++]=I,ms[L++]=h.r,ms[L++]=h.g,ms[L++]=h.b,ms[L++]=h.a,ms[L++]=1,ms[L++]=1,this.twoColorTint&&(ms[L++]=0,ms[L++]=0,ms[L++]=0,ms[L++]=0),ms[L++]=C,ms[L++]=R,ms[L++]=h.r,ms[L++]=h.g,ms[L++]=h.b,ms[L++]=h.a,ms[L++]=1,ms[L++]=0,this.twoColorTint&&(ms[L++]=0,ms[L++]=0,ms[L++]=0,ms[L++]=0),ms[L++]=k,ms[L++]=F,ms[L++]=h.r,ms[L++]=h.g,ms[L++]=h.b,ms[L++]=h.a,ms[L++]=0,ms[L++]=0,this.twoColorTint&&(ms[L++]=0,ms[L++]=0,ms[L++]=0,ms[L]=0),this.batcher.draw(t,ms,xs)}drawRegion(t,e,i,s,r,n){this.enableRenderer(this.batcher),n||(n=vs);var a=0;ms[a++]=e,ms[a++]=i,ms[a++]=n.r,ms[a++]=n.g,ms[a++]=n.b,ms[a++]=n.a,ms[a++]=t.u,ms[a++]=t.v2,this.twoColorTint&&(ms[a++]=0,ms[a++]=0,ms[a++]=0,ms[a++]=0),ms[a++]=e+s,ms[a++]=i,ms[a++]=n.r,ms[a++]=n.g,ms[a++]=n.b,ms[a++]=n.a,ms[a++]=t.u2,ms[a++]=t.v2,this.twoColorTint&&(ms[a++]=0,ms[a++]=0,ms[a++]=0,ms[a++]=0),ms[a++]=e+s,ms[a++]=i+r,ms[a++]=n.r,ms[a++]=n.g,ms[a++]=n.b,ms[a++]=n.a,ms[a++]=t.u2,ms[a++]=t.v,this.twoColorTint&&(ms[a++]=0,ms[a++]=0,ms[a++]=0,ms[a++]=0),ms[a++]=e,ms[a++]=i+r,ms[a++]=n.r,ms[a++]=n.g,ms[a++]=n.b,ms[a++]=n.a,ms[a++]=t.u,ms[a++]=t.v,this.twoColorTint&&(ms[a++]=0,ms[a++]=0,ms[a++]=0,ms[a]=0),this.batcher.draw(t.page.texture,ms,xs)}line(t,e,i,s,r,n){this.enableRenderer(this.shapes),this.shapes.line(t,e,i,s,r)}triangle(t,e,i,s,r,n,a,o,h,l){this.enableRenderer(this.shapes),this.shapes.triangle(t,e,i,s,r,n,a,o,h,l)}quad(t,e,i,s,r,n,a,o,h,l,c,u,d){this.enableRenderer(this.shapes),this.shapes.quad(t,e,i,s,r,n,a,o,h,l,c,u,d)}rect(t,e,i,s,r,n){this.enableRenderer(this.shapes),this.shapes.rect(t,e,i,s,r,n)}rectLine(t,e,i,s,r,n,a){this.enableRenderer(this.shapes),this.shapes.rectLine(t,e,i,s,r,n,a)}polygon(t,e,i,s){this.enableRenderer(this.shapes),this.shapes.polygon(t,e,i,s)}circle(t,e,i,s,r,n=0){this.enableRenderer(this.shapes),this.shapes.circle(t,e,i,s,r,n)}curve(t,e,i,s,r,n,a,o,h,l){this.enableRenderer(this.shapes),this.shapes.curve(t,e,i,s,r,n,a,o,h,l)}end(){this.activeRenderer===this.batcher?this.batcher.end():this.activeRenderer===this.shapes&&this.shapes.end(),this.activeRenderer=null}resize(t){let e=this.canvas;var i=window.devicePixelRatio||1,s=Math.round(e.clientWidth*i),r=Math.round(e.clientHeight*i);if(e.width==s&&e.height==r||(e.width=s,e.height=r),this.context.gl.viewport(0,0,e.width,e.height),t===ws.Expand)this.camera.setViewport(s,r);else if(t===ws.Fit){let t=e.width,i=e.height,s=this.camera.viewportWidth,r=this.camera.viewportHeight,n=r/s<i/t?s/t:r/i;this.camera.setViewport(t*n,i*n)}this.camera.update()}enableRenderer(t){this.activeRenderer!==t&&(this.end(),t instanceof ns?(this.batcherShader.bind(),this.batcherShader.setUniform4x4f(Zi.MVP_MATRIX,this.camera.projectionView.values),this.batcherShader.setUniformi("u_texture",0),this.batcher.begin(this.batcherShader),this.activeRenderer=this.batcher):t instanceof as?(this.shapesShader.bind(),this.shapesShader.setUniform4x4f(Zi.MVP_MATRIX,this.camera.projectionView.values),this.shapes.begin(this.shapesShader),this.activeRenderer=this.shapes):this.activeRenderer=this.skeletonDebugRenderer)}},ws=(t=>(t[t.Stretch=0]="Stretch",t[t.Expand=1]="Expand",t[t.Fit=2]="Fit",t))(ws||{}),bs=0,As=163,Ms=class{constructor(t){if(this.logo=null,this.spinner=null,this.angle=0,this.fadeOut=0,this.fadeIn=0,this.timeKeeper=new y,this.backgroundColor=new h(.135,.135,.135,1),this.tempColor=new h,this.renderer=t,this.timeKeeper.maxDelta=9,!ps){let t=navigator.userAgent.indexOf("Safari")>-1,e=()=>bs++;(ps=new Image).src=Ss,t||(ps.crossOrigin="anonymous"),ps.onload=e,(fs=new Image).src=Es,t||(fs.crossOrigin="anonymous"),fs.onload=e}}dispose(){var t,e;null==(t=this.logo)||t.dispose(),null==(e=this.spinner)||e.dispose()}draw(t=!1){if(bs<2||t&&this.fadeOut>1)return;this.timeKeeper.update();let e=Math.abs(Math.sin(this.timeKeeper.totalTime+.25));this.angle-=200*this.timeKeeper.delta*(1+1.5*Math.pow(e,5));let i=this.tempColor,s=this.renderer,r=s.canvas,n=s.context.gl;if(s.resize(1),s.camera.position.set(r.width/2,r.height/2,0),s.batcher.setBlendMode(n.ONE,n.ONE,n.ONE_MINUS_SRC_ALPHA),t){if(this.fadeOut+=this.timeKeeper.delta*(this.timeKeeper.totalTime<1?2:1),this.fadeOut>1)return;i.setFromColor(this.backgroundColor),e=1-this.fadeOut/1,e=1-(e-1)*(e-1),i.a*=e,i.a>0&&(s.camera.zoom=1,s.begin(),s.quad(!0,0,0,r.width,0,r.width,r.height,0,r.height,i,i,i,i),s.end())}else this.fadeIn+=this.timeKeeper.delta,this.backgroundColor.a>0&&(n.clearColor(this.backgroundColor.r,this.backgroundColor.g,this.backgroundColor.b,this.backgroundColor.a),n.clear(n.COLOR_BUFFER_BIT)),e=1;e*=Math.min(this.fadeIn/1,1),i.set(e,e,e,e),this.logo||(this.logo=new Mi(s.context,ps),this.spinner=new Mi(s.context,fs)),s.camera.zoom=Math.max(1,As/r.height),s.begin(),s.drawTexture(this.logo,(r.width-165)/2,(r.height-108)/2,165,108,i),this.spinner&&s.drawTextureRotated(this.spinner,(r.width-As)/2,(r.height-As)/2,As,As,81.5,81.5,this.angle,i),s.end()}},Es="data:image/png;base64,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",Ss="data:image/png;base64,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",Ts=class{constructor(t,e){this.time=new y,e.pathPrefix||(e.pathPrefix=""),e.app||(e.app={loadAssets:()=>{},initialize:()=>{},update:()=>{},render:()=>{},error:()=>{}}),e.webglConfig&&(e.webglConfig={alpha:!0}),this.htmlCanvas=t,this.context=new yi(t,e.webglConfig),this.renderer=new ys(t,this.context),this.gl=this.context.gl,this.assetManager=new Ei(this.context,e.pathPrefix),this.input=new qi(t),e.app.loadAssets&&e.app.loadAssets(this);let i=()=>{requestAnimationFrame(i),this.time.update(),e.app.update&&e.app.update(this,this.time.delta),e.app.render&&e.app.render(this)},s=()=>{this.assetManager.isLoadingComplete()?this.assetManager.hasErrors()?e.app.error&&e.app.error(this,this.assetManager.getErrors()):(e.app.initialize&&e.app.initialize(this),i()):requestAnimationFrame(s)};requestAnimationFrame(s)}clear(t,e,i,s){this.gl.clearColor(t,e,i,s),this.gl.clear(this.gl.COLOR_BUFFER_BIT)}};return gs=r,((r,n,a,o)=>{if(n&&"object"==typeof n||"function"==typeof n)for(let h of i(n))s.call(r,h)||h===a||t(r,h,{get:()=>n[h],enumerable:!(o=e(n,h))||o.enumerable});return r})(t({},"__esModule",{value:!0}),gs)})();t.exports=e}).call(window)},3524:(t,e,i)=>{var s=i(7473),r=i(4597),n=i(6732),a=i(2482),o=i(704),h=i(3137),l=i(1192),c=new s({Extends:h,initialize:function(t,e,i,s,n,c,u){var d,f,p,g=[],m=t.cacheManager.custom.spine;if(a(e)){var x=e;for(e=r(x,"key"),f=new o(t,{key:e,url:r(x,"jsonURL"),extension:r(x,"jsonExtension","json"),xhrSettings:r(x,"jsonXhrSettings")}),s=r(x,"atlasURL"),n=r(x,"preMultipliedAlpha"),Array.isArray(s)||(s=[s]),d=0;d<s.length;d++)(p=new l(t,{key:e+"!"+d,url:s[d],extension:r(x,"atlasExtension","atlas"),xhrSettings:r(x,"atlasXhrSettings")})).cache=m,g.push(p)}else for(f=new o(t,e,i,c),Array.isArray(s)||(s=[s]),d=0;d<s.length;d++)(p=new l(t,e+"!"+d,s[d],u)).cache=m,g.push(p);g.unshift(f),h.call(this,t,"spine",e,g),this.config.preMultipliedAlpha=n},onFileComplete:function(t){if(-1!==this.files.indexOf(t)&&(this.pending--,"text"===t.type)){for(var e=t.data.split("\n"),i=[e[0]],s=0;s<e.length;s++){var a=e[s];""===a.trim()&&s<e.length-1&&(a=e[s+1],i.push(a))}var o=this.config,h=this.loader,l=h.baseURL,c=h.path,u=h.prefix,d=r(o,"baseURL",this.baseURL),f=r(o,"path",t.src.match(/^.*\//))[0],p=r(o,"prefix",this.prefix),g=r(o,"textureXhrSettings");h.setBaseURL(d),h.setPath(f),h.setPrefix(p);for(var m=0;m<i.length;m++){var x=i[m],v=new n(h,x,x,g);h.keyExists(v)||(this.addToMultiFile(v),h.addFile(v))}h.setBaseURL(l),h.setPath(c),h.setPrefix(u)}},addToCache:function(){if(this.isReadyToProcess()){var t;this.files[0].addToCache();for(var e="",i="",s=!!this.config.preMultipliedAlpha,r=this.loader.textureManager,n=1;n<this.files.length;n++){var a=this.files[n];if("text"===a.type)e=a.key.replace(/![\d]$/,""),t=a.cache,i=i.concat(a.data);else{var o=a.key.trim(),h=o.indexOf("!"),l=o.substr(h+1);r.exists(l)||r.addImage(l,a.data)}a.pendingDestroy()}t.add(e,{preMultipliedAlpha:s,data:i,prefix:this.prefix}),this.complete=!0}}});t.exports=c},4513:(t,e,i)=>{var s=i(2494),r=i(7473),n=i(5851),a=i(3527),o=i(5722),h={canvas:i(6937),webgl:i(6937)},l=i(3524),c=i(8332),u=i(5782),d=i(1984),f=new r({Extends:o,initialize:function(t,e,i){o.call(this,t,e,i);var r=e.game;this.isWebGL=2===r.config.renderType,this.cache=r.cache.addCustom("spine"),this.spineTextures=r.cache.addCustom("spineTextures"),this.json=r.cache.json,this.textures=r.textures,this.drawDebug=!1,this.gl,this.renderer,this.sceneRenderer,this.skeletonRenderer,this.skeletonDebugRenderer,this.plugin=h,this.temp1,this.temp2,this.isWebGL?(this.runtime=h.webgl,this.renderer=r.renderer,this.gl=r.renderer.gl,this.getAtlas=this.getAtlasWebGL):(this.runtime=h.canvas,this.renderer=r.renderer,this.getAtlas=this.getAtlasCanvas),this.renderer||(this.renderer={width:r.scale.width,height:r.scale.height,preRender:d,postRender:d,render:d,destroy:d});e.registerFileType("spine",this.spineFileCallback,t),e.registerGameObject("spine",(function(t,e,s,r,n){var a=this.scene.sys[i],o=new c(this.scene,a,t,e,s,r,n);return this.displayList.add(o),this.updateList.add(o),o}),(function(t,e){void 0===t&&(t={});var r=n(t,"key",null),a=n(t,"animationName",null),o=n(t,"loop",!1),h=this.scene.sys[i],l=new c(this.scene,h,0,0,r,a,o);void 0!==e&&(t.add=e),s(this.scene,l,t);var u=n(t,"skinName",!1);u&&l.setSkinByName(u);var d=n(t,"slotName",!1),f=n(t,"attachmentName",null);return d&&l.setAttachment(d,f),l.refresh()})),e.registerGameObject("spineContainer",(function(t,e,s){var r=this.scene.sys[i],n=new u(this.scene,r,t,e,s);return this.displayList.add(n),n}),(function(t,e){void 0===t&&(t={});var r=n(t,"x",0),a=n(t,"y",0),o=n(t,"children",null),h=this.scene.sys[i],l=new u(this.scene,h,r,a,o);return void 0!==e&&(t.add=e),s(this.scene,l,t),l}))},boot:function(){this.isWebGL?(this.bootWebGL(),this.onResize(),this.game.scale.on(a,this.onResize,this)):this.bootCanvas();var t=this.systems.events;t.once("shutdown",this.shutdown,this),t.once("destroy",this.destroy,this),this.game.events.once("destroy",this.gameDestroy,this)},bootCanvas:function(){this.skeletonRenderer=new h.canvas.SkeletonRenderer(this.scene.sys.context)},bootWebGL:function(){var t=this.renderer.spineSceneRenderer;t||(t=new h.webgl.SceneRenderer(this.renderer.canvas,this.gl,!0),this.renderer.spineSceneRenderer=t),this.sceneRenderer=t,this.skeletonRenderer=t.skeletonRenderer,this.skeletonDebugRenderer=t.skeletonDebugRenderer,this.temp1=new h.webgl.Vector3(0,0,0),this.temp2=new h.webgl.Vector3(0,0,0)},getAtlasCanvas:function(t){var e=this.cache.get(t);if(e){var i,s=this.spineTextures;if(s.has(t))i=s.get(t);else{var r=this.textures;i=new this.runtime.TextureAtlas(e.data,(function(t){return new h.canvas.CanvasTexture(r.get(e.prefix+t).getSourceImage())}))}return i}console.warn("No atlas data for: "+t)},getAtlasWebGL:function(t){var e=this.cache.get(t);if(e){var i,s=this.spineTextures;if(s.has(t))i=s.get(t);else{var r=this.textures,n=this.sceneRenderer.context.gl;n.pixelStorei(n.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),i=new this.runtime.TextureAtlas(e.data,(function(t){return new h.webgl.GLTexture(n,r.get(e.prefix+t).getSourceImage(),!1)}))}return i}console.warn("No atlas data for: "+t)},spineFileCallback:function(t,e,i,s,r,n,a){var o;if(a=a||{},Array.isArray(t))for(var h=0;h<t.length;h++)(o=new l(this,t[h])).prefix=o.prefix||a.prefix||"",this.addFile(o.files);else(o=new l(this,t,e,i,s,r,n)).prefix=o.prefix||a.prefix||"",this.addFile(o.files);return this},worldToLocal:function(t,e,i,s){var r=this.temp1,n=this.temp2,a=this.sceneRenderer.camera;r.set(t+i.x,e-i.y,0);var o=a.viewportWidth,h=a.viewportHeight;return a.screenToWorld(r,o,h),s&&null!==s.parent?(s.parent.worldToLocal(n.set(r.x-i.x,r.y-i.y,0)),new this.runtime.Vector2(n.x,n.y)):s?new this.runtime.Vector2(r.x-i.x,r.y-i.y):new this.runtime.Vector2(r.x,r.y)},getVector2:function(t,e){return new this.runtime.Vector2(t,e)},getVector3:function(t,e,i){return new h.webgl.Vector3(t,e,i)},setDebugBones:function(t){return void 0===t&&(t=!0),this.skeletonDebugRenderer.drawBones=t,this},setDebugRegionAttachments:function(t){return void 0===t&&(t=!0),this.skeletonDebugRenderer.drawRegionAttachments=t,this},setDebugBoundingBoxes:function(t){return void 0===t&&(t=!0),this.skeletonDebugRenderer.drawBoundingBoxes=t,this},setDebugMeshHull:function(t){return void 0===t&&(t=!0),this.skeletonDebugRenderer.drawMeshHull=t,this},setDebugMeshTriangles:function(t){return void 0===t&&(t=!0),this.skeletonDebugRenderer.drawMeshTriangles=t,this},setDebugPaths:function(t){return void 0===t&&(t=!0),this.skeletonDebugRenderer.drawPaths=t,this},setDebugSkeletonXY:function(t){return void 0===t&&(t=!0),this.skeletonDebugRenderer.drawSkeletonXY=t,this},setDebugClipping:function(t){return void 0===t&&(t=!0),this.skeletonDebugRenderer.drawClipping=t,this},setEffect:function(t){return this.sceneRenderer.skeletonRenderer.vertexEffect=t,this},createSkeleton:function(t,e){var i=t,s=t,r=-1!==t.indexOf(".");if(r){var a=t.split(".");i=a.shift(),s=a.join(".")}var o=this.cache.get(i),h=this.getAtlas(i);if(!h)return null;if(!this.spineTextures.has(i)){var l,c,u,d=this.gl;for(this.isWebGL&&d.pixelStorei(d.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),l=0;l<h.pages.length;l++)c=h.pages[l],u=o.prefix?o.prefix+c.name:c.name,this.isWebGL?c.setTexture(new this.runtime.GLTexture(d,this.textures.get(u).getSourceImage(),!1)):c.setTexture(new this.runtime.CanvasTexture(this.textures.get(u).getSourceImage()));this.spineTextures.add(i,h)}var f,p=o.preMultipliedAlpha,g=new this.runtime.AtlasAttachmentLoader(h),m=new this.runtime.SkeletonJson(g);if(e)f=e;else{var x=this.json.get(i);f=r?n(x,s):x}if(f){var v=m.readSkeletonData(f);return{skeletonData:v,skeleton:new this.runtime.Skeleton(v),preMultipliedAlpha:p}}return null},createAnimationState:function(t){var e=new this.runtime.AnimationStateData(t.data);return{stateData:e,state:new this.runtime.AnimationState(e)}},getBounds:function(t){var e=new this.runtime.Vector2,i=new this.runtime.Vector2;return t.getBounds(e,i,[]),{offset:e,size:i}},onResize:function(){var t=this.renderer,e=this.sceneRenderer,i=t.width,s=t.height;e.camera.position.x=i/2,e.camera.position.y=s/2,e.camera.setViewport(i,s)},shutdown:function(){this.systems.events.off("shutdown",this.shutdown,this),this.isWebGL&&this.game.scale.off(a,this.onResize,this)},destroy:function(){this.shutdown(),this.game=null,this.scene=null,this.systems=null,this.cache=null,this.spineTextures=null,this.json=null,this.textures=null,this.skeletonRenderer=null,this.gl=null},gameDestroy:function(){this.pluginManager.removeGameObject("spine",!0,!0),this.pluginManager.removeGameObject("spineContainer",!0,!0),this.pluginManager=null;var t=this.renderer.spineSceneRenderer;t&&t.dispose(),this.renderer.spineSceneRenderer=null,this.sceneRenderer=null}});f.SpineGameObject=c,f.SpineContainer=u,t.exports=f},5782:(t,e,i)=>{var s=i(7473),r=i(7361),n=i(7738),a=new s({Extends:r,Mixins:[n],initialize:function(t,e,i,s,n){r.call(this,t,i,s,n),this.type="Spine",this.plugin=e},preDestroy:function(){this.removeAll(!!this.exclusive),this.localTransform.destroy(),this.tempTransformMatrix.destroy(),this.list=[],this._displayList=null,this.plugin=null}});t.exports=a},7738:(t,e,i)=>{var s=i(1984),r=i(1984);s=i(434),t.exports={renderWebGL:s,renderCanvas:r}},434:t=>{t.exports=function(t,e,i,s){var r=e.plugin.sceneRenderer,n=e.list;if(0!==n.length){i.addToRenderList(e);var a=e.localTransform;s?(a.loadIdentity(),a.multiply(s),a.translate(e.x,e.y),a.rotate(e.rotation),a.scale(e.scaleX,e.scaleY)):a.applyITRS(e.x,e.y,e.rotation,e.scaleX,e.scaleY),t.newType&&(t.pipelines.clear(),r.begin());var o=t.nextTypeMatch;t.nextTypeMatch=!0,t.newType=!1;for(var h=0;h<n.length;h++){var l=n[h];if(l.willRender(i,e)){var c=l.mask;c&&(r.end(),t.pipelines.rebind(),c.preRenderWebGL(t,l,i),t.pipelines.clear(),r.begin()),l.renderWebGL(t,l,i,a,e),c&&(r.end(),t.pipelines.rebind(),c.postRenderWebGL(t,i),t.pipelines.clear(),r.begin())}}t.nextTypeMatch=o,o||(r.end(),t.pipelines.rebind())}else r.batcher.isDrawing&&t.finalType&&(r.end(),t.pipelines.rebind())}},6576:t=>{t.exports="complete"},8621:t=>{t.exports="dispose"},8944:t=>{t.exports="end"},7494:t=>{t.exports="event"},1908:t=>{t.exports="interrupted"},5591:t=>{t.exports="start"},5146:(t,e,i)=>{t.exports={COMPLETE:i(6576),DISPOSE:i(8621),END:i(8944),EVENT:i(7494),INTERRUPTED:i(1908),START:i(5591)}},8332:(t,e,i)=>{var s=i(6412),r=i(2915),n=i(7473),a=i(1991),o=i(3131),h=i(9660),l=i(4627),c=i(3212),u=i(8414),d=i(3426),f=i(7149),p=i(2273),g=i(4208),m=i(5146),x=i(2762),v=new n({Extends:p,Mixins:[a,o,h,l,c,u,x],initialize:function(t,e,i,s,r,n,a){p.call(this,t,"Spine"),this.plugin=e,this.skeleton=null,this.skeletonData=null,this.state=null,this.stateData=null,this.root=null,this.bounds=null,this.drawDebug=!1,this.timeScale=1,this.displayOriginX=0,this.displayOriginY=0,this.preMultipliedAlpha=!1,this.blendMode=-1,this.setPosition(i,s),r&&this.setSkeleton(r,n,a)},willRender:function(t,e){var i=!this.skeleton||!(15!==this.renderFlags||0!==this.cameraFilter&&this.cameraFilter&t.id);if(!e&&!i&&this.parentContainer){var s=this.plugin,r=s.sceneRenderer;s.gl&&r.batcher.isDrawing&&(r.end(),s.renderer.pipelines.rebind())}return i},setAlpha:function(t,e){if(void 0===t&&(t=1),e){var i=this.findSlot(e);i&&(i.color.a=r(t,0,1))}else this.alpha=t;return this},alpha:{get:function(){return this.skeleton.color.a},set:function(t){var e=r(t,0,1);this.skeleton&&(this.skeleton.color.a=e),0===e?this.renderFlags&=-3:this.renderFlags|=2}},red:{get:function(){return this.skeleton.color.r},set:function(t){var e=r(t,0,1);this.skeleton&&(this.skeleton.color.r=e)}},green:{get:function(){return this.skeleton.color.g},set:function(t){var e=r(t,0,1);this.skeleton&&(this.skeleton.color.g=e)}},blue:{get:function(){return this.skeleton.color.b},set:function(t){var e=r(t,0,1);this.skeleton&&(this.skeleton.color.b=e)}},setColor:function(t,e){void 0===t&&(t=16777215);var i=(t>>16&255)/255,s=(t>>8&255)/255,r=(255&t)/255,n=t>16777215?(t>>>24)/255:null,a=this.skeleton;if(e){var o=this.findSlot(e);o&&(a=o)}return a.color.r=i,a.color.g=s,a.color.b=r,null!==n&&(a.color.a=n),this},setSkeletonFromJSON:function(t,e,i,s){return this.setSkeleton(t,e,i,s)},setSkeleton:function(t,e,i,s){this.state&&(this.state.clearListeners(),this.state.clearListenerNotifications());var r=this.plugin.createSkeleton(t,s);this.skeletonData=r.skeletonData,this.preMultipliedAlpha=r.preMultipliedAlpha;var n=r.skeleton;return n.setSkin(),n.setToSetupPose(),this.skeleton=n,r=this.plugin.createAnimationState(n),this.state&&(this.state.clearListeners(),this.state.clearListenerNotifications()),this.state=r.state,this.stateData=r.stateData,this.state.addListener({event:this.onEvent.bind(this),complete:this.onComplete.bind(this),start:this.onStart.bind(this),end:this.onEnd.bind(this),dispose:this.onDispose.bind(this),interrupted:this.onInterrupted.bind(this)}),e&&this.setAnimation(0,e,i),this.root=this.getRootBone(),this.root&&(this.root.rotation=g(d(this.rotation))+90),this.state.apply(n),n.updateCache(),this.updateSize()},onComplete:function(t){this.emit(m.COMPLETE,t)},onDispose:function(t){this.emit(m.DISPOSE,t)},onEnd:function(t){this.emit(m.END,t)},onEvent:function(t,e){this.emit(m.EVENT,t,e)},onInterrupted:function(t){this.emit(m.INTERRUPTED,t)},onStart:function(t){this.emit(m.START,t)},refresh:function(){return this.root&&(this.root.rotation=g(d(this.rotation))+90),this.updateSize(),this.skeleton.updateCache(),this},setSize:function(t,e,i,s){var r=this.skeleton;return void 0===t&&(t=r.data.width),void 0===e&&(e=r.data.height),void 0===i&&(i=0),void 0===s&&(s=0),this.width=t,this.height=e,this.displayOriginX=r.x-i,this.displayOriginY=r.y-s,this},setOffset:function(t,e){var i=this.skeleton;return void 0===t&&(t=0),void 0===e&&(e=0),this.displayOriginX=i.x-t,this.displayOriginY=i.y-e,this},updateSize:function(){var t=this.skeleton,e=this.plugin.renderer.height,i=this.scaleX,s=this.scaleY;t.x=this.x,t.y=e-this.y,t.scaleX=1,t.scaleY=1,t.updateWorldTransform();var r=this.getBounds();return this.width=r.size.x,this.height=r.size.y,this.displayOriginX=this.x-r.offset.x,this.displayOriginY=this.y-(e-(this.height+r.offset.y)),t.scaleX=i,t.scaleY=s,t.updateWorldTransform(),this},scaleX:{get:function(){return this._scaleX},set:function(t){this._scaleX=t,this.refresh()}},scaleY:{get:function(){return this._scaleY},set:function(t){this._scaleY=t,this.refresh()}},getBoneList:function(){var t=[],e=this.skeletonData;if(e)for(var i=0;i<e.bones.length;i++)t.push(e.bones[i].name);return t},getSkinList:function(){var t=[],e=this.skeletonData;if(e)for(var i=0;i<e.skins.length;i++)t.push(e.skins[i].name);return t},getSlotList:function(){for(var t=[],e=this.skeleton,i=0;i<e.slots.length;i++)t.push(e.slots[i].data.name);return t},getAnimationList:function(){var t=[],e=this.skeletonData;if(e)for(var i=0;i<e.animations.length;i++)t.push(e.animations[i].name);return t},getCurrentAnimation:function(t){void 0===t&&(t=0);var e=this.state.getCurrent(t);if(e)return e.animation},play:function(t,e,i){return this.setAnimation(0,t,e,i),this},setAnimation:function(t,e,i,s){if(void 0===i&&(i=!1),void 0===s&&(s=!1),s&&this.state){var r=this.state.getCurrent(t);if(r&&r.animation.name===e&&!r.isComplete())return}if(this.findAnimation(e))return this.state.setAnimation(t,e,i)},addAnimation:function(t,e,i,s){return void 0===i&&(i=!1),void 0===s&&(s=0),this.state.addAnimation(t,e,i,s)},setEmptyAnimation:function(t,e){return this.state.setEmptyAnimation(t,e)},clearTrack:function(t){return this.state.clearTrack(t),this},clearTracks:function(){return this.state.clearTracks(),this},setSkinByName:function(t){var e=this.skeleton;return e.setSkinByName(t),e.setSlotsToSetupPose(),this.state.apply(e),this},setSkin:function(t){var e=this.skeleton;return e.setSkin(t),e.setSlotsToSetupPose(),this.state.apply(e),this},setMix:function(t,e,i){return this.stateData.setMix(t,e,i),this},getAttachment:function(t,e){return this.skeleton.getAttachment(t,e)},getAttachmentByName:function(t,e){return this.skeleton.getAttachmentByName(t,e)},setAttachment:function(t,e){if(Array.isArray(t)&&Array.isArray(e)&&t.length===e.length)for(var i=0;i<t.length;i++)this.skeleton.setAttachment(t[i],e[i]);else this.skeleton.setAttachment(t,e);return this},setToSetupPose:function(){return this.skeleton.setToSetupPose(),this},setSlotsToSetupPose:function(){return this.skeleton.setSlotsToSetupPose(),this},setBonesToSetupPose:function(){return this.skeleton.setBonesToSetupPose(),this},getRootBone:function(){return this.skeleton.getRootBone()},angleBoneToXY:function(t,e,i,n,a,o){void 0===n&&(n=0),void 0===a&&(a=0),void 0===o&&(o=360);var h=this.plugin.renderer.height,l=d(s(t.worldX,h-t.worldY,e,i)+f(n));return t.rotation=r(g(l),a,o),this},findBone:function(t){return this.skeleton.findBone(t)},findBoneIndex:function(t){return this.skeleton.findBoneIndex(t)},findSlot:function(t){return this.skeleton.findSlot(t)},findSlotIndex:function(t){return this.skeleton.findSlotIndex(t)},findSkin:function(t){return this.skeletonData.findSkin(t)},findEvent:function(t){return this.skeletonData.findEvent(t)},findAnimation:function(t){return this.skeletonData.findAnimation(t)},findIkConstraint:function(t){return this.skeletonData.findIkConstraint(t)},findTransformConstraint:function(t){return this.skeletonData.findTransformConstraint(t)},findPathConstraint:function(t){return this.skeletonData.findPathConstraint(t)},findPathConstraintIndex:function(t){return this.skeletonData.findPathConstraintIndex(t)},getBounds:function(){return this.plugin.getBounds(this.skeleton)},preUpdate:function(t,e){var i=this.skeleton;this.state.update(e/1e3*this.timeScale),this.state.apply(i)},preDestroy:function(){this.state&&(this.state.clearListeners(),this.state.clearListenerNotifications()),this.plugin=null,this.skeleton=null,this.skeletonData=null,this.state=null,this.stateData=null}});t.exports=v},2762:(t,e,i)=>{var s=i(1984),r=i(1984),n=i(1984);s=i(4290),n=i(780),t.exports={renderWebGL:s,renderCanvas:r,renderDirect:n}},780:(t,e,i)=>{var s=i(2915),r=i(3426),n=i(2208),a=i(4208),o=i(8445);t.exports=function(t,e,i,h,l){var c=e.plugin,u=e.skeleton,d=c.sceneRenderer;t.pipelines.clear(),d.begin();var f=e.scrollFactorX,p=e.scrollFactorY,g=u.color.a;l&&(e.scrollFactorX=l.scrollFactorX,e.scrollFactorY=l.scrollFactorY,u.color.a=s(g*l.alpha,0,1)),i.addToRenderList(e);var m=n(e,i,h).calc,x=t.height;if(u.x=m.tx,u.y=x-m.ty,u.scaleX=m.scaleX,u.scaleY=m.scaleY,e.scaleX<0?(u.scaleX*=-1,e.root.rotation=o(a(m.rotationNormalized)-180,0,360)):e.root.rotation=o(a(r(m.rotationNormalized))+90,0,360),e.scaleY<0&&(u.scaleY*=-1,e.scaleX<0?e.root.rotation-=2*a(m.rotationNormalized):e.root.rotation+=2*a(m.rotationNormalized)),u.updateWorldTransform(),d.drawSkeleton(u,e.preMultipliedAlpha),l&&(e.scrollFactorX=f,e.scrollFactorY=p,u.color.a=g),c.drawDebug||e.drawDebug){var v=u.x,y=u.y;u.x=0,u.y=0,d.drawSkeletonDebug(u,e.preMultipliedAlpha),u.x=v,u.y=y}d.end(),t.pipelines.rebind()}},4290:(t,e,i)=>{var s=i(2915),r=i(3426),n=i(2208),a=i(4208),o=i(8445);t.exports=function(t,e,i,h,l){var c=e.plugin,u=e.skeleton,d=c.sceneRenderer;t.newType&&(t.pipelines.clear(),d.begin());var f=e.scrollFactorX,p=e.scrollFactorY,g=u.color.a;l&&(e.scrollFactorX=l.scrollFactorX,e.scrollFactorY=l.scrollFactorY,u.color.a=s(g*l.alpha,0,1)),i.addToRenderList(e);var m=n(e,i,h).calc,x=t.height;if(u.x=m.tx,u.y=x-m.ty,u.scaleX=m.scaleX,u.scaleY=m.scaleY,e.scaleX<0?(u.scaleX*=-1,e.root.rotation=o(a(m.rotationNormalized)-180,0,360)):e.root.rotation=o(a(r(m.rotationNormalized))+90,0,360),e.scaleY<0&&(u.scaleY*=-1,e.scaleX<0?e.root.rotation-=2*a(m.rotationNormalized):e.root.rotation+=2*a(m.rotationNormalized)),u.updateWorldTransform(),d.drawSkeleton(u,e.preMultipliedAlpha),l&&(e.scrollFactorX=f,e.scrollFactorY=p,u.color.a=g),c.drawDebug||e.drawDebug){var v=u.x,y=u.y;u.x=0,u.y=0,d.drawSkeletonDebug(u,e.preMultipliedAlpha),u.x=v,u.y=y}t.nextTypeMatch||(d.end(),t.pipelines.rebind())}},9454:(t,e,i)=>{var s={VERSION:"3.70.0",BlendModes:i(8351),ScaleModes:i(8196),AUTO:0,CANVAS:1,WEBGL:2,HEADLESS:3,FOREVER:-1,NONE:4,UP:5,DOWN:6,LEFT:7,RIGHT:8};t.exports=s},1081:(t,e,i)=>{var s=i(7473),r=i(6748),n=new s({initialize:function(t,e){this.parent=t,this.events=e,e||(this.events=t.events?t.events:t),this.list={},this.values={},this._frozen=!1,!t.hasOwnProperty("sys")&&this.events&&this.events.once(r.DESTROY,this.destroy,this)},get:function(t){var e=this.list;if(Array.isArray(t)){for(var i=[],s=0;s<t.length;s++)i.push(e[t[s]]);return i}return e[t]},getAll:function(){var t={};for(var e in this.list)this.list.hasOwnProperty(e)&&(t[e]=this.list[e]);return t},query:function(t){var e={};for(var i in this.list)this.list.hasOwnProperty(i)&&i.match(t)&&(e[i]=this.list[i]);return e},set:function(t,e){if(this._frozen)return this;if("string"==typeof t)return this.setValue(t,e);for(var i in t)this.setValue(i,t[i]);return this},inc:function(t,e){if(this._frozen)return this;void 0===e&&(e=1);var i=this.get(t);return void 0===i&&(i=0),this.set(t,i+e),this},toggle:function(t){return this._frozen||this.set(t,!this.get(t)),this},setValue:function(t,e){if(this._frozen)return this;if(this.has(t))this.values[t]=e;else{var i=this,s=this.list,n=this.events,a=this.parent;Object.defineProperty(this.values,t,{enumerable:!0,configurable:!0,get:function(){return s[t]},set:function(e){if(!i._frozen){var o=s[t];s[t]=e,n.emit(r.CHANGE_DATA,a,t,e,o),n.emit(r.CHANGE_DATA_KEY+t,a,e,o)}}}),s[t]=e,n.emit(r.SET_DATA,a,t,e)}return this},each:function(t,e){for(var i=[this.parent,null,void 0],s=1;s<arguments.length;s++)i.push(arguments[s]);for(var r in this.list)i[1]=r,i[2]=this.list[r],t.apply(e,i);return this},merge:function(t,e){for(var i in void 0===e&&(e=!0),t)t.hasOwnProperty(i)&&(e||!e&&!this.has(i))&&this.setValue(i,t[i]);return this},remove:function(t){if(this._frozen)return this;if(!Array.isArray(t))return this.removeValue(t);for(var e=0;e<t.length;e++)this.removeValue(t[e]);return this},removeValue:function(t){if(this.has(t)){var e=this.list[t];delete this.list[t],delete this.values[t],this.events.emit(r.REMOVE_DATA,this.parent,t,e)}return this},pop:function(t){var e=void 0;return!this._frozen&&this.has(t)&&(e=this.list[t],delete this.list[t],delete this.values[t],this.events.emit(r.REMOVE_DATA,this.parent,t,e)),e},has:function(t){return this.list.hasOwnProperty(t)},setFreeze:function(t){return this._frozen=t,this},reset:function(){for(var t in this.list)delete this.list[t],delete this.values[t];return this._frozen=!1,this},destroy:function(){this.reset(),this.events.off(r.CHANGE_DATA),this.events.off(r.SET_DATA),this.events.off(r.REMOVE_DATA),this.parent=null},freeze:{get:function(){return this._frozen},set:function(t){this._frozen=!!t}},count:{get:function(){var t=0;for(var e in this.list)void 0!==this.list[e]&&t++;return t}}});t.exports=n},9044:t=>{t.exports="changedata"},7801:t=>{t.exports="changedata-"},4873:t=>{t.exports="destroy"},9966:t=>{t.exports="removedata"},4586:t=>{t.exports="setdata"},6748:(t,e,i)=>{t.exports={CHANGE_DATA:i(9044),CHANGE_DATA_KEY:i(7801),DESTROY:i(4873),REMOVE_DATA:i(9966),SET_DATA:i(4586)}},3004:(t,e,i)=>{var s=i(2776),r={flac:!1,aac:!1,audioData:!1,dolby:!1,m4a:!1,mp3:!1,ogg:!1,opus:!1,wav:!1,webAudio:!1,webm:!1};t.exports=function(){if("function"==typeof importScripts)return r;r.audioData=!!window.Audio,r.webAudio=!(!window.AudioContext&&!window.webkitAudioContext);var t=document.createElement("audio"),e=!!t.canPlayType;try{if(e){var i=function(e,i){var s=t.canPlayType("audio/"+e).replace(/^no$/,"");return i?Boolean(s||t.canPlayType("audio/"+i).replace(/^no$/,"")):Boolean(s)};if(r.ogg=i('ogg; codecs="vorbis"'),r.opus=i('ogg; codecs="opus"',"opus"),r.mp3=i("mpeg"),r.wav=i("wav"),r.m4a=i("x-m4a"),r.aac=i("aac"),r.flac=i("flac","x-flac"),r.webm=i('webm; codecs="vorbis"'),""!==t.canPlayType('audio/mp4; codecs="ec-3"'))if(s.edge)r.dolby=!0;else if(s.safari&&s.safariVersion>=9&&/Mac OS X (\d+)_(\d+)/.test(navigator.userAgent)){var n=parseInt(RegExp.$1,10),a=parseInt(RegExp.$2,10);(10===n&&a>=11||n>10)&&(r.dolby=!0)}}}catch(t){}return r}()},2776:(t,e,i)=>{var s,r=i(5203),n={chrome:!1,chromeVersion:0,edge:!1,firefox:!1,firefoxVersion:0,ie:!1,ieVersion:0,mobileSafari:!1,opera:!1,safari:!1,safariVersion:0,silk:!1,trident:!1,tridentVersion:0,es2019:!1};t.exports=(s=navigator.userAgent,/Edg\/\d+/.test(s)?(n.edge=!0,n.es2019=!0):/OPR/.test(s)?(n.opera=!0,n.es2019=!0):/Chrome\/(\d+)/.test(s)&&!r.windowsPhone?(n.chrome=!0,n.chromeVersion=parseInt(RegExp.$1,10),n.es2019=n.chromeVersion>69):/Firefox\D+(\d+)/.test(s)?(n.firefox=!0,n.firefoxVersion=parseInt(RegExp.$1,10),n.es2019=n.firefoxVersion>10):/AppleWebKit/.test(s)&&r.iOS?(n.mobileSafari=!0,n.es2019=!0):/MSIE (\d+\.\d+);/.test(s)?(n.ie=!0,n.ieVersion=parseInt(RegExp.$1,10)):/Version\/(\d+\.\d+) Safari/.test(s)&&!r.windowsPhone?(n.safari=!0,n.safariVersion=parseInt(RegExp.$1,10),n.es2019=n.safariVersion>10):/Trident\/(\d+\.\d+)(.*)rv:(\d+\.\d+)/.test(s)&&(n.ie=!0,n.trident=!0,n.tridentVersion=parseInt(RegExp.$1,10),n.ieVersion=parseInt(RegExp.$3,10)),/Silk/.test(s)&&(n.silk=!0),n)},6505:(t,e,i)=>{var s,r,n,a=i(8073),o={supportInverseAlpha:!1,supportNewBlendModes:!1};t.exports=("function"!=typeof importScripts&&void 0!==document&&(o.supportNewBlendModes=(s="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAABAQMAAADD8p2OAAAAA1BMVEX/",r="AAAACklEQVQI12NgAAAAAgAB4iG8MwAAAABJRU5ErkJggg==",(n=new Image).onload=function(){var t=new Image;t.onload=function(){var e=a.create2D(t,6).getContext("2d",{willReadFrequently:!0});if(e.globalCompositeOperation="multiply",e.drawImage(n,0,0),e.drawImage(t,2,0),!e.getImageData(2,0,1,1))return!1;var i=e.getImageData(2,0,1,1).data;a.remove(t),o.supportNewBlendModes=255===i[0]&&0===i[1]&&0===i[2]},t.src=s+"/wCKxvRF"+r},n.src=s+"AP804Oa6"+r,!1),o.supportInverseAlpha=function(){var t=a.create2D(this,2).getContext("2d",{willReadFrequently:!0});t.fillStyle="rgba(10, 20, 30, 0.5)",t.fillRect(0,0,1,1);var e=t.getImageData(0,0,1,1);if(null===e)return!1;t.putImageData(e,1,0);var i=t.getImageData(1,0,1,1),s=i.data[0]===e.data[0]&&i.data[1]===e.data[1]&&i.data[2]===e.data[2]&&i.data[3]===e.data[3];return a.remove(this),s}()),o)},6543:(t,e,i)=>{var s=i(5203),r=i(2776),n=i(8073),a={canvas:!1,canvasBitBltShift:null,file:!1,fileSystem:!1,getUserMedia:!0,littleEndian:!1,localStorage:!1,pointerLock:!1,stableSort:!1,support32bit:!1,vibration:!1,webGL:!1,worker:!1};t.exports=function(){if("function"==typeof importScripts)return a;a.canvas=!!window.CanvasRenderingContext2D;try{a.localStorage=!!localStorage.getItem}catch(t){a.localStorage=!1}a.file=!!(window.File&&window.FileReader&&window.FileList&&window.Blob),a.fileSystem=!!window.requestFileSystem;var t,e,i,o=!1;return a.webGL=function(){if(window.WebGLRenderingContext)try{var t=n.createWebGL(this),e=t.getContext("webgl")||t.getContext("experimental-webgl"),i=n.create2D(this),s=i.getContext("2d",{willReadFrequently:!0}).createImageData(1,1);return o=s.data instanceof Uint8ClampedArray,n.remove(t),n.remove(i),!!e}catch(t){return!1}return!1}(),a.worker=!!window.Worker,a.pointerLock="pointerLockElement"in document||"mozPointerLockElement"in document||"webkitPointerLockElement"in document,navigator.getUserMedia=navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia||navigator.oGetUserMedia,window.URL=window.URL||window.webkitURL||window.mozURL||window.msURL,a.getUserMedia=a.getUserMedia&&!!navigator.getUserMedia&&!!window.URL,r.firefox&&r.firefoxVersion<21&&(a.getUserMedia=!1),!s.iOS&&(r.ie||r.firefox||r.chrome)&&(a.canvasBitBltShift=!0),(r.safari||r.mobileSafari)&&(a.canvasBitBltShift=!1),navigator.vibrate=navigator.vibrate||navigator.webkitVibrate||navigator.mozVibrate||navigator.msVibrate,navigator.vibrate&&(a.vibration=!0),"undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint32Array&&(a.littleEndian=(t=new ArrayBuffer(4),e=new Uint8Array(t),i=new Uint32Array(t),e[0]=161,e[1]=178,e[2]=195,e[3]=212,3569595041===i[0]||2712847316!==i[0]&&null)),a.support32bit="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8ClampedArray&&"undefined"!=typeof Int32Array&&null!==a.littleEndian&&o,a}()},3922:t=>{var e={available:!1,cancel:"",keyboard:!1,request:""};t.exports=function(){if("function"==typeof importScripts)return e;var t,i="Fullscreen",s="FullScreen",r=["request"+i,"request"+s,"webkitRequest"+i,"webkitRequest"+s,"msRequest"+i,"msRequest"+s,"mozRequest"+s,"mozRequest"+i];for(t=0;t<r.length;t++)if(document.documentElement[r[t]]){e.available=!0,e.request=r[t];break}var n=["cancel"+s,"exit"+i,"webkitCancel"+s,"webkitExit"+i,"msCancel"+s,"msExit"+i,"mozCancel"+s,"mozExit"+i];if(e.available)for(t=0;t<n.length;t++)if(document[n[t]]){e.cancel=n[t];break}return window.Element&&Element.ALLOW_KEYBOARD_INPUT&&!/ Version\/5\.1(?:\.\d+)? Safari\//.test(navigator.userAgent)&&(e.keyboard=!0),Object.defineProperty(e,"active",{get:function(){return!!(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement)}}),e}()},1454:(t,e,i)=>{var s=i(2776),r={gamepads:!1,mspointer:!1,touch:!1,wheelEvent:null};t.exports=("function"==typeof importScripts||(("ontouchstart"in document.documentElement||navigator.maxTouchPoints&&navigator.maxTouchPoints>=1)&&(r.touch=!0),(navigator.msPointerEnabled||navigator.pointerEnabled)&&(r.mspointer=!0),navigator.getGamepads&&(r.gamepads=!0),"onwheel"in window||s.ie&&"WheelEvent"in window?r.wheelEvent="wheel":"onmousewheel"in window?r.wheelEvent="mousewheel":s.firefox&&"MouseScrollEvent"in window&&(r.wheelEvent="DOMMouseScroll")),r)},5203:t=>{var e={android:!1,chromeOS:!1,cordova:!1,crosswalk:!1,desktop:!1,ejecta:!1,electron:!1,iOS:!1,iOSVersion:0,iPad:!1,iPhone:!1,kindle:!1,linux:!1,macOS:!1,node:!1,nodeWebkit:!1,pixelRatio:1,webApp:!1,windows:!1,windowsPhone:!1};t.exports=function(){if("function"==typeof importScripts)return e;var t=navigator.userAgent;/Windows/.test(t)?e.windows=!0:/Mac OS/.test(t)&&!/like Mac OS/.test(t)?navigator.maxTouchPoints&&navigator.maxTouchPoints>2?(e.iOS=!0,e.iPad=!0,navigator.appVersion.match(/Version\/(\d+)/),e.iOSVersion=parseInt(RegExp.$1,10)):e.macOS=!0:/Android/.test(t)?e.android=!0:/Linux/.test(t)?e.linux=!0:/iP[ao]d|iPhone/i.test(t)?(e.iOS=!0,navigator.appVersion.match(/OS (\d+)/),e.iOSVersion=parseInt(RegExp.$1,10),e.iPhone=-1!==t.toLowerCase().indexOf("iphone"),e.iPad=-1!==t.toLowerCase().indexOf("ipad")):/Kindle/.test(t)||/\bKF[A-Z][A-Z]+/.test(t)||/Silk.*Mobile Safari/.test(t)?e.kindle=!0:/CrOS/.test(t)&&(e.chromeOS=!0),(/Windows Phone/i.test(t)||/IEMobile/i.test(t))&&(e.android=!1,e.iOS=!1,e.macOS=!1,e.windows=!0,e.windowsPhone=!0);var i=/Silk/.test(t);return(e.windows||e.macOS||e.linux&&!i||e.chromeOS)&&(e.desktop=!0),(e.windowsPhone||/Windows NT/i.test(t)&&/Touch/i.test(t))&&(e.desktop=!1),navigator.standalone&&(e.webApp=!0),"function"!=typeof importScripts&&(void 0!==window.cordova&&(e.cordova=!0),void 0!==window.ejecta&&(e.ejecta=!0)),"undefined"!=typeof process&&process.versions&&process.versions.node&&(e.node=!0),e.node&&"object"==typeof process.versions&&(e.nodeWebkit=!!process.versions["node-webkit"],e.electron=!!process.versions.electron),/Crosswalk/.test(t)&&(e.crosswalk=!0),e.pixelRatio=window.devicePixelRatio||1,e}()},2131:(t,e,i)=>{var s=i(4597),r={h264:!1,hls:!1,mp4:!1,m4v:!1,ogg:!1,vp9:!1,webm:!1,hasRequestVideoFrame:!1};t.exports=function(){if("function"==typeof importScripts)return r;var t=document.createElement("video"),e=!!t.canPlayType,i=/^no$/;try{e&&(t.canPlayType('video/ogg; codecs="theora"').replace(i,"")&&(r.ogg=!0),t.canPlayType('video/mp4; codecs="avc1.42E01E"').replace(i,"")&&(r.h264=!0,r.mp4=!0),t.canPlayType("video/x-m4v").replace(i,"")&&(r.m4v=!0),t.canPlayType('video/webm; codecs="vp8, vorbis"').replace(i,"")&&(r.webm=!0),t.canPlayType('video/webm; codecs="vp9"').replace(i,"")&&(r.vp9=!0),t.canPlayType('application/x-mpegURL; codecs="avc1.42E01E"').replace(i,"")&&(r.hls=!0))}catch(t){}return t.parentNode&&t.parentNode.removeChild(t),r.getVideoURL=function(t){Array.isArray(t)||(t=[t]);for(var e=0;e<t.length;e++){var i,n=s(t[e],"url",t[e]);if(0===n.indexOf("blob:"))return{url:n,type:""};if(i=0===n.indexOf("data:")?n.split(",")[0].match(/\/(.*?);/):n.match(/\.([a-zA-Z0-9]+)($|\?)/),i=s(t[e],"type",i?i[1]:"").toLowerCase(),r[i])return{url:n,type:i}}return null},r}()},9356:(t,e,i)=>{t.exports={os:i(5203),browser:i(2776),features:i(6543),input:i(1454),audio:i(3004),video:i(2131),fullscreen:i(3922),canvasFeatures:i(6505)}},5686:(t,e,i)=>{var s=i(7473),r=new Float32Array(20),n=new s({initialize:function(){this._matrix=new Float32Array(20),this.alpha=1,this._dirty=!0,this._data=new Float32Array(20),this.reset()},set:function(t){return this._matrix.set(t),this._dirty=!0,this},reset:function(){var t=this._matrix;return t.fill(0),t[0]=1,t[6]=1,t[12]=1,t[18]=1,this.alpha=1,this._dirty=!0,this},getData:function(){var t=this._data;return this._dirty&&(t.set(this._matrix),t[4]/=255,t[9]/=255,t[14]/=255,t[19]/=255,this._dirty=!1),t},brightness:function(t,e){void 0===t&&(t=0),void 0===e&&(e=!1);var i=t;return this.multiply([i,0,0,0,0,0,i,0,0,0,0,0,i,0,0,0,0,0,1,0],e)},saturate:function(t,e){void 0===t&&(t=0),void 0===e&&(e=!1);var i=2*t/3+1,s=-.5*(i-1);return this.multiply([i,s,s,0,0,s,i,s,0,0,s,s,i,0,0,0,0,0,1,0],e)},desaturate:function(t){return void 0===t&&(t=!1),this.saturate(-1,t)},hue:function(t,e){void 0===t&&(t=0),void 0===e&&(e=!1),t=t/180*Math.PI;var i=Math.cos(t),s=Math.sin(t),r=.213,n=.715,a=.072;return this.multiply([r+.787*i+s*-r,n+i*-n+s*-n,a+i*-a+.928*s,0,0,r+i*-r+.143*s,n+i*(1-n)+.14*s,a+i*-a+-.283*s,0,0,r+i*-r+-.787*s,n+i*-n+s*n,a+.928*i+s*a,0,0,0,0,0,1,0],e)},grayscale:function(t,e){return void 0===t&&(t=1),void 0===e&&(e=!1),this.saturate(-t,e)},blackWhite:function(t){return void 0===t&&(t=!1),this.multiply(n.BLACK_WHITE,t)},contrast:function(t,e){void 0===t&&(t=0),void 0===e&&(e=!1);var i=t+1,s=-.5*(i-1);return this.multiply([i,0,0,0,s,0,i,0,0,s,0,0,i,0,s,0,0,0,1,0],e)},negative:function(t){return void 0===t&&(t=!1),this.multiply(n.NEGATIVE,t)},desaturateLuminance:function(t){return void 0===t&&(t=!1),this.multiply(n.DESATURATE_LUMINANCE,t)},sepia:function(t){return void 0===t&&(t=!1),this.multiply(n.SEPIA,t)},night:function(t,e){return void 0===t&&(t=.1),void 0===e&&(e=!1),this.multiply([-2*t,-t,0,0,0,-t,0,t,0,0,0,t,2*t,0,0,0,0,0,1,0],e)},lsd:function(t){return void 0===t&&(t=!1),this.multiply(n.LSD,t)},brown:function(t){return void 0===t&&(t=!1),this.multiply(n.BROWN,t)},vintagePinhole:function(t){return void 0===t&&(t=!1),this.multiply(n.VINTAGE,t)},kodachrome:function(t){return void 0===t&&(t=!1),this.multiply(n.KODACHROME,t)},technicolor:function(t){return void 0===t&&(t=!1),this.multiply(n.TECHNICOLOR,t)},polaroid:function(t){return void 0===t&&(t=!1),this.multiply(n.POLAROID,t)},shiftToBGR:function(t){return void 0===t&&(t=!1),this.multiply(n.SHIFT_BGR,t)},multiply:function(t,e){void 0===e&&(e=!1),e||this.reset();var i=this._matrix,s=r;return s.set(i),i.set([s[0]*t[0]+s[1]*t[5]+s[2]*t[10]+s[3]*t[15],s[0]*t[1]+s[1]*t[6]+s[2]*t[11]+s[3]*t[16],s[0]*t[2]+s[1]*t[7]+s[2]*t[12]+s[3]*t[17],s[0]*t[3]+s[1]*t[8]+s[2]*t[13]+s[3]*t[18],s[0]*t[4]+s[1]*t[9]+s[2]*t[14]+s[3]*t[19]+s[4],s[5]*t[0]+s[6]*t[5]+s[7]*t[10]+s[8]*t[15],s[5]*t[1]+s[6]*t[6]+s[7]*t[11]+s[8]*t[16],s[5]*t[2]+s[6]*t[7]+s[7]*t[12]+s[8]*t[17],s[5]*t[3]+s[6]*t[8]+s[7]*t[13]+s[8]*t[18],s[5]*t[4]+s[6]*t[9]+s[7]*t[14]+s[8]*t[19]+s[9],s[10]*t[0]+s[11]*t[5]+s[12]*t[10]+s[13]*t[15],s[10]*t[1]+s[11]*t[6]+s[12]*t[11]+s[13]*t[16],s[10]*t[2]+s[11]*t[7]+s[12]*t[12]+s[13]*t[17],s[10]*t[3]+s[11]*t[8]+s[12]*t[13]+s[13]*t[18],s[10]*t[4]+s[11]*t[9]+s[12]*t[14]+s[13]*t[19]+s[14],s[15]*t[0]+s[16]*t[5]+s[17]*t[10]+s[18]*t[15],s[15]*t[1]+s[16]*t[6]+s[17]*t[11]+s[18]*t[16],s[15]*t[2]+s[16]*t[7]+s[17]*t[12]+s[18]*t[17],s[15]*t[3]+s[16]*t[8]+s[17]*t[13]+s[18]*t[18],s[15]*t[4]+s[16]*t[9]+s[17]*t[14]+s[18]*t[19]+s[19]]),this._dirty=!0,this}});n.BLACK_WHITE=[.3,.6,.1,0,0,.3,.6,.1,0,0,.3,.6,.1,0,0,0,0,0,1,0],n.NEGATIVE=[-1,0,0,1,0,0,-1,0,1,0,0,0,-1,1,0,0,0,0,1,0],n.DESATURATE_LUMINANCE=[.2764723,.929708,.0938197,0,-37.1,.2764723,.929708,.0938197,0,-37.1,.2764723,.929708,.0938197,0,-37.1,0,0,0,1,0],n.SEPIA=[.393,.7689999,.18899999,0,0,.349,.6859999,.16799999,0,0,.272,.5339999,.13099999,0,0,0,0,0,1,0],n.LSD=[2,-.4,.5,0,0,-.5,2,-.4,0,0,-.4,-.5,3,0,0,0,0,0,1,0],n.BROWN=[.5997023498159715,.34553243048391263,-.2708298674538042,0,47.43192855600873,-.037703249837783157,.8609577587992641,.15059552388459913,0,-36.96841498319127,.24113635128153335,-.07441037908422492,.44972182064877153,0,-7.562075277591283,0,0,0,1,0],n.VINTAGE=[.6279345635605994,.3202183420819367,-.03965408211312453,0,9.651285835294123,.02578397704808868,.6441188644374771,.03259127616149294,0,7.462829176470591,.0466055556782719,-.0851232987247891,.5241648018700465,0,5.159190588235296,0,0,0,1,0],n.KODACHROME=[1.1285582396593525,-.3967382283601348,-.03992559172921793,0,63.72958762196502,-.16404339962244616,1.0835251566291304,-.05498805115633132,0,24.732407896706203,-.16786010706155763,-.5603416277695248,1.6014850761964943,0,35.62982807460946,0,0,0,1,0],n.TECHNICOLOR=[1.9125277891456083,-.8545344976951645,-.09155508482755585,0,11.793603434377337,-.3087833385928097,1.7658908555458428,-.10601743074722245,0,-70.35205161461398,-.231103377548616,-.7501899197440212,1.847597816108189,0,30.950940869491138,0,0,0,1,0],n.POLAROID=[1.438,-.062,-.062,0,0,-.122,1.378,-.122,0,0,-.016,-.016,1.483,0,0,0,0,0,1,0],n.SHIFT_BGR=[0,0,1,0,0,0,1,0,0,0,1,0,0,0,0,0,0,0,1,0],t.exports=n},8073:(t,e,i)=>{var s,r,n,a=i(9454),o=i(2150),h=[],l=!1;t.exports=(n=function(){var t=0;return h.forEach((function(e){e.parent&&t++})),t},{create2D:function(t,e,i){return s(t,e,i,a.CANVAS)},create:s=function(t,e,i,s,n){var c;void 0===e&&(e=1),void 0===i&&(i=1),void 0===s&&(s=a.CANVAS),void 0===n&&(n=!1);var u=r(s);return null===u?(u={parent:t,canvas:document.createElement("canvas"),type:s},s===a.CANVAS&&h.push(u),c=u.canvas):(u.parent=t,c=u.canvas),n&&(u.parent=c),c.width=e,c.height=i,l&&s===a.CANVAS&&o.disable(c.getContext("2d",{willReadFrequently:!1})),c},createWebGL:function(t,e,i){return s(t,e,i,a.WEBGL)},disableSmoothing:function(){l=!0},enableSmoothing:function(){l=!1},first:r=function(t){if(void 0===t&&(t=a.CANVAS),t===a.WEBGL)return null;for(var e=0;e<h.length;e++){var i=h[e];if(!i.parent&&i.type===t)return i}return null},free:function(){return h.length-n()},pool:h,remove:function(t){var e=t instanceof HTMLCanvasElement;h.forEach((function(i){(e&&i.canvas===t||!e&&i.parent===t)&&(i.parent=null,i.canvas.width=1,i.canvas.height=1)}))},total:n})},2150:t=>{var e,i="";t.exports={disable:function(t){return""===i&&(i=e(t)),i&&(t[i]=!1),t},enable:function(t){return""===i&&(i=e(t)),i&&(t[i]=!0),t},getPrefix:e=function(t){for(var e=["i","webkitI","msI","mozI","oI"],i=0;i<e.length;i++){var s=e[i]+"mageSmoothingEnabled";if(s in t)return s}return null},isEnabled:function(t){return null!==i?t[i]:null}}},7499:(t,e,i)=>{var s=i(7473),r=i(3649),n=new s({initialize:function(t,e,i,s,r,n){e||(e=t.sys.make.image({x:i,y:s,key:r,frame:n,add:!1})),this.bitmapMask=e,this.invertAlpha=!1,this.isStencil=!1},setBitmap:function(t){this.bitmapMask=t},preRenderWebGL:function(t,e,i){t.pipelines.BITMAPMASK_PIPELINE.beginMask(this,e,i)},postRenderWebGL:function(t,e,i){t.pipelines.BITMAPMASK_PIPELINE.endMask(this,e,i)},preRenderCanvas:function(){},postRenderCanvas:function(){},destroy:function(){this.bitmapMask=null}});r.register("bitmapMask",(function(t,e,i,s,r){return new n(this.scene,t,e,i,s,r)})),t.exports=n},6726:(t,e,i)=>{var s=new(i(7473))({initialize:function(t,e){this.geometryMask=e,this.invertAlpha=!1,this.isStencil=!0,this.level=0},setShape:function(t){return this.geometryMask=t,this},setInvertAlpha:function(t){return void 0===t&&(t=!0),this.invertAlpha=t,this},preRenderWebGL:function(t,e,i){var s=t.gl;t.flush(),0===t.maskStack.length&&(s.enable(s.STENCIL_TEST),s.clear(s.STENCIL_BUFFER_BIT),t.maskCount=0),t.currentCameraMask.mask!==this&&(t.currentMask.mask=this),t.maskStack.push({mask:this,camera:i}),this.applyStencil(t,i,!0),t.maskCount++},applyStencil:function(t,e,i){var s=t.gl,r=this.geometryMask,n=t.maskCount,a=255;s.colorMask(!1,!1,!1,!1),i?(s.stencilFunc(s.EQUAL,n,a),s.stencilOp(s.KEEP,s.KEEP,s.INCR),n++):(s.stencilFunc(s.EQUAL,n+1,a),s.stencilOp(s.KEEP,s.KEEP,s.DECR)),this.level=n,r.renderWebGL(t,r,e),t.flush(),s.colorMask(!0,!0,!0,!0),s.stencilOp(s.KEEP,s.KEEP,s.KEEP),this.invertAlpha?s.stencilFunc(s.NOTEQUAL,n,a):s.stencilFunc(s.EQUAL,n,a)},postRenderWebGL:function(t){var e=t.gl;t.maskStack.pop(),t.maskCount--,t.flush();var i=t.currentMask;if(0===t.maskStack.length)i.mask=null,e.disable(e.STENCIL_TEST);else{var s=t.maskStack[t.maskStack.length-1];s.mask.applyStencil(t,s.camera,!1),t.currentCameraMask.mask!==s.mask?(i.mask=s.mask,i.camera=s.camera):i.mask=null}},preRenderCanvas:function(t,e,i){var s=this.geometryMask;t.currentContext.save(),s.renderCanvas(t,s,i,null,null,!0),t.currentContext.clip()},postRenderCanvas:function(t){t.currentContext.restore()},destroy:function(){this.geometryMask=null}});t.exports=s},7340:(t,e,i)=>{var s=i(7473),r=i(6128),n=i(1571),a=new s({Extends:r,initialize:function(t,e){void 0===e&&(e=1),r.call(this,n.BARREL,t),this.amount=e}});t.exports=a},5170:(t,e,i)=>{var s=i(7473),r=i(6128),n=i(1571),a=new s({Extends:r,initialize:function(t,e,i,s,a,o,h){void 0===i&&(i=1),void 0===s&&(s=1),void 0===a&&(a=1),void 0===o&&(o=1),void 0===h&&(h=4),r.call(this,n.BLOOM,t),this.steps=h,this.offsetX=i,this.offsetY=s,this.blurStrength=a,this.strength=o,this.glcolor=[1,1,1],null!=e&&(this.color=e)},color:{get:function(){var t=this.glcolor;return(255*t[0]<<16)+(255*t[1]<<8)+(255*t[2]|0)},set:function(t){var e=this.glcolor;e[0]=(t>>16&255)/255,e[1]=(t>>8&255)/255,e[2]=(255&t)/255}}});t.exports=a},4199:(t,e,i)=>{var s=i(7473),r=i(6128),n=i(1571),a=new s({Extends:r,initialize:function(t,e,i,s,a,o,h){void 0===e&&(e=0),void 0===i&&(i=2),void 0===s&&(s=2),void 0===a&&(a=1),void 0===h&&(h=4),r.call(this,n.BLUR,t),this.quality=e,this.x=i,this.y=s,this.steps=h,this.strength=a,this.glcolor=[1,1,1],null!=o&&(this.color=o)},color:{get:function(){var t=this.glcolor;return(255*t[0]<<16)+(255*t[1]<<8)+(255*t[2]|0)},set:function(t){var e=this.glcolor;e[0]=(t>>16&255)/255,e[1]=(t>>8&255)/255,e[2]=(255&t)/255}}});t.exports=a},3132:(t,e,i)=>{var s=i(7473),r=i(6128),n=i(1571),a=new s({Extends:r,initialize:function(t,e,i,s,a,o,h,l){void 0===e&&(e=.5),void 0===i&&(i=1),void 0===s&&(s=.2),void 0===a&&(a=!1),void 0===o&&(o=1),void 0===h&&(h=1),void 0===l&&(l=1),r.call(this,n.BOKEH,t),this.radius=e,this.amount=i,this.contrast=s,this.isTiltShift=a,this.strength=l,this.blurX=o,this.blurY=h}});t.exports=a},6610:(t,e,i)=>{var s=i(7473),r=i(6128),n=i(1571),a=new s({Extends:r,initialize:function(t,e,i,s,a,o){void 0===e&&(e=8),void 0===a&&(a=1),void 0===o&&(o=.005),r.call(this,n.CIRCLE,t),this.scale=a,this.feather=o,this.thickness=e,this.glcolor=[1,.2,.7],this.glcolor2=[1,0,0,.4],null!=i&&(this.color=i),null!=s&&(this.backgroundColor=s)},color:{get:function(){var t=this.glcolor;return(255*t[0]<<16)+(255*t[1]<<8)+(255*t[2]|0)},set:function(t){var e=this.glcolor;e[0]=(t>>16&255)/255,e[1]=(t>>8&255)/255,e[2]=(255&t)/255}},backgroundColor:{get:function(){var t=this.glcolor2;return(255*t[0]<<16)+(255*t[1]<<8)+(255*t[2]|0)},set:function(t){var e=this.glcolor2;e[0]=(t>>16&255)/255,e[1]=(t>>8&255)/255,e[2]=(255&t)/255}},backgroundAlpha:{get:function(){return this.glcolor2[3]},set:function(t){this.glcolor2[3]=t}}});t.exports=a},4931:(t,e,i)=>{var s=i(7473),r=i(5686),n=i(1571),a=new s({Extends:r,initialize:function(t){r.call(this),this.type=n.COLOR_MATRIX,this.gameObject=t,this.active=!0},destroy:function(){this.gameObject=null,this._matrix=null,this._data=null}});t.exports=a},6128:(t,e,i)=>{var s=new(i(7473))({initialize:function(t,e){this.type=t,this.gameObject=e,this.active=!0},setActive:function(t){return this.active=t,this},destroy:function(){this.gameObject=null,this.active=!1}});t.exports=s},9195:(t,e,i)=>{var s=i(7473),r=i(6128),n=i(1571),a=new s({Extends:r,initialize:function(t,e,i,s){void 0===e&&(e="__WHITE"),void 0===i&&(i=.005),void 0===s&&(s=.005),r.call(this,n.DISPLACEMENT,t),this.x=i,this.y=s,this.glTexture,this.setTexture(e)},setTexture:function(t){var e=this.gameObject.scene.sys.textures.getFrame(t);return e&&(this.glTexture=e.glTexture),this}});t.exports=a},445:(t,e,i)=>{var s=i(7473),r=i(6128),n=i(1571),a=new s({Extends:r,initialize:function(t,e,i,s,a){void 0===i&&(i=4),void 0===s&&(s=0),void 0===a&&(a=!1),r.call(this,n.GLOW,t),this.outerStrength=i,this.innerStrength=s,this.knockout=a,this.glcolor=[1,1,1,1],void 0!==e&&(this.color=e)},color:{get:function(){var t=this.glcolor;return(255*t[0]<<16)+(255*t[1]<<8)+(255*t[2]|0)},set:function(t){var e=this.glcolor;e[0]=(t>>16&255)/255,e[1]=(t>>8&255)/255,e[2]=(255&t)/255}}});t.exports=a},7724:(t,e,i)=>{var s=i(7473),r=i(6128),n=i(1571),a=new s({Extends:r,initialize:function(t,e,i,s,a,o,h,l,c){void 0===s&&(s=.2),void 0===a&&(a=0),void 0===o&&(o=0),void 0===h&&(h=0),void 0===l&&(l=1),void 0===c&&(c=0),r.call(this,n.GRADIENT,t),this.alpha=s,this.size=c,this.fromX=a,this.fromY=o,this.toX=h,this.toY=l,this.glcolor1=[255,0,0],this.glcolor2=[0,255,0],null!=e&&(this.color1=e),null!=i&&(this.color2=i)},color1:{get:function(){var t=this.glcolor1;return(t[0]<<16)+(t[1]<<8)+(0|t[2])},set:function(t){var e=this.glcolor1;e[0]=t>>16&255,e[1]=t>>8&255,e[2]=255&t}},color2:{get:function(){var t=this.glcolor2;return(t[0]<<16)+(t[1]<<8)+(0|t[2])},set:function(t){var e=this.glcolor2;e[0]=t>>16&255,e[1]=t>>8&255,e[2]=255&t}}});t.exports=a},4412:(t,e,i)=>{var s=i(7473),r=i(6128),n=i(1571),a=new s({Extends:r,initialize:function(t,e){void 0===e&&(e=1),r.call(this,n.PIXELATE,t),this.amount=e}});t.exports=a},75:(t,e,i)=>{var s=i(7473),r=i(6128),n=i(1571),a=new s({Extends:r,initialize:function(t,e,i,s,a,o,h,l){void 0===e&&(e=0),void 0===i&&(i=0),void 0===s&&(s=.1),void 0===a&&(a=1),void 0===h&&(h=6),void 0===l&&(l=1),r.call(this,n.SHADOW,t),this.x=e,this.y=i,this.decay=s,this.power=a,this.glcolor=[0,0,0,1],this.samples=h,this.intensity=l,void 0!==o&&(this.color=o)},color:{get:function(){var t=this.glcolor;return(255*t[0]<<16)+(255*t[1]<<8)+(255*t[2]|0)},set:function(t){var e=this.glcolor;e[0]=(t>>16&255)/255,e[1]=(t>>8&255)/255,e[2]=(255&t)/255}}});t.exports=a},8734:(t,e,i)=>{var s=i(7473),r=i(6128),n=i(1571),a=new s({Extends:r,initialize:function(t,e,i,s,a){void 0===e&&(e=.5),void 0===i&&(i=.5),void 0===s&&(s=3),void 0===a&&(a=!1),r.call(this,n.SHINE,t),this.speed=e,this.lineWidth=i,this.gradient=s,this.reveal=a}});t.exports=a},2437:(t,e,i)=>{var s=i(7473),r=i(6128),n=i(1571),a=new s({Extends:r,initialize:function(t,e,i,s,a){void 0===e&&(e=.5),void 0===i&&(i=.5),void 0===s&&(s=.5),void 0===a&&(a=.5),r.call(this,n.VIGNETTE,t),this.x=e,this.y=i,this.radius=s,this.strength=a}});t.exports=a},5984:(t,e,i)=>{var s=i(7473),r=i(6128),n=i(1571),a=new s({Extends:r,initialize:function(t,e,i,s,a){void 0===e&&(e=.1),void 0===i&&(i=0),void 0===s&&(s=0),void 0===a&&(a=!1),r.call(this,n.WIPE,t),this.progress=0,this.wipeWidth=e,this.direction=i,this.axis=s,this.reveal=a}});t.exports=a},1571:t=>{t.exports={GLOW:4,SHADOW:5,PIXELATE:6,VIGNETTE:7,SHINE:8,BLUR:9,GRADIENT:12,BLOOM:13,COLOR_MATRIX:14,CIRCLE:15,BARREL:16,DISPLACEMENT:17,WIPE:18,BOKEH:19}},7347:(t,e,i)=>{var s=i(1030),r=i(1571),n={Barrel:i(7340),Controller:i(6128),Bloom:i(5170),Blur:i(4199),Bokeh:i(3132),Circle:i(6610),ColorMatrix:i(4931),Displacement:i(9195),Glow:i(445),Gradient:i(7724),Pixelate:i(4412),Shadow:i(75),Shine:i(8734),Vignette:i(2437),Wipe:i(5984)};n=s(!1,n,r),t.exports=n},2494:(t,e,i)=>{var s=i(8351),r=i(8361);t.exports=function(t,e,i){e.x=r(i,"x",0),e.y=r(i,"y",0),e.depth=r(i,"depth",0),e.flipX=r(i,"flipX",!1),e.flipY=r(i,"flipY",!1);var n=r(i,"scale",null);"number"==typeof n?e.setScale(n):null!==n&&(e.scaleX=r(n,"x",1),e.scaleY=r(n,"y",1));var a=r(i,"scrollFactor",null);"number"==typeof a?e.setScrollFactor(a):null!==a&&(e.scrollFactorX=r(a,"x",1),e.scrollFactorY=r(a,"y",1)),e.rotation=r(i,"rotation",0);var o=r(i,"angle",null);null!==o&&(e.angle=o),e.alpha=r(i,"alpha",1);var h=r(i,"origin",null);if("number"==typeof h)e.setOrigin(h);else if(null!==h){var l=r(h,"x",.5),c=r(h,"y",.5);e.setOrigin(l,c)}return e.blendMode=r(i,"blendMode",s.NORMAL),e.visible=r(i,"visible",!0),r(i,"add",!0)&&t.sys.displayList.add(e),e.preUpdate&&t.sys.updateList.add(e),e}},2273:(t,e,i)=>{var s=i(7473),r=i(6125),n=i(1081),a=i(4399),o=i(3389),h=i(204),l=new s({Extends:a,initialize:function(t,e){a.call(this),this.scene=t,this.displayList=null,this.type=e,this.state=0,this.parentContainer=null,this.name="",this.active=!0,this.tabIndex=-1,this.data=null,this.renderFlags=15,this.cameraFilter=0,this.input=null,this.body=null,this.ignoreDestroy=!1,this.on(o.ADDED_TO_SCENE,this.addedToScene,this),this.on(o.REMOVED_FROM_SCENE,this.removedFromScene,this),t.sys.queueDepthSort()},setActive:function(t){return this.active=t,this},setName:function(t){return this.name=t,this},setState:function(t){return this.state=t,this},setDataEnabled:function(){return this.data||(this.data=new n(this)),this},setData:function(t,e){return this.data||(this.data=new n(this)),this.data.set(t,e),this},incData:function(t,e){return this.data||(this.data=new n(this)),this.data.inc(t,e),this},toggleData:function(t){return this.data||(this.data=new n(this)),this.data.toggle(t),this},getData:function(t){return this.data||(this.data=new n(this)),this.data.get(t)},setInteractive:function(t,e,i){return this.scene.sys.input.enable(this,t,e,i),this},disableInteractive:function(){return this.scene.sys.input.disable(this),this},removeInteractive:function(){return this.scene.sys.input.clear(this),this.input=void 0,this},addedToScene:function(){},removedFromScene:function(){},update:function(){},toJSON:function(){return r(this)},willRender:function(t){return!(!(!this.displayList||!this.displayList.active||this.displayList.willRender(t))||l.RENDER_MASK!==this.renderFlags||0!==this.cameraFilter&&this.cameraFilter&t.id)},getIndexList:function(){for(var t=this,e=this.parentContainer,i=[];e&&(i.unshift(e.getIndex(t)),t=e,e.parentContainer);)e=e.parentContainer;return this.displayList?i.unshift(this.displayList.getIndex(t)):i.unshift(this.scene.sys.displayList.getIndex(t)),i},addToDisplayList:function(t){return void 0===t&&(t=this.scene.sys.displayList),this.displayList&&this.displayList!==t&&this.removeFromDisplayList(),t.exists(this)||(this.displayList=t,t.add(this,!0),t.queueDepthSort(),this.emit(o.ADDED_TO_SCENE,this,this.scene),t.events.emit(h.ADDED_TO_SCENE,this,this.scene)),this},addToUpdateList:function(){return this.scene&&this.preUpdate&&this.scene.sys.updateList.add(this),this},removeFromDisplayList:function(){var t=this.displayList||this.scene.sys.displayList;return t&&t.exists(this)&&(t.remove(this,!0),t.queueDepthSort(),this.displayList=null,this.emit(o.REMOVED_FROM_SCENE,this,this.scene),t.events.emit(h.REMOVED_FROM_SCENE,this,this.scene)),this},removeFromUpdateList:function(){return this.scene&&this.preUpdate&&this.scene.sys.updateList.remove(this),this},destroy:function(t){this.scene&&!this.ignoreDestroy&&(void 0===t&&(t=!1),this.preDestroy&&this.preDestroy.call(this),this.emit(o.DESTROY,this,t),this.removeAllListeners(),this.postPipelines&&this.resetPostPipeline(!0),this.removeFromDisplayList(),this.removeFromUpdateList(),this.input&&(this.scene.sys.input.clear(this),this.input=void 0),this.data&&(this.data.destroy(),this.data=void 0),this.body&&(this.body.destroy(),this.body=void 0),this.preFX&&(this.preFX.destroy(),this.preFX=void 0),this.postFX&&(this.postFX.destroy(),this.postFX=void 0),this.active=!1,this.visible=!1,this.scene=void 0,this.parentContainer=void 0)}});l.RENDER_MASK=15,t.exports=l},3649:(t,e,i)=>{var s=i(7473),r=i(8456),n=i(204),a=new s({initialize:function(t){this.scene=t,this.systems=t.sys,this.events=t.sys.events,this.displayList,this.updateList,this.events.once(n.BOOT,this.boot,this),this.events.on(n.START,this.start,this)},boot:function(){this.displayList=this.systems.displayList,this.updateList=this.systems.updateList,this.events.once(n.DESTROY,this.destroy,this)},start:function(){this.events.once(n.SHUTDOWN,this.shutdown,this)},existing:function(t){return(t.renderCanvas||t.renderWebGL)&&this.displayList.add(t),t.preUpdate&&this.updateList.add(t),t},shutdown:function(){this.events.off(n.SHUTDOWN,this.shutdown,this)},destroy:function(){this.shutdown(),this.events.off(n.START,this.start,this),this.scene=null,this.systems=null,this.events=null,this.displayList=null,this.updateList=null}});a.register=function(t,e){a.prototype.hasOwnProperty(t)||(a.prototype[t]=e)},a.remove=function(t){a.prototype.hasOwnProperty(t)&&delete a.prototype[t]},r.register("GameObjectFactory",a,"add"),t.exports=a},2208:(t,e,i)=>{var s=i(4227),r=new s,n=new s,a=new s,o={camera:r,sprite:n,calc:a};t.exports=function(t,e,i){var s=r,h=n,l=a;return h.applyITRS(t.x,t.y,t.rotation,t.scaleX,t.scaleY),s.copyFrom(e.matrix),i?(s.multiplyWithOffset(i,-e.scrollX*t.scrollFactorX,-e.scrollY*t.scrollFactorY),h.e=t.x,h.f=t.y):(h.e-=e.scrollX*t.scrollFactorX,h.f-=e.scrollY*t.scrollFactorY),s.multiply(h,l),o}},4344:(t,e,i)=>{var s=i(2915),r={_alpha:1,_alphaTL:1,_alphaTR:1,_alphaBL:1,_alphaBR:1,clearAlpha:function(){return this.setAlpha(1)},setAlpha:function(t,e,i,r){return void 0===t&&(t=1),void 0===e?this.alpha=t:(this._alphaTL=s(t,0,1),this._alphaTR=s(e,0,1),this._alphaBL=s(i,0,1),this._alphaBR=s(r,0,1)),this},alpha:{get:function(){return this._alpha},set:function(t){var e=s(t,0,1);this._alpha=e,this._alphaTL=e,this._alphaTR=e,this._alphaBL=e,this._alphaBR=e,0===e?this.renderFlags&=-3:this.renderFlags|=2}},alphaTopLeft:{get:function(){return this._alphaTL},set:function(t){var e=s(t,0,1);this._alphaTL=e,0!==e&&(this.renderFlags|=2)}},alphaTopRight:{get:function(){return this._alphaTR},set:function(t){var e=s(t,0,1);this._alphaTR=e,0!==e&&(this.renderFlags|=2)}},alphaBottomLeft:{get:function(){return this._alphaBL},set:function(t){var e=s(t,0,1);this._alphaBL=e,0!==e&&(this.renderFlags|=2)}},alphaBottomRight:{get:function(){return this._alphaBR},set:function(t){var e=s(t,0,1);this._alphaBR=e,0!==e&&(this.renderFlags|=2)}}};t.exports=r},4518:(t,e,i)=>{var s=i(2915),r={_alpha:1,clearAlpha:function(){return this.setAlpha(1)},setAlpha:function(t){return void 0===t&&(t=1),this.alpha=t,this},alpha:{get:function(){return this._alpha},set:function(t){var e=s(t,0,1);this._alpha=e,0===e?this.renderFlags&=-3:this.renderFlags|=2}}};t.exports=r},5173:(t,e,i)=>{var s=i(8351),r={_blendMode:s.NORMAL,blendMode:{get:function(){return this._blendMode},set:function(t){"string"==typeof t&&(t=s[t]),(t|=0)>=-1&&(this._blendMode=t)}},setBlendMode:function(t){return this.blendMode=t,this}};t.exports=r},1991:t=>{t.exports={width:0,height:0,displayWidth:{get:function(){return this.scaleX*this.width},set:function(t){this.scaleX=t/this.width}},displayHeight:{get:function(){return this.scaleY*this.height},set:function(t){this.scaleY=t/this.height}},setSize:function(t,e){return this.width=t,this.height=e,this},setDisplaySize:function(t,e){return this.displayWidth=t,this.displayHeight=e,this}}},8305:t=>{var e={texture:null,frame:null,isCropped:!1,setCrop:function(t,e,i,s){if(void 0===t)this.isCropped=!1;else if(this.frame){if("number"==typeof t)this.frame.setCropUVs(this._crop,t,e,i,s,this.flipX,this.flipY);else{var r=t;this.frame.setCropUVs(this._crop,r.x,r.y,r.width,r.height,this.flipX,this.flipY)}this.isCropped=!0}return this},resetCropObject:function(){return{u0:0,v0:0,u1:0,v1:0,width:0,height:0,x:0,y:0,flipX:!1,flipY:!1,cx:0,cy:0,cw:0,ch:0}}};t.exports=e},3131:t=>{var e={_depth:0,depth:{get:function(){return this._depth},set:function(t){this.displayList&&this.displayList.queueDepthSort(),this._depth=t}},setDepth:function(t){return void 0===t&&(t=0),this.depth=t,this}};t.exports=e},1626:(t,e,i)=>{var s=i(7473),r=i(7347),n=i(8935),a=new s({initialize:function(t,e){this.gameObject=t,this.isPost=e,this.enabled=!1,this.list=[],this.padding=0},setPadding:function(t){return void 0===t&&(t=0),this.padding=t,this.gameObject},onFXCopy:function(){},onFX:function(){},enable:function(t){if(!this.isPost){var e=this.gameObject.scene.sys.renderer;e&&e.pipelines?(this.gameObject.pipeline=e.pipelines.FX_PIPELINE,void 0!==t&&(this.padding=t),this.enabled=!0):this.enabled=!1}},clear:function(){if(this.isPost)this.gameObject.resetPostPipeline(!0);else{for(var t=this.list,e=0;e<t.length;e++)t[e].destroy();this.list=[]}return this.enabled=!1,this.gameObject},remove:function(t){var e;if(this.isPost){var i=this.gameObject.getPostPipeline(String(t.type));for(Array.isArray(i)||(i=[i]),e=0;e<i.length;e++){var s=i[e];if(s.controller===t){this.gameObject.removePostPipeline(s),t.destroy();break}}}else{var r=this.list;for(e=0;e<r.length;e++)r[e]===t&&(n(r,e),t.destroy())}return this.gameObject},disable:function(t){return void 0===t&&(t=!1),this.isPost||this.gameObject.resetPipeline(),this.enabled=!1,t&&this.clear(),this.gameObject},add:function(t,e){if(!this.isPost)return this.enabled||this.enable(),this.list.push(t),t;var i=String(t.type);this.gameObject.setPostPipeline(i,e);var s=this.gameObject.getPostPipeline(i);return s?(Array.isArray(s)&&(s=s.pop()),s.controller=t,t):void 0},addGlow:function(t,e,i,s,n,a){return this.add(new r.Glow(this.gameObject,t,e,i,s),{quality:n,distance:a})},addShadow:function(t,e,i,s,n,a,o){return this.add(new r.Shadow(this.gameObject,t,e,i,s,n,a,o))},addPixelate:function(t){return this.add(new r.Pixelate(this.gameObject,t))},addVignette:function(t,e,i,s){return this.add(new r.Vignette(this.gameObject,t,e,i,s))},addShine:function(t,e,i,s){return this.add(new r.Shine(this.gameObject,t,e,i,s))},addBlur:function(t,e,i,s,n,a){return this.add(new r.Blur(this.gameObject,t,e,i,s,n,a))},addGradient:function(t,e,i,s,n,a,o,h){return this.add(new r.Gradient(this.gameObject,t,e,i,s,n,a,o,h))},addBloom:function(t,e,i,s,n,a){return this.add(new r.Bloom(this.gameObject,t,e,i,s,n,a))},addColorMatrix:function(){return this.add(new r.ColorMatrix(this.gameObject))},addCircle:function(t,e,i,s,n){return this.add(new r.Circle(this.gameObject,t,e,i,s,n))},addBarrel:function(t){return this.add(new r.Barrel(this.gameObject,t))},addDisplacement:function(t,e,i){return this.add(new r.Displacement(this.gameObject,t,e,i))},addWipe:function(t,e,i){return this.add(new r.Wipe(this.gameObject,t,e,i))},addReveal:function(t,e,i){return this.add(new r.Wipe(this.gameObject,t,e,i,!0))},addBokeh:function(t,e,i){return this.add(new r.Bokeh(this.gameObject,t,e,i))},addTiltShift:function(t,e,i,s,n,a){return this.add(new r.Bokeh(this.gameObject,t,e,i,!0,s,n,a))},destroy:function(){this.clear(),this.gameObject=null}});t.exports=a},9660:t=>{t.exports={flipX:!1,flipY:!1,toggleFlipX:function(){return this.flipX=!this.flipX,this},toggleFlipY:function(){return this.flipY=!this.flipY,this},setFlipX:function(t){return this.flipX=t,this},setFlipY:function(t){return this.flipY=t,this},setFlip:function(t,e){return this.flipX=t,this.flipY=e,this},resetFlip:function(){return this.flipX=!1,this.flipY=!1,this}}},3671:(t,e,i)=>{var s=i(1392),r=i(9876),n=i(2529),a={prepareBoundsOutput:function(t,e){(void 0===e&&(e=!1),0!==this.rotation&&r(t,this.x,this.y,this.rotation),e&&this.parentContainer)&&this.parentContainer.getBoundsTransformMatrix().transformPoint(t.x,t.y,t);return t},getCenter:function(t,e){return void 0===t&&(t=new n),t.x=this.x-this.displayWidth*this.originX+this.displayWidth/2,t.y=this.y-this.displayHeight*this.originY+this.displayHeight/2,this.prepareBoundsOutput(t,e)},getTopLeft:function(t,e){return t||(t=new n),t.x=this.x-this.displayWidth*this.originX,t.y=this.y-this.displayHeight*this.originY,this.prepareBoundsOutput(t,e)},getTopCenter:function(t,e){return t||(t=new n),t.x=this.x-this.displayWidth*this.originX+this.displayWidth/2,t.y=this.y-this.displayHeight*this.originY,this.prepareBoundsOutput(t,e)},getTopRight:function(t,e){return t||(t=new n),t.x=this.x-this.displayWidth*this.originX+this.displayWidth,t.y=this.y-this.displayHeight*this.originY,this.prepareBoundsOutput(t,e)},getLeftCenter:function(t,e){return t||(t=new n),t.x=this.x-this.displayWidth*this.originX,t.y=this.y-this.displayHeight*this.originY+this.displayHeight/2,this.prepareBoundsOutput(t,e)},getRightCenter:function(t,e){return t||(t=new n),t.x=this.x-this.displayWidth*this.originX+this.displayWidth,t.y=this.y-this.displayHeight*this.originY+this.displayHeight/2,this.prepareBoundsOutput(t,e)},getBottomLeft:function(t,e){return t||(t=new n),t.x=this.x-this.displayWidth*this.originX,t.y=this.y-this.displayHeight*this.originY+this.displayHeight,this.prepareBoundsOutput(t,e)},getBottomCenter:function(t,e){return t||(t=new n),t.x=this.x-this.displayWidth*this.originX+this.displayWidth/2,t.y=this.y-this.displayHeight*this.originY+this.displayHeight,this.prepareBoundsOutput(t,e)},getBottomRight:function(t,e){return t||(t=new n),t.x=this.x-this.displayWidth*this.originX+this.displayWidth,t.y=this.y-this.displayHeight*this.originY+this.displayHeight,this.prepareBoundsOutput(t,e)},getBounds:function(t){var e,i,r,n,a,o,h,l;if(void 0===t&&(t=new s),this.parentContainer){var c=this.parentContainer.getBoundsTransformMatrix();this.getTopLeft(t),c.transformPoint(t.x,t.y,t),e=t.x,i=t.y,this.getTopRight(t),c.transformPoint(t.x,t.y,t),r=t.x,n=t.y,this.getBottomLeft(t),c.transformPoint(t.x,t.y,t),a=t.x,o=t.y,this.getBottomRight(t),c.transformPoint(t.x,t.y,t),h=t.x,l=t.y}else this.getTopLeft(t),e=t.x,i=t.y,this.getTopRight(t),r=t.x,n=t.y,this.getBottomLeft(t),a=t.x,o=t.y,this.getBottomRight(t),h=t.x,l=t.y;return t.x=Math.min(e,r,a,h),t.y=Math.min(i,n,o,l),t.width=Math.max(e,r,a,h)-t.x,t.height=Math.max(i,n,o,l)-t.y,t}};t.exports=a},2246:(t,e,i)=>{var s=i(7499),r=i(6726),n={mask:null,setMask:function(t){return this.mask=t,this},clearMask:function(t){return void 0===t&&(t=!1),t&&this.mask&&this.mask.destroy(),this.mask=null,this},createBitmapMask:function(t,e,i,r,n){return void 0===t&&(this.texture||this.shader||this.geom)&&(t=this),new s(this.scene,t,e,i,r,n)},createGeometryMask:function(t){return void 0!==t||"Graphics"!==this.type&&!this.geom||(t=this),new r(this.scene,t)}};t.exports=n},5085:t=>{var e={_originComponent:!0,originX:.5,originY:.5,_displayOriginX:0,_displayOriginY:0,displayOriginX:{get:function(){return this._displayOriginX},set:function(t){this._displayOriginX=t,this.originX=t/this.width}},displayOriginY:{get:function(){return this._displayOriginY},set:function(t){this._displayOriginY=t,this.originY=t/this.height}},setOrigin:function(t,e){return void 0===t&&(t=.5),void 0===e&&(e=t),this.originX=t,this.originY=e,this.updateDisplayOrigin()},setOriginFromFrame:function(){return this.frame&&this.frame.customPivot?(this.originX=this.frame.pivotX,this.originY=this.frame.pivotY,this.updateDisplayOrigin()):this.setOrigin()},setDisplayOrigin:function(t,e){return void 0===t&&(t=0),void 0===e&&(e=t),this.displayOriginX=t,this.displayOriginY=e,this},updateDisplayOrigin:function(){return this._displayOriginX=this.originX*this.width,this._displayOriginY=this.originY*this.height,this}};t.exports=e},77:(t,e,i)=>{var s=i(7149),r=i(1864),n=i(5851),a=i(3747),o=i(2529),h={path:null,rotateToPath:!1,pathRotationOffset:0,pathOffset:null,pathVector:null,pathDelta:null,pathTween:null,pathConfig:null,_prevDirection:a.PLAYING_FORWARD,setPath:function(t,e){void 0===e&&(e=this.pathConfig);var i=this.pathTween;return i&&i.isPlaying()&&i.stop(),this.path=t,e&&this.startFollow(e),this},setRotateToPath:function(t,e){return void 0===e&&(e=0),this.rotateToPath=t,this.pathRotationOffset=e,this},isFollowing:function(){var t=this.pathTween;return t&&t.isPlaying()},startFollow:function(t,e){void 0===t&&(t={}),void 0===e&&(e=0);var i=this.pathTween;i&&i.isPlaying()&&i.stop(),"number"==typeof t&&(t={duration:t}),t.from=n(t,"from",0),t.to=n(t,"to",1);var h=r(t,"positionOnPath",!1);this.rotateToPath=r(t,"rotateToPath",!1),this.pathRotationOffset=n(t,"rotationOffset",0);var l=n(t,"startAt",e);if(l&&(t.onStart=function(t){var e=t.data[0];e.progress=l,e.elapsed=e.duration*l;var i=e.ease(e.progress);e.current=e.start+(e.end-e.start)*i,e.setTargetValue()}),this.pathOffset||(this.pathOffset=new o(this.x,this.y)),this.pathVector||(this.pathVector=new o),this.pathDelta||(this.pathDelta=new o),this.pathDelta.reset(),t.persist=!0,this.pathTween=this.scene.sys.tweens.addCounter(t),this.path.getStartPoint(this.pathOffset),h&&(this.x=this.pathOffset.x,this.y=this.pathOffset.y),this.pathOffset.x=this.x-this.pathOffset.x,this.pathOffset.y=this.y-this.pathOffset.y,this._prevDirection=a.PLAYING_FORWARD,this.rotateToPath){var c=this.path.getPoint(.1);this.rotation=Math.atan2(c.y-this.y,c.x-this.x)+s(this.pathRotationOffset)}return this.pathConfig=t,this},pauseFollow:function(){var t=this.pathTween;return t&&t.isPlaying()&&t.pause(),this},resumeFollow:function(){var t=this.pathTween;return t&&t.isPaused()&&t.resume(),this},stopFollow:function(){var t=this.pathTween;return t&&t.isPlaying()&&t.stop(),this},pathUpdate:function(){var t=this.pathTween;if(t&&t.data){var e=t.data[0],i=this.pathDelta,r=this.pathVector;if(i.copy(r).negate(),e.state===a.COMPLETE)return this.path.getPoint(e.end,r),i.add(r),r.add(this.pathOffset),void this.setPosition(r.x,r.y);if(e.state!==a.PLAYING_FORWARD&&e.state!==a.PLAYING_BACKWARD)return;this.path.getPoint(t.getValue(),r),i.add(r),r.add(this.pathOffset);var n=this.x,o=this.y;this.setPosition(r.x,r.y);var h=this.x-n,l=this.y-o;if(0===h&&0===l)return;if(e.state!==this._prevDirection)return void(this._prevDirection=e.state);this.rotateToPath&&(this.rotation=Math.atan2(l,h)+s(this.pathRotationOffset))}}};t.exports=h},986:(t,e,i)=>{var s=i(3911),r={defaultPipeline:null,pipeline:null,pipelineData:null,initPipeline:function(t){this.pipelineData={};var e=this.scene.sys.renderer;if(!e)return!1;var i=e.pipelines;if(i){void 0===t&&(t=i.default);var s=i.get(t);if(s)return this.defaultPipeline=s,this.pipeline=s,!0}return!1},setPipeline:function(t,e,i){var r=this.scene.sys.renderer;if(!r)return this;var n=r.pipelines;if(n){var a=n.get(t);a&&(this.pipeline=a),e&&(this.pipelineData=i?s(e):e)}return this},setPipelineData:function(t,e){var i=this.pipelineData;return void 0===e?delete i[t]:i[t]=e,this},resetPipeline:function(t){return void 0===t&&(t=!1),this.pipeline=this.defaultPipeline,t&&(this.pipelineData={}),null!==this.pipeline},getPipelineName:function(){return this.pipeline.name}};t.exports=r},4461:(t,e,i)=>{var s=i(3911),r=i(1626),n=i(8935),a={hasPostPipeline:!1,postPipelines:null,postPipelineData:null,preFX:null,postFX:null,initPostPipeline:function(t){this.postPipelines=[],this.postPipelineData={},this.postFX=new r(this,!0),t&&(this.preFX=new r(this,!1))},setPostPipeline:function(t,e,i){var r=this.scene.sys.renderer;if(!r)return this;var n=r.pipelines;if(n){Array.isArray(t)||(t=[t]);for(var a=0;a<t.length;a++){var o=n.getPostPipeline(t[a],this,e);o&&this.postPipelines.push(o)}e&&(this.postPipelineData=i?s(e):e)}return this.hasPostPipeline=this.postPipelines.length>0,this},setPostPipelineData:function(t,e){var i=this.postPipelineData;return void 0===e?delete i[t]:i[t]=e,this},getPostPipeline:function(t){for(var e="string"==typeof t,i=this.postPipelines,s=[],r=0;r<i.length;r++){var n=i[r];(e&&n.name===t||!e&&n instanceof t)&&s.push(n)}return 1===s.length?s[0]:s},resetPostPipeline:function(t){void 0===t&&(t=!1);for(var e=this.postPipelines,i=0;i<e.length;i++)e[i].destroy();this.postPipelines=[],this.hasPostPipeline=!1,t&&(this.postPipelineData={})},removePostPipeline:function(t){for(var e="string"==typeof t,i=this.postPipelines,s=i.length-1;s>=0;s--){var r=i[s];(e&&r.name===t||!e&&r===t)&&(r.destroy(),n(i,s))}return this.hasPostPipeline=this.postPipelines.length>0,this},clearFX:function(){return this.preFX&&this.preFX.clear(),this.postFX&&this.postFX.clear(),this}};t.exports=a},4627:t=>{var e={scrollFactorX:1,scrollFactorY:1,setScrollFactor:function(t,e){return void 0===e&&(e=t),this.scrollFactorX=t,this.scrollFactorY=e,this}};t.exports=e},1868:t=>{var e={_sizeComponent:!0,width:0,height:0,displayWidth:{get:function(){return Math.abs(this.scaleX*this.frame.realWidth)},set:function(t){this.scaleX=t/this.frame.realWidth}},displayHeight:{get:function(){return Math.abs(this.scaleY*this.frame.realHeight)},set:function(t){this.scaleY=t/this.frame.realHeight}},setSizeToFrame:function(t){t||(t=this.frame),this.width=t.realWidth,this.height=t.realHeight;var e=this.input;return e&&!e.customHitArea&&(e.hitArea.width=this.width,e.hitArea.height=this.height),this},setSize:function(t,e){return this.width=t,this.height=e,this},setDisplaySize:function(t,e){return this.displayWidth=t,this.displayHeight=e,this}};t.exports=e},4976:(t,e,i)=>{var s=i(2362),r={texture:null,frame:null,isCropped:!1,setTexture:function(t,e,i,s){return this.texture=this.scene.sys.textures.get(t),this.setFrame(e,i,s)},setFrame:function(t,e,i){return void 0===e&&(e=!0),void 0===i&&(i=!0),t instanceof s?(this.texture=this.scene.sys.textures.get(t.texture.key),this.frame=t):this.frame=this.texture.get(t),this.frame.cutWidth&&this.frame.cutHeight?this.renderFlags|=8:this.renderFlags&=-9,this._sizeComponent&&e&&this.setSizeToFrame(),this._originComponent&&i&&(this.frame.customPivot?this.setOrigin(this.frame.pivotX,this.frame.pivotY):this.updateDisplayOrigin()),this}};t.exports=r},9243:(t,e,i)=>{var s=i(2362),r={texture:null,frame:null,isCropped:!1,setCrop:function(t,e,i,s){if(void 0===t)this.isCropped=!1;else if(this.frame){if("number"==typeof t)this.frame.setCropUVs(this._crop,t,e,i,s,this.flipX,this.flipY);else{var r=t;this.frame.setCropUVs(this._crop,r.x,r.y,r.width,r.height,this.flipX,this.flipY)}this.isCropped=!0}return this},setTexture:function(t,e){return this.texture=this.scene.sys.textures.get(t),this.setFrame(e)},setFrame:function(t,e,i){return void 0===e&&(e=!0),void 0===i&&(i=!0),t instanceof s?(this.texture=this.scene.sys.textures.get(t.texture.key),this.frame=t):this.frame=this.texture.get(t),this.frame.cutWidth&&this.frame.cutHeight?this.renderFlags|=8:this.renderFlags&=-9,this._sizeComponent&&e&&this.setSizeToFrame(),this._originComponent&&i&&(this.frame.customPivot?this.setOrigin(this.frame.pivotX,this.frame.pivotY):this.updateDisplayOrigin()),this.isCropped&&this.frame.updateCropUVs(this._crop,this.flipX,this.flipY),this},resetCropObject:function(){return{u0:0,v0:0,u1:0,v1:0,width:0,height:0,x:0,y:0,flipX:!1,flipY:!1,cx:0,cy:0,cw:0,ch:0}}};t.exports=r},5693:t=>{var e={tintTopLeft:16777215,tintTopRight:16777215,tintBottomLeft:16777215,tintBottomRight:16777215,tintFill:!1,clearTint:function(){return this.setTint(16777215),this},setTint:function(t,e,i,s){return void 0===t&&(t=16777215),void 0===e&&(e=t,i=t,s=t),this.tintTopLeft=t,this.tintTopRight=e,this.tintBottomLeft=i,this.tintBottomRight=s,this.tintFill=!1,this},setTintFill:function(t,e,i,s){return this.setTint(t,e,i,s),this.tintFill=!0,this},tint:{get:function(){return this.tintTopLeft},set:function(t){this.setTint(t,t,t,t)}},isTinted:{get:function(){var t=16777215;return this.tintFill||this.tintTopLeft!==t||this.tintTopRight!==t||this.tintBottomLeft!==t||this.tintBottomRight!==t}}};t.exports=e},6125:t=>{t.exports=function(t){var e={name:t.name,type:t.type,x:t.x,y:t.y,depth:t.depth,scale:{x:t.scaleX,y:t.scaleY},origin:{x:t.originX,y:t.originY},flipX:t.flipX,flipY:t.flipY,rotation:t.rotation,alpha:t.alpha,visible:t.visible,blendMode:t.blendMode,textureKey:"",frameKey:"",data:{}};return t.texture&&(e.textureKey=t.texture.key,e.frameKey=t.frame.name),e}},3212:(t,e,i)=>{var s=i(7425),r=i(4227),n=i(7556),a=i(3692),o=i(2820),h=i(2529),l={hasTransformComponent:!0,_scaleX:1,_scaleY:1,_rotation:0,x:0,y:0,z:0,w:0,scale:{get:function(){return(this._scaleX+this._scaleY)/2},set:function(t){this._scaleX=t,this._scaleY=t,0===t?this.renderFlags&=-5:this.renderFlags|=4}},scaleX:{get:function(){return this._scaleX},set:function(t){this._scaleX=t,0===t?this.renderFlags&=-5:0!==this._scaleY&&(this.renderFlags|=4)}},scaleY:{get:function(){return this._scaleY},set:function(t){this._scaleY=t,0===t?this.renderFlags&=-5:0!==this._scaleX&&(this.renderFlags|=4)}},angle:{get:function(){return o(this._rotation*s.RAD_TO_DEG)},set:function(t){this.rotation=o(t)*s.DEG_TO_RAD}},rotation:{get:function(){return this._rotation},set:function(t){this._rotation=a(t)}},setPosition:function(t,e,i,s){return void 0===t&&(t=0),void 0===e&&(e=t),void 0===i&&(i=0),void 0===s&&(s=0),this.x=t,this.y=e,this.z=i,this.w=s,this},copyPosition:function(t){return void 0!==t.x&&(this.x=t.x),void 0!==t.y&&(this.y=t.y),void 0!==t.z&&(this.z=t.z),void 0!==t.w&&(this.w=t.w),this},setRandomPosition:function(t,e,i,s){return void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=this.scene.sys.scale.width),void 0===s&&(s=this.scene.sys.scale.height),this.x=t+Math.random()*i,this.y=e+Math.random()*s,this},setRotation:function(t){return void 0===t&&(t=0),this.rotation=t,this},setAngle:function(t){return void 0===t&&(t=0),this.angle=t,this},setScale:function(t,e){return void 0===t&&(t=1),void 0===e&&(e=t),this.scaleX=t,this.scaleY=e,this},setX:function(t){return void 0===t&&(t=0),this.x=t,this},setY:function(t){return void 0===t&&(t=0),this.y=t,this},setZ:function(t){return void 0===t&&(t=0),this.z=t,this},setW:function(t){return void 0===t&&(t=0),this.w=t,this},getLocalTransformMatrix:function(t){return void 0===t&&(t=new r),t.applyITRS(this.x,this.y,this._rotation,this._scaleX,this._scaleY)},getWorldTransformMatrix:function(t,e){void 0===t&&(t=new r);var i=this.parentContainer;if(!i)return this.getLocalTransformMatrix(t);for(e||(e=new r),t.applyITRS(this.x,this.y,this._rotation,this._scaleX,this._scaleY);i;)e.applyITRS(i.x,i.y,i._rotation,i._scaleX,i._scaleY),e.multiply(t,t),i=i.parentContainer;return t},getLocalPoint:function(t,e,i,s){i||(i=new h),s||(s=this.scene.sys.cameras.main);var r=s.scrollX,a=s.scrollY,o=t+r*this.scrollFactorX-r,l=e+a*this.scrollFactorY-a;return this.parentContainer?this.getWorldTransformMatrix().applyInverse(o,l,i):n(o,l,this.x,this.y,this.rotation,this.scaleX,this.scaleY,i),this._originComponent&&(i.x+=this._displayOriginX,i.y+=this._displayOriginY),i},getParentRotation:function(){for(var t=0,e=this.parentContainer;e;)t+=e.rotation,e=e.parentContainer;return t}};t.exports=l},4227:(t,e,i)=>{var s=i(7473),r=i(7425),n=i(2529),a=new s({initialize:function(t,e,i,s,r,n){void 0===t&&(t=1),void 0===e&&(e=0),void 0===i&&(i=0),void 0===s&&(s=1),void 0===r&&(r=0),void 0===n&&(n=0),this.matrix=new Float32Array([t,e,i,s,r,n,0,0,1]),this.decomposedMatrix={translateX:0,translateY:0,scaleX:1,scaleY:1,rotation:0},this.quad=new Float32Array(8)},a:{get:function(){return this.matrix[0]},set:function(t){this.matrix[0]=t}},b:{get:function(){return this.matrix[1]},set:function(t){this.matrix[1]=t}},c:{get:function(){return this.matrix[2]},set:function(t){this.matrix[2]=t}},d:{get:function(){return this.matrix[3]},set:function(t){this.matrix[3]=t}},e:{get:function(){return this.matrix[4]},set:function(t){this.matrix[4]=t}},f:{get:function(){return this.matrix[5]},set:function(t){this.matrix[5]=t}},tx:{get:function(){return this.matrix[4]},set:function(t){this.matrix[4]=t}},ty:{get:function(){return this.matrix[5]},set:function(t){this.matrix[5]=t}},rotation:{get:function(){return Math.acos(this.a/this.scaleX)*(Math.atan(-this.c/this.a)<0?-1:1)}},rotationNormalized:{get:function(){var t=this.matrix,e=t[0],i=t[1],s=t[2],n=t[3];return e||i?i>0?Math.acos(e/this.scaleX):-Math.acos(e/this.scaleX):s||n?r.TAU-(n>0?Math.acos(-s/this.scaleY):-Math.acos(s/this.scaleY)):0}},scaleX:{get:function(){return Math.sqrt(this.a*this.a+this.b*this.b)}},scaleY:{get:function(){return Math.sqrt(this.c*this.c+this.d*this.d)}},loadIdentity:function(){var t=this.matrix;return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,this},translate:function(t,e){var i=this.matrix;return i[4]=i[0]*t+i[2]*e+i[4],i[5]=i[1]*t+i[3]*e+i[5],this},scale:function(t,e){var i=this.matrix;return i[0]*=t,i[1]*=t,i[2]*=e,i[3]*=e,this},rotate:function(t){var e=Math.sin(t),i=Math.cos(t),s=this.matrix,r=s[0],n=s[1],a=s[2],o=s[3];return s[0]=r*i+a*e,s[1]=n*i+o*e,s[2]=r*-e+a*i,s[3]=n*-e+o*i,this},multiply:function(t,e){var i=this.matrix,s=t.matrix,r=i[0],n=i[1],a=i[2],o=i[3],h=i[4],l=i[5],c=s[0],u=s[1],d=s[2],f=s[3],p=s[4],g=s[5],m=void 0===e?i:e.matrix;return m[0]=c*r+u*a,m[1]=c*n+u*o,m[2]=d*r+f*a,m[3]=d*n+f*o,m[4]=p*r+g*a+h,m[5]=p*n+g*o+l,m},multiplyWithOffset:function(t,e,i){var s=this.matrix,r=t.matrix,n=s[0],a=s[1],o=s[2],h=s[3],l=e*n+i*o+s[4],c=e*a+i*h+s[5],u=r[0],d=r[1],f=r[2],p=r[3],g=r[4],m=r[5];return s[0]=u*n+d*o,s[1]=u*a+d*h,s[2]=f*n+p*o,s[3]=f*a+p*h,s[4]=g*n+m*o+l,s[5]=g*a+m*h+c,this},transform:function(t,e,i,s,r,n){var a=this.matrix,o=a[0],h=a[1],l=a[2],c=a[3],u=a[4],d=a[5];return a[0]=t*o+e*l,a[1]=t*h+e*c,a[2]=i*o+s*l,a[3]=i*h+s*c,a[4]=r*o+n*l+u,a[5]=r*h+n*c+d,this},transformPoint:function(t,e,i){void 0===i&&(i={x:0,y:0});var s=this.matrix,r=s[0],n=s[1],a=s[2],o=s[3],h=s[4],l=s[5];return i.x=t*r+e*a+h,i.y=t*n+e*o+l,i},invert:function(){var t=this.matrix,e=t[0],i=t[1],s=t[2],r=t[3],n=t[4],a=t[5],o=e*r-i*s;return t[0]=r/o,t[1]=-i/o,t[2]=-s/o,t[3]=e/o,t[4]=(s*a-r*n)/o,t[5]=-(e*a-i*n)/o,this},copyFrom:function(t){var e=this.matrix;return e[0]=t.a,e[1]=t.b,e[2]=t.c,e[3]=t.d,e[4]=t.e,e[5]=t.f,this},copyFromArray:function(t){var e=this.matrix;return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],this},copyToContext:function(t){var e=this.matrix;return t.transform(e[0],e[1],e[2],e[3],e[4],e[5]),t},setToContext:function(t){return t.setTransform(this),t},copyToArray:function(t){var e=this.matrix;return void 0===t?t=[e[0],e[1],e[2],e[3],e[4],e[5]]:(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5]),t},setTransform:function(t,e,i,s,r,n){var a=this.matrix;return a[0]=t,a[1]=e,a[2]=i,a[3]=s,a[4]=r,a[5]=n,this},decomposeMatrix:function(){var t=this.decomposedMatrix,e=this.matrix,i=e[0],s=e[1],r=e[2],n=e[3],a=i*n-s*r;if(t.translateX=e[4],t.translateY=e[5],i||s){var o=Math.sqrt(i*i+s*s);t.rotation=s>0?Math.acos(i/o):-Math.acos(i/o),t.scaleX=o,t.scaleY=a/o}else if(r||n){var h=Math.sqrt(r*r+n*n);t.rotation=.5*Math.PI-(n>0?Math.acos(-r/h):-Math.acos(r/h)),t.scaleX=a/h,t.scaleY=h}else t.rotation=0,t.scaleX=0,t.scaleY=0;return t},applyITRS:function(t,e,i,s,r){var n=this.matrix,a=Math.sin(i),o=Math.cos(i);return n[4]=t,n[5]=e,n[0]=o*s,n[1]=a*s,n[2]=-a*r,n[3]=o*r,this},applyInverse:function(t,e,i){void 0===i&&(i=new n);var s=this.matrix,r=s[0],a=s[1],o=s[2],h=s[3],l=s[4],c=s[5],u=1/(r*h+o*-a);return i.x=h*u*t+-o*u*e+(c*o-l*h)*u,i.y=r*u*e+-a*u*t+(-c*r+l*a)*u,i},setQuad:function(t,e,i,s,r,n){void 0===r&&(r=!1),void 0===n&&(n=this.quad);var a=this.matrix,o=a[0],h=a[1],l=a[2],c=a[3],u=a[4],d=a[5];return r?(n[0]=Math.round(t*o+e*l+u),n[1]=Math.round(t*h+e*c+d),n[2]=Math.round(t*o+s*l+u),n[3]=Math.round(t*h+s*c+d),n[4]=Math.round(i*o+s*l+u),n[5]=Math.round(i*h+s*c+d),n[6]=Math.round(i*o+e*l+u),n[7]=Math.round(i*h+e*c+d)):(n[0]=t*o+e*l+u,n[1]=t*h+e*c+d,n[2]=t*o+s*l+u,n[3]=t*h+s*c+d,n[4]=i*o+s*l+u,n[5]=i*h+s*c+d,n[6]=i*o+e*l+u,n[7]=i*h+e*c+d),n},getX:function(t,e){return t*this.a+e*this.c+this.e},getY:function(t,e){return t*this.b+e*this.d+this.f},getXRound:function(t,e,i){var s=this.getX(t,e);return i&&(s=Math.round(s)),s},getYRound:function(t,e,i){var s=this.getY(t,e);return i&&(s=Math.round(s)),s},getCSSMatrix:function(){var t=this.matrix;return"matrix("+t[0]+","+t[1]+","+t[2]+","+t[3]+","+t[4]+","+t[5]+")"},destroy:function(){this.matrix=null,this.quad=null,this.decomposedMatrix=null}});t.exports=a},8414:t=>{var e={_visible:!0,visible:{get:function(){return this._visible},set:function(t){t?(this._visible=!0,this.renderFlags|=1):(this._visible=!1,this.renderFlags&=-2)}},setVisible:function(t){return this.visible=t,this}};t.exports=e},4286:(t,e,i)=>{t.exports={Alpha:i(4344),AlphaSingle:i(4518),BlendMode:i(5173),ComputedSize:i(1991),Crop:i(8305),Depth:i(3131),Flip:i(9660),FX:i(1626),GetBounds:i(3671),Mask:i(2246),Origin:i(5085),PathFollower:i(77),Pipeline:i(986),PostPipeline:i(4461),ScrollFactor:i(4627),Size:i(1868),Texture:i(4976),TextureCrop:i(9243),Tint:i(5693),ToJSON:i(6125),Transform:i(3212),TransformMatrix:i(4227),Visible:i(8414)}},7361:(t,e,i)=>{var s=i(1953),r=i(8351),n=i(7473),a=i(4286),o=i(3389),h=i(2273),l=i(1392),c=i(3232),u=i(9422),d=i(2529),f=new n({Extends:h,Mixins:[a.AlphaSingle,a.BlendMode,a.ComputedSize,a.Depth,a.Mask,a.PostPipeline,a.Transform,a.Visible,c],initialize:function(t,e,i,s){h.call(this,t,"Container"),this.list=[],this.exclusive=!0,this.maxSize=-1,this.position=0,this.localTransform=new a.TransformMatrix,this.tempTransformMatrix=new a.TransformMatrix,this._sortKey="",this._sysEvents=t.sys.events,this.scrollFactorX=1,this.scrollFactorY=1,this.initPostPipeline(),this.setPosition(e,i),this.setBlendMode(r.SKIP_CHECK),s&&this.add(s)},originX:{get:function(){return.5}},originY:{get:function(){return.5}},displayOriginX:{get:function(){return.5*this.width}},displayOriginY:{get:function(){return.5*this.height}},setExclusive:function(t){return void 0===t&&(t=!0),this.exclusive=t,this},getBounds:function(t){if(void 0===t&&(t=new l),t.setTo(this.x,this.y,0,0),this.parentContainer){var e=this.parentContainer.getBoundsTransformMatrix().transformPoint(this.x,this.y);t.setTo(e.x,e.y,0,0)}if(this.list.length>0){var i=this.list,s=new l,r=!1;t.setEmpty();for(var n=0;n<i.length;n++){var a=i[n];a.getBounds&&(a.getBounds(s),r?u(s,t,t):(t.setTo(s.x,s.y,s.width,s.height),r=!0))}}return t},addHandler:function(t){t.once(o.DESTROY,this.remove,this),this.exclusive&&(t.parentContainer&&t.parentContainer.remove(t),t.parentContainer=this,t.removeFromDisplayList(),t.addedToScene())},removeHandler:function(t){t.off(o.DESTROY,this.remove,this),this.exclusive&&(t.parentContainer=null,t.removedFromScene(),t.addToDisplayList())},pointToContainer:function(t,e){void 0===e&&(e=new d),this.parentContainer?this.parentContainer.pointToContainer(t,e):(e.x=t.x,e.y=t.y);var i=this.tempTransformMatrix;return i.applyITRS(this.x,this.y,this.rotation,this.scaleX,this.scaleY),i.invert(),i.transformPoint(t.x,t.y,e),e},getBoundsTransformMatrix:function(){return this.getWorldTransformMatrix(this.tempTransformMatrix,this.localTransform)},add:function(t){return s.Add(this.list,t,this.maxSize,this.addHandler,this),this},addAt:function(t,e){return s.AddAt(this.list,t,e,this.maxSize,this.addHandler,this),this},getAt:function(t){return this.list[t]},getIndex:function(t){return this.list.indexOf(t)},sort:function(t,e){return t?(void 0===e&&(e=function(e,i){return e[t]-i[t]}),s.StableSort(this.list,e),this):this},getByName:function(t){return s.GetFirst(this.list,"name",t)},getRandom:function(t,e){return s.GetRandom(this.list,t,e)},getFirst:function(t,e,i,r){return s.GetFirst(this.list,t,e,i,r)},getAll:function(t,e,i,r){return s.GetAll(this.list,t,e,i,r)},count:function(t,e,i,r){return s.CountAllMatching(this.list,t,e,i,r)},swap:function(t,e){return s.Swap(this.list,t,e),this},moveTo:function(t,e){return s.MoveTo(this.list,t,e),this},moveAbove:function(t,e){return s.MoveAbove(this.list,t,e),this},moveBelow:function(t,e){return s.MoveBelow(this.list,t,e),this},remove:function(t,e){var i=s.Remove(this.list,t,this.removeHandler,this);if(e&&i){Array.isArray(i)||(i=[i]);for(var r=0;r<i.length;r++)i[r].destroy()}return this},removeAt:function(t,e){var i=s.RemoveAt(this.list,t,this.removeHandler,this);return e&&i&&i.destroy(),this},removeBetween:function(t,e,i){var r=s.RemoveBetween(this.list,t,e,this.removeHandler,this);if(i)for(var n=0;n<r.length;n++)r[n].destroy();return this},removeAll:function(t){var e=this.list;if(t){for(var i=0;i<e.length;i++)e[i]&&e[i].scene&&(e[i].off(o.DESTROY,this.remove,this),e[i].destroy());this.list=[]}else s.RemoveBetween(e,0,e.length,this.removeHandler,this);return this},bringToTop:function(t){return s.BringToTop(this.list,t),this},sendToBack:function(t){return s.SendToBack(this.list,t),this},moveUp:function(t){return s.MoveUp(this.list,t),this},moveDown:function(t){return s.MoveDown(this.list,t),this},reverse:function(){return this.list.reverse(),this},shuffle:function(){return s.Shuffle(this.list),this},replace:function(t,e,i){return s.Replace(this.list,t,e)&&(this.addHandler(e),this.removeHandler(t),i&&t.destroy()),this},exists:function(t){return this.list.indexOf(t)>-1},setAll:function(t,e,i,r){return s.SetAll(this.list,t,e,i,r),this},each:function(t,e){var i,s=[null],r=this.list.slice(),n=r.length;for(i=2;i<arguments.length;i++)s.push(arguments[i]);for(i=0;i<n;i++)s[0]=r[i],t.apply(e,s);return this},iterate:function(t,e){var i,s=[null];for(i=2;i<arguments.length;i++)s.push(arguments[i]);for(i=0;i<this.list.length;i++)s[0]=this.list[i],t.apply(e,s);return this},setScrollFactor:function(t,e,i){return void 0===e&&(e=t),void 0===i&&(i=!1),this.scrollFactorX=t,this.scrollFactorY=e,i&&(s.SetAll(this.list,"scrollFactorX",t),s.SetAll(this.list,"scrollFactorY",e)),this},length:{get:function(){return this.list.length}},first:{get:function(){return this.position=0,this.list.length>0?this.list[0]:null}},last:{get:function(){return this.list.length>0?(this.position=this.list.length-1,this.list[this.position]):null}},next:{get:function(){return this.position<this.list.length?(this.position++,this.list[this.position]):null}},previous:{get:function(){return this.position>0?(this.position--,this.list[this.position]):null}},preDestroy:function(){this.removeAll(!!this.exclusive),this.localTransform.destroy(),this.tempTransformMatrix.destroy(),this.list=[]}});t.exports=f},3232:(t,e,i)=>{var s=i(1984),r=s,n=s;r=i(4343),t.exports={renderWebGL:r,renderCanvas:n}},4343:t=>{t.exports=function(t,e,i,s){i.addToRenderList(e);var r=e.list,n=r.length;if(0!==n){var a=e.localTransform;s?(a.loadIdentity(),a.multiply(s),a.translate(e.x,e.y),a.rotate(e.rotation),a.scale(e.scaleX,e.scaleY)):a.applyITRS(e.x,e.y,e.rotation,e.scaleX,e.scaleY),t.pipelines.preBatch(e);var o=-1!==e.blendMode;o||t.setBlendMode(0);for(var h=e.alpha,l=e.scrollFactorX,c=e.scrollFactorY,u=0;u<n;u++){var d=r[u];if(d.willRender(i)){var f,p,g,m;if(void 0!==d.alphaTopLeft)f=d.alphaTopLeft,p=d.alphaTopRight,g=d.alphaBottomLeft,m=d.alphaBottomRight;else{var x=d.alpha;f=x,p=x,g=x,m=x}var v=d.scrollFactorX,y=d.scrollFactorY;o||d.blendMode===t.currentBlendMode||t.setBlendMode(d.blendMode);var w=d.mask;w&&w.preRenderWebGL(t,d,i);var b=d.type;b!==t.currentType&&(t.newType=!0,t.currentType=b),t.nextTypeMatch=u<n-1&&r[u+1].type===t.currentType,d.setScrollFactor(v*l,y*c),d.setAlpha(f*h,p*h,g*h,m*h),d.renderWebGL(t,d,i,a,e),d.setAlpha(f,p,g,m),d.setScrollFactor(v,y),w&&w.postRenderWebGL(t,i),t.newType=!1}}t.pipelines.postBatch(e)}}},6608:t=>{t.exports="addedtoscene"},4265:t=>{t.exports="destroy"},8671:t=>{t.exports="removedfromscene"},3420:t=>{t.exports="complete"},601:t=>{t.exports="created"},7919:t=>{t.exports="error"},6231:t=>{t.exports="locked"},5241:t=>{t.exports="loop"},8325:t=>{t.exports="playing"},3356:t=>{t.exports="play"},7513:t=>{t.exports="seeked"},5788:t=>{t.exports="seeking"},7111:t=>{t.exports="stalled"},8118:t=>{t.exports="stop"},9184:t=>{t.exports="textureready"},4287:t=>{t.exports="unlocked"},857:t=>{t.exports="unsupported"},3389:(t,e,i)=>{t.exports={ADDED_TO_SCENE:i(6608),DESTROY:i(4265),REMOVED_FROM_SCENE:i(8671),VIDEO_COMPLETE:i(3420),VIDEO_CREATED:i(601),VIDEO_ERROR:i(7919),VIDEO_LOCKED:i(6231),VIDEO_LOOP:i(5241),VIDEO_PLAY:i(3356),VIDEO_PLAYING:i(8325),VIDEO_SEEKED:i(7513),VIDEO_SEEKING:i(5788),VIDEO_STALLED:i(7111),VIDEO_STOP:i(8118),VIDEO_TEXTURE:i(9184),VIDEO_UNLOCKED:i(4287),VIDEO_UNSUPPORTED:i(857)}},1643:t=>{t.exports={CIRCLE:0,ELLIPSE:1,LINE:2,POINT:3,POLYGON:4,RECTANGLE:5,TRIANGLE:6}},8881:(t,e,i)=>{var s=i(7655);t.exports=function(t,e,i){return void 0===i&&(i=new s),i.x=t.x1+(t.x2-t.x1)*e,i.y=t.y1+(t.y2-t.y1)*e,i}},4479:(t,e,i)=>{var s=i(4771),r=i(7655);t.exports=function(t,e,i,n){void 0===n&&(n=[]),!e&&i>0&&(e=s(t)/i);for(var a=t.x1,o=t.y1,h=t.x2,l=t.y2,c=0;c<e;c++){var u=c/e,d=a+(h-a)*u,f=o+(l-o)*u;n.push(new r(d,f))}return n}},4771:t=>{t.exports=function(t){return Math.sqrt((t.x2-t.x1)*(t.x2-t.x1)+(t.y2-t.y1)*(t.y2-t.y1))}},284:(t,e,i)=>{var s=i(7473),r=i(8881),n=i(4479),a=i(1643),o=i(3915),h=i(2529),l=new s({initialize:function(t,e,i,s){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=0),void 0===s&&(s=0),this.type=a.LINE,this.x1=t,this.y1=e,this.x2=i,this.y2=s},getPoint:function(t,e){return r(this,t,e)},getPoints:function(t,e,i){return n(this,t,e,i)},getRandomPoint:function(t){return o(this,t)},setTo:function(t,e,i,s){return void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=0),void 0===s&&(s=0),this.x1=t,this.y1=e,this.x2=i,this.y2=s,this},setFromObjects:function(t,e){return this.x1=t.x,this.y1=t.y,this.x2=e.x,this.y2=e.y,this},getPointA:function(t){return void 0===t&&(t=new h),t.set(this.x1,this.y1),t},getPointB:function(t){return void 0===t&&(t=new h),t.set(this.x2,this.y2),t},left:{get:function(){return Math.min(this.x1,this.x2)},set:function(t){this.x1<=this.x2?this.x1=t:this.x2=t}},right:{get:function(){return Math.max(this.x1,this.x2)},set:function(t){this.x1>this.x2?this.x1=t:this.x2=t}},top:{get:function(){return Math.min(this.y1,this.y2)},set:function(t){this.y1<=this.y2?this.y1=t:this.y2=t}},bottom:{get:function(){return Math.max(this.y1,this.y2)},set:function(t){this.y1>this.y2?this.y1=t:this.y2=t}}});t.exports=l},3915:(t,e,i)=>{var s=i(7655);t.exports=function(t,e){void 0===e&&(e=new s);var i=Math.random();return e.x=t.x1+i*(t.x2-t.x1),e.y=t.y1+i*(t.y2-t.y1),e}},7655:(t,e,i)=>{var s=i(7473),r=i(1643),n=new s({initialize:function(t,e){void 0===t&&(t=0),void 0===e&&(e=t),this.type=r.POINT,this.x=t,this.y=e},setTo:function(t,e){return void 0===t&&(t=0),void 0===e&&(e=t),this.x=t,this.y=e,this}});t.exports=n},5956:t=>{t.exports=function(t,e,i){return!(t.width<=0||t.height<=0)&&(t.x<=e&&t.x+t.width>=e&&t.y<=i&&t.y+t.height>=i)}},716:(t,e,i)=>{var s=i(7120),r=i(7655);t.exports=function(t,e,i){if(void 0===i&&(i=new r),e<=0||e>=1)return i.x=t.x,i.y=t.y,i;var n=s(t)*e;return e>.5?(n-=t.width+t.height)<=t.width?(i.x=t.right-n,i.y=t.bottom):(i.x=t.x,i.y=t.bottom-(n-t.width)):n<=t.width?(i.x=t.x+n,i.y=t.y):(i.x=t.right,i.y=t.y+(n-t.width)),i}},8151:(t,e,i)=>{var s=i(716),r=i(7120);t.exports=function(t,e,i,n){void 0===n&&(n=[]),!e&&i>0&&(e=r(t)/i);for(var a=0;a<e;a++){var o=a/e;n.push(s(t,o))}return n}},7120:t=>{t.exports=function(t){return 2*(t.width+t.height)}},2161:(t,e,i)=>{var s=i(7655);t.exports=function(t,e){return void 0===e&&(e=new s),e.x=t.x+Math.random()*t.width,e.y=t.y+Math.random()*t.height,e}},1392:(t,e,i)=>{var s=i(7473),r=i(5956),n=i(716),a=i(8151),o=i(1643),h=i(284),l=i(2161),c=new s({initialize:function(t,e,i,s){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=0),void 0===s&&(s=0),this.type=o.RECTANGLE,this.x=t,this.y=e,this.width=i,this.height=s},contains:function(t,e){return r(this,t,e)},getPoint:function(t,e){return n(this,t,e)},getPoints:function(t,e,i){return a(this,t,e,i)},getRandomPoint:function(t){return l(this,t)},setTo:function(t,e,i,s){return this.x=t,this.y=e,this.width=i,this.height=s,this},setEmpty:function(){return this.setTo(0,0,0,0)},setPosition:function(t,e){return void 0===e&&(e=t),this.x=t,this.y=e,this},setSize:function(t,e){return void 0===e&&(e=t),this.width=t,this.height=e,this},isEmpty:function(){return this.width<=0||this.height<=0},getLineA:function(t){return void 0===t&&(t=new h),t.setTo(this.x,this.y,this.right,this.y),t},getLineB:function(t){return void 0===t&&(t=new h),t.setTo(this.right,this.y,this.right,this.bottom),t},getLineC:function(t){return void 0===t&&(t=new h),t.setTo(this.right,this.bottom,this.x,this.bottom),t},getLineD:function(t){return void 0===t&&(t=new h),t.setTo(this.x,this.bottom,this.x,this.y),t},left:{get:function(){return this.x},set:function(t){t>=this.right?this.width=0:this.width=this.right-t,this.x=t}},right:{get:function(){return this.x+this.width},set:function(t){t<=this.x?this.width=0:this.width=t-this.x}},top:{get:function(){return this.y},set:function(t){t>=this.bottom?this.height=0:this.height=this.bottom-t,this.y=t}},bottom:{get:function(){return this.y+this.height},set:function(t){t<=this.y?this.height=0:this.height=t-this.y}},centerX:{get:function(){return this.x+this.width/2},set:function(t){this.x=t-this.width/2}},centerY:{get:function(){return this.y+this.height/2},set:function(t){this.y=t-this.height/2}}});t.exports=c},9422:(t,e,i)=>{var s=i(1392);t.exports=function(t,e,i){void 0===i&&(i=new s);var r=Math.min(t.x,e.x),n=Math.min(t.y,e.y),a=Math.max(t.right,e.right)-r,o=Math.max(t.bottom,e.bottom)-n;return i.setTo(r,n,a,o)}},1593:(t,e,i)=>{var s=i(7473),r=i(4359),n=i(1179),a=i(4597),o=i(5593),h=i(7410),l=i(5874),c=i(707),u=new s({initialize:function(t,e){if(this.loader=t,this.cache=a(e,"cache",!1),this.type=a(e,"type",!1),!this.type)throw new Error("Invalid File type: "+this.type);this.key=a(e,"key",!1);var i=this.key;if(t.prefix&&""!==t.prefix&&(this.key=t.prefix+i),!this.key)throw new Error("Invalid File key: "+this.key);var s=a(e,"url");void 0===s?s=t.path+i+"."+a(e,"extension",""):"string"!=typeof s||s.match(/^(?:blob:|data:|capacitor:\/\/|http:\/\/|https:\/\/|\/\/)/)||(s=t.path+s),this.url=s,this.src="",this.xhrSettings=c(a(e,"responseType",void 0)),a(e,"xhrSettings",!1)&&(this.xhrSettings=h(this.xhrSettings,a(e,"xhrSettings",{}))),this.xhrLoader=null,this.state="function"==typeof this.url?r.FILE_POPULATED:r.FILE_PENDING,this.bytesTotal=0,this.bytesLoaded=-1,this.percentComplete=-1,this.crossOrigin=void 0,this.data=void 0,this.config=a(e,"config",{}),this.multiFile,this.linkFile},setLink:function(t){this.linkFile=t,t.linkFile=this},resetXHR:function(){this.xhrLoader&&(this.xhrLoader.onload=void 0,this.xhrLoader.onerror=void 0,this.xhrLoader.onprogress=void 0)},load:function(){this.state===r.FILE_POPULATED?this.loader.nextFile(this,!0):(this.state=r.FILE_LOADING,this.src=o(this,this.loader.baseURL),0===this.src.indexOf("data:")?console.warn("Local data URIs are not supported: "+this.key):this.xhrLoader=l(this,this.loader.xhr))},onLoad:function(t,e){var i=t.responseURL&&this.loader.localSchemes.some((function(e){return 0===t.responseURL.indexOf(e)}))&&0===e.target.status,s=!(e.target&&200!==e.target.status)||i;4===t.readyState&&t.status>=400&&t.status<=599&&(s=!1),this.state=r.FILE_LOADED,this.resetXHR(),this.loader.nextFile(this,s)},onError:function(){this.resetXHR(),this.loader.nextFile(this,!1)},onProgress:function(t){t.lengthComputable&&(this.bytesLoaded=t.loaded,this.bytesTotal=t.total,this.percentComplete=Math.min(this.bytesLoaded/this.bytesTotal,1),this.loader.emit(n.FILE_PROGRESS,this,this.percentComplete))},onProcess:function(){this.state=r.FILE_PROCESSING,this.onProcessComplete()},onProcessComplete:function(){this.state=r.FILE_COMPLETE,this.multiFile&&this.multiFile.onFileComplete(this),this.loader.fileProcessComplete(this)},onProcessError:function(){console.error('Failed to process file: %s "%s"',this.type,this.key),this.state=r.FILE_ERRORED,this.multiFile&&this.multiFile.onFileFailed(this),this.loader.fileProcessComplete(this)},hasCacheConflict:function(){return this.cache&&this.cache.exists(this.key)},addToCache:function(){this.cache&&this.data&&this.cache.add(this.key,this.data)},pendingDestroy:function(t){if(this.state!==r.FILE_PENDING_DESTROY){void 0===t&&(t=this.data);var e=this.key,i=this.type;this.loader.emit(n.FILE_COMPLETE,e,i,t),this.loader.emit(n.FILE_KEY_COMPLETE+i+"-"+e,e,i,t),this.loader.flagForRemoval(this),this.state=r.FILE_PENDING_DESTROY}},destroy:function(){this.loader=null,this.cache=null,this.xhrSettings=null,this.multiFile=null,this.linkFile=null,this.data=null}});u.createObjectURL=function(t,e,i){if("function"==typeof URL)t.src=URL.createObjectURL(e);else{var s=new FileReader;s.onload=function(){t.removeAttribute("crossOrigin"),t.src="data:"+(e.type||i)+";base64,"+s.result.split(",")[1]},s.onerror=t.onerror,s.readAsDataURL(e)}},u.revokeObjectURL=function(t){"function"==typeof URL&&URL.revokeObjectURL(t.src)},t.exports=u},9845:t=>{var e={},i={install:function(t){for(var i in e)t[i]=e[i]},register:function(t,i){e[t]=i},destroy:function(){e={}}};t.exports=i},5593:t=>{t.exports=function(t,e){return!!t.url&&(t.url.match(/^(?:blob:|data:|capacitor:\/\/|http:\/\/|https:\/\/|\/\/)/)?t.url:e+t.url)}},7410:(t,e,i)=>{var s=i(1030),r=i(707);t.exports=function(t,e){var i=void 0===t?r():s({},t);if(e)for(var n in e)void 0!==e[n]&&(i[n]=e[n]);return i}},3137:(t,e,i)=>{var s=i(7473),r=i(4359),n=i(1179),a=new s({initialize:function(t,e,i,s){var n=[];s.forEach((function(t){t&&n.push(t)})),this.loader=t,this.type=e,this.key=i;var a=this.key;t.prefix&&""!==t.prefix&&(this.key=t.prefix+a),this.multiKeyIndex=t.multiKeyIndex++,this.files=n,this.state=r.FILE_PENDING,this.complete=!1,this.pending=n.length,this.failed=0,this.config={},this.baseURL=t.baseURL,this.path=t.path,this.prefix=t.prefix;for(var o=0;o<n.length;o++)n[o].multiFile=this},isReadyToProcess:function(){return 0===this.pending&&0===this.failed&&!this.complete},addToMultiFile:function(t){return this.files.push(t),t.multiFile=this,this.pending++,this.complete=!1,this},onFileComplete:function(t){-1!==this.files.indexOf(t)&&this.pending--},onFileFailed:function(t){-1!==this.files.indexOf(t)&&(this.failed++,console.error('File failed: %s "%s" (via %s "%s")',this.type,this.key,t.type,t.key))},pendingDestroy:function(){if(this.state!==r.FILE_PENDING_DESTROY){var t=this.key,e=this.type;this.loader.emit(n.FILE_COMPLETE,t,e),this.loader.emit(n.FILE_KEY_COMPLETE+e+"-"+t,t,e),this.loader.flagForRemoval(this);for(var i=0;i<this.files.length;i++)this.files[i].pendingDestroy();this.state=r.FILE_PENDING_DESTROY}},destroy:function(){this.loader=null,this.files=null,this.config=null}});t.exports=a},5874:(t,e,i)=>{var s=i(7410);t.exports=function(t,e){var i=s(e,t.xhrSettings),r=new XMLHttpRequest;if(r.open("GET",t.src,i.async,i.user,i.password),r.responseType=t.xhrSettings.responseType,r.timeout=i.timeout,i.headers)for(var n in i.headers)r.setRequestHeader(n,i.headers[n]);return i.header&&i.headerValue&&r.setRequestHeader(i.header,i.headerValue),i.requestedWith&&r.setRequestHeader("X-Requested-With",i.requestedWith),i.overrideMimeType&&r.overrideMimeType(i.overrideMimeType),i.withCredentials&&(r.withCredentials=!0),r.onload=t.onLoad.bind(t,r),r.onerror=t.onError.bind(t,r),r.onprogress=t.onProgress.bind(t),r.send(),r}},707:t=>{t.exports=function(t,e,i,s,r,n){return void 0===t&&(t=""),void 0===e&&(e=!0),void 0===i&&(i=""),void 0===s&&(s=""),void 0===r&&(r=0),void 0===n&&(n=!1),{responseType:t,async:e,user:i,password:s,timeout:r,headers:void 0,header:void 0,headerValue:void 0,requestedWith:!1,overrideMimeType:void 0,withCredentials:n}}},4359:t=>{t.exports={LOADER_IDLE:0,LOADER_LOADING:1,LOADER_PROCESSING:2,LOADER_COMPLETE:3,LOADER_SHUTDOWN:4,LOADER_DESTROYED:5,FILE_PENDING:10,FILE_LOADING:11,FILE_LOADED:12,FILE_FAILED:13,FILE_PROCESSING:14,FILE_ERRORED:16,FILE_COMPLETE:17,FILE_DESTROYED:18,FILE_POPULATED:19,FILE_PENDING_DESTROY:20}},462:t=>{t.exports="addfile"},7297:t=>{t.exports="complete"},8660:t=>{t.exports="filecomplete"},6484:t=>{t.exports="filecomplete-"},7972:t=>{t.exports="loaderror"},1906:t=>{t.exports="load"},1441:t=>{t.exports="fileprogress"},1072:t=>{t.exports="postprocess"},1927:t=>{t.exports="progress"},6597:t=>{t.exports="start"},1179:(t,e,i)=>{t.exports={ADD:i(462),COMPLETE:i(7297),FILE_COMPLETE:i(8660),FILE_KEY_COMPLETE:i(6484),FILE_LOAD_ERROR:i(7972),FILE_LOAD:i(1906),FILE_PROGRESS:i(1441),POST_PROCESS:i(1072),PROGRESS:i(1927),START:i(6597)}},6732:(t,e,i)=>{var s=i(7473),r=i(4359),n=i(1593),a=i(9845),o=i(4597),h=i(2482),l=i(5593),c=new s({Extends:n,initialize:function t(e,i,s,r,a){var l,c="png";if(h(i)){var u=i;i=o(u,"key"),s=o(u,"url"),l=o(u,"normalMap"),r=o(u,"xhrSettings"),c=o(u,"extension",c),a=o(u,"frameConfig")}Array.isArray(s)&&(l=s[1],s=s[0]);var d={type:"image",cache:e.textureManager,extension:c,responseType:"blob",key:i,url:s,xhrSettings:r,config:a};if(n.call(this,e,d),l){var f=new t(e,this.key,l,r,a);f.type="normalMap",this.setLink(f),e.addFile(f)}this.useImageElementLoad="HTMLImageElement"===e.imageLoadType,this.useImageElementLoad&&(this.load=this.loadImage,this.onProcess=this.onProcessImage)},onProcess:function(){this.state=r.FILE_PROCESSING,this.data=new Image,this.data.crossOrigin=this.crossOrigin;var t=this;this.data.onload=function(){n.revokeObjectURL(t.data),t.onProcessComplete()},this.data.onerror=function(){n.revokeObjectURL(t.data),t.onProcessError()},n.createObjectURL(this.data,this.xhrLoader.response,"image/png")},onProcessImage:function(){var t=this.state;this.state=r.FILE_PROCESSING,t===r.FILE_LOADED?this.onProcessComplete():this.onProcessError()},loadImage:function(){if(this.state=r.FILE_LOADING,this.src=l(this,this.loader.baseURL),0===this.src.indexOf("data:"))console.warn("Local data URIs are not supported: "+this.key);else{this.data=new Image,this.data.crossOrigin=this.crossOrigin;var t=this;this.data.onload=function(){t.state=r.FILE_LOADED,t.loader.nextFile(t,!0)},this.data.onerror=function(){t.loader.nextFile(t,!1)},this.data.src=this.src}},addToCache:function(){var t=this.linkFile;t?t.state>=r.FILE_COMPLETE&&("spritesheet"===t.type?t.addToCache():"normalMap"===this.type?this.cache.addImage(this.key,t.data,this.data):this.cache.addImage(this.key,this.data,t.data)):this.cache.addImage(this.key,this.data)}});a.register("image",(function(t,e,i){if(Array.isArray(t))for(var s=0;s<t.length;s++)this.addFile(new c(this,t[s]));else this.addFile(new c(this,t,e,i));return this})),t.exports=c},704:(t,e,i)=>{var s=i(7473),r=i(4359),n=i(1593),a=i(9845),o=i(4597),h=i(5851),l=i(2482),c=new s({Extends:n,initialize:function(t,e,i,s,a){var c="json";if(l(e)){var u=e;e=o(u,"key"),i=o(u,"url"),s=o(u,"xhrSettings"),c=o(u,"extension",c),a=o(u,"dataKey",a)}var d={type:"json",cache:t.cacheManager.json,extension:c,responseType:"text",key:e,url:i,xhrSettings:s,config:a};n.call(this,t,d),l(i)&&(this.data=a?h(i,a):i,this.state=r.FILE_POPULATED)},onProcess:function(){if(this.state!==r.FILE_POPULATED){this.state=r.FILE_PROCESSING;try{var t=JSON.parse(this.xhrLoader.responseText)}catch(t){throw this.onProcessError(),t}var e=this.config;this.data="string"==typeof e?h(t,e,t):t}this.onProcessComplete()}});a.register("json",(function(t,e,i,s){if(Array.isArray(t))for(var r=0;r<t.length;r++)this.addFile(new c(this,t[r]));else this.addFile(new c(this,t,e,s,i));return this})),t.exports=c},1192:(t,e,i)=>{var s=i(7473),r=i(4359),n=i(1593),a=i(9845),o=i(4597),h=i(2482),l=new s({Extends:n,initialize:function(t,e,i,s){var r="text",a="txt",l=t.cacheManager.text;if(h(e)){var c=e;e=o(c,"key"),i=o(c,"url"),s=o(c,"xhrSettings"),a=o(c,"extension",a),r=o(c,"type",r),l=o(c,"cache",l)}var u={type:r,cache:l,extension:a,responseType:"text",key:e,url:i,xhrSettings:s};n.call(this,t,u)},onProcess:function(){this.state=r.FILE_PROCESSING,this.data=this.xhrLoader.responseText,this.onProcessComplete()}});a.register("text",(function(t,e,i){if(Array.isArray(t))for(var s=0;s<t.length;s++)this.addFile(new l(this,t[s]));else this.addFile(new l(this,t,e,i));return this})),t.exports=l},3136:t=>{t.exports=function(t){for(var e=0,i=0;i<t.length;i++)e+=+t[i];return e/t.length}},785:(t,e,i)=>{var s=i(3916);t.exports=function(t,e){return s(t)/s(e)/s(t-e)}},7025:t=>{t.exports=function(t,e){return Math.floor(Math.random()*(e-t+1)+t)}},48:t=>{t.exports=function(t,e,i,s,r){var n=.5*(s-e),a=.5*(r-i),o=t*t;return(2*i-2*s+n+a)*(t*o)+(-3*i+3*s-2*n-a)*o+n*t+i}},5035:t=>{t.exports=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=10);var s=Math.pow(i,-e);return Math.ceil(t*s)/s}},2915:t=>{t.exports=function(t,e,i){return Math.max(e,Math.min(i,t))}},7149:(t,e,i)=>{var s=i(7425);t.exports=function(t){return t*s.DEG_TO_RAD}},2975:t=>{t.exports=function(t,e){return Math.abs(t-e)}},2107:(t,e,i)=>{var s=i(2915),r=i(7473),n=i(9652),a=i(1984),o=new n,h=new r({initialize:function t(e,i,s,r){void 0===e&&(e=0),void 0===i&&(i=0),void 0===s&&(s=0),void 0===r&&(r=t.DefaultOrder),this._x=e,this._y=i,this._z=s,this._order=r,this.onChangeCallback=a},x:{get:function(){return this._x},set:function(t){this._x=t,this.onChangeCallback(this)}},y:{get:function(){return this._y},set:function(t){this._y=t,this.onChangeCallback(this)}},z:{get:function(){return this._z},set:function(t){this._z=t,this.onChangeCallback(this)}},order:{get:function(){return this._order},set:function(t){this._order=t,this.onChangeCallback(this)}},set:function(t,e,i,s){return void 0===s&&(s=this._order),this._x=t,this._y=e,this._z=i,this._order=s,this.onChangeCallback(this),this},copy:function(t){return this.set(t.x,t.y,t.z,t.order)},setFromQuaternion:function(t,e,i){return void 0===e&&(e=this._order),void 0===i&&(i=!1),o.fromQuat(t),this.setFromRotationMatrix(o,e,i)},setFromRotationMatrix:function(t,e,i){void 0===e&&(e=this._order),void 0===i&&(i=!1);var r=t.val,n=r[0],a=r[4],o=r[8],h=r[1],l=r[5],c=r[9],u=r[2],d=r[6],f=r[10],p=0,g=0,m=0,x=.99999;switch(e){case"XYZ":g=Math.asin(s(o,-1,1)),Math.abs(o)<x?(p=Math.atan2(-c,f),m=Math.atan2(-a,n)):p=Math.atan2(d,l);break;case"YXZ":p=Math.asin(-s(c,-1,1)),Math.abs(c)<x?(g=Math.atan2(o,f),m=Math.atan2(h,l)):g=Math.atan2(-u,n);break;case"ZXY":p=Math.asin(s(d,-1,1)),Math.abs(d)<x?(g=Math.atan2(-u,f),m=Math.atan2(-a,l)):m=Math.atan2(h,n);break;case"ZYX":g=Math.asin(-s(u,-1,1)),Math.abs(u)<x?(p=Math.atan2(d,f),m=Math.atan2(h,n)):m=Math.atan2(-a,l);break;case"YZX":m=Math.asin(s(h,-1,1)),Math.abs(h)<x?(p=Math.atan2(-c,l),g=Math.atan2(-u,n)):g=Math.atan2(o,f);break;case"XZY":m=Math.asin(-s(a,-1,1)),Math.abs(a)<x?(p=Math.atan2(d,l),g=Math.atan2(o,n)):p=Math.atan2(-c,f)}return this._x=p,this._y=g,this._z=m,this._order=e,i&&this.onChangeCallback(this),this}});h.RotationOrders=["XYZ","YXZ","ZXY","ZYX","YZX","XZY"],h.DefaultOrder="XYZ",t.exports=h},3916:t=>{t.exports=function(t){if(0===t)return 1;for(var e=t;--t;)e*=t;return e}},104:t=>{t.exports=function(t,e){return Math.random()*(e-t)+t}},4941:t=>{t.exports=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=10);var s=Math.pow(i,-e);return Math.floor(t*s)/s}},1555:(t,e,i)=>{var s=i(2915);t.exports=function(t,e,i){return(i-e)*(t=s(t,0,1))+e}},5005:t=>{t.exports=function(t,e){return t/e/1e3}},3702:t=>{t.exports=function(t){return t==parseFloat(t)?!(t%2):void 0}},8820:t=>{t.exports=function(t){return t===parseFloat(t)?!(t%2):void 0}},1743:t=>{t.exports=function(t,e,i){return(e-t)*i+t}},3416:t=>{t.exports=function(t,e,i){return void 0===i&&(i=0),t.clone().lerp(e,i)}},2149:(t,e,i)=>{var s=new(i(7473))({initialize:function(t){this.val=new Float32Array(9),t?this.copy(t):this.identity()},clone:function(){return new s(this)},set:function(t){return this.copy(t)},copy:function(t){var e=this.val,i=t.val;return e[0]=i[0],e[1]=i[1],e[2]=i[2],e[3]=i[3],e[4]=i[4],e[5]=i[5],e[6]=i[6],e[7]=i[7],e[8]=i[8],this},fromMat4:function(t){var e=t.val,i=this.val;return i[0]=e[0],i[1]=e[1],i[2]=e[2],i[3]=e[4],i[4]=e[5],i[5]=e[6],i[6]=e[8],i[7]=e[9],i[8]=e[10],this},fromArray:function(t){var e=this.val;return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],e[6]=t[6],e[7]=t[7],e[8]=t[8],this},identity:function(){var t=this.val;return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=0,t[7]=0,t[8]=1,this},transpose:function(){var t=this.val,e=t[1],i=t[2],s=t[5];return t[1]=t[3],t[2]=t[6],t[3]=e,t[5]=t[7],t[6]=i,t[7]=s,this},invert:function(){var t=this.val,e=t[0],i=t[1],s=t[2],r=t[3],n=t[4],a=t[5],o=t[6],h=t[7],l=t[8],c=l*n-a*h,u=-l*r+a*o,d=h*r-n*o,f=e*c+i*u+s*d;return f?(f=1/f,t[0]=c*f,t[1]=(-l*i+s*h)*f,t[2]=(a*i-s*n)*f,t[3]=u*f,t[4]=(l*e-s*o)*f,t[5]=(-a*e+s*r)*f,t[6]=d*f,t[7]=(-h*e+i*o)*f,t[8]=(n*e-i*r)*f,this):null},adjoint:function(){var t=this.val,e=t[0],i=t[1],s=t[2],r=t[3],n=t[4],a=t[5],o=t[6],h=t[7],l=t[8];return t[0]=n*l-a*h,t[1]=s*h-i*l,t[2]=i*a-s*n,t[3]=a*o-r*l,t[4]=e*l-s*o,t[5]=s*r-e*a,t[6]=r*h-n*o,t[7]=i*o-e*h,t[8]=e*n-i*r,this},determinant:function(){var t=this.val,e=t[0],i=t[1],s=t[2],r=t[3],n=t[4],a=t[5],o=t[6],h=t[7],l=t[8];return e*(l*n-a*h)+i*(-l*r+a*o)+s*(h*r-n*o)},multiply:function(t){var e=this.val,i=e[0],s=e[1],r=e[2],n=e[3],a=e[4],o=e[5],h=e[6],l=e[7],c=e[8],u=t.val,d=u[0],f=u[1],p=u[2],g=u[3],m=u[4],x=u[5],v=u[6],y=u[7],w=u[8];return e[0]=d*i+f*n+p*h,e[1]=d*s+f*a+p*l,e[2]=d*r+f*o+p*c,e[3]=g*i+m*n+x*h,e[4]=g*s+m*a+x*l,e[5]=g*r+m*o+x*c,e[6]=v*i+y*n+w*h,e[7]=v*s+y*a+w*l,e[8]=v*r+y*o+w*c,this},translate:function(t){var e=this.val,i=t.x,s=t.y;return e[6]=i*e[0]+s*e[3]+e[6],e[7]=i*e[1]+s*e[4]+e[7],e[8]=i*e[2]+s*e[5]+e[8],this},rotate:function(t){var e=this.val,i=e[0],s=e[1],r=e[2],n=e[3],a=e[4],o=e[5],h=Math.sin(t),l=Math.cos(t);return e[0]=l*i+h*n,e[1]=l*s+h*a,e[2]=l*r+h*o,e[3]=l*n-h*i,e[4]=l*a-h*s,e[5]=l*o-h*r,this},scale:function(t){var e=this.val,i=t.x,s=t.y;return e[0]=i*e[0],e[1]=i*e[1],e[2]=i*e[2],e[3]=s*e[3],e[4]=s*e[4],e[5]=s*e[5],this},fromQuat:function(t){var e=t.x,i=t.y,s=t.z,r=t.w,n=e+e,a=i+i,o=s+s,h=e*n,l=e*a,c=e*o,u=i*a,d=i*o,f=s*o,p=r*n,g=r*a,m=r*o,x=this.val;return x[0]=1-(u+f),x[3]=l+m,x[6]=c-g,x[1]=l-m,x[4]=1-(h+f),x[7]=d+p,x[2]=c+g,x[5]=d-p,x[8]=1-(h+u),this},normalFromMat4:function(t){var e=t.val,i=this.val,s=e[0],r=e[1],n=e[2],a=e[3],o=e[4],h=e[5],l=e[6],c=e[7],u=e[8],d=e[9],f=e[10],p=e[11],g=e[12],m=e[13],x=e[14],v=e[15],y=s*h-r*o,w=s*l-n*o,b=s*c-a*o,A=r*l-n*h,M=r*c-a*h,E=n*c-a*l,S=u*m-d*g,T=u*x-f*g,I=u*v-p*g,C=d*x-f*m,R=d*v-p*m,k=f*v-p*x,F=y*k-w*R+b*C+A*I-M*T+E*S;return F?(F=1/F,i[0]=(h*k-l*R+c*C)*F,i[1]=(l*I-o*k-c*T)*F,i[2]=(o*R-h*I+c*S)*F,i[3]=(n*R-r*k-a*C)*F,i[4]=(s*k-n*I+a*T)*F,i[5]=(r*I-s*R-a*S)*F,i[6]=(m*E-x*M+v*A)*F,i[7]=(x*b-g*E-v*w)*F,i[8]=(g*M-m*b+v*y)*F,this):null}});t.exports=s},9652:(t,e,i)=>{var s=i(7473),r=i(5689),n=1e-6,a=new s({initialize:function(t){this.val=new Float32Array(16),t?this.copy(t):this.identity()},clone:function(){return new a(this)},set:function(t){return this.copy(t)},setValues:function(t,e,i,s,r,n,a,o,h,l,c,u,d,f,p,g){var m=this.val;return m[0]=t,m[1]=e,m[2]=i,m[3]=s,m[4]=r,m[5]=n,m[6]=a,m[7]=o,m[8]=h,m[9]=l,m[10]=c,m[11]=u,m[12]=d,m[13]=f,m[14]=p,m[15]=g,this},copy:function(t){var e=t.val;return this.setValues(e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15])},fromArray:function(t){return this.setValues(t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8],t[9],t[10],t[11],t[12],t[13],t[14],t[15])},zero:function(){return this.setValues(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)},transform:function(t,e,i){var s=o.fromQuat(i).val,r=e.x,n=e.y,a=e.z;return this.setValues(s[0]*r,s[1]*r,s[2]*r,0,s[4]*n,s[5]*n,s[6]*n,0,s[8]*a,s[9]*a,s[10]*a,0,t.x,t.y,t.z,1)},xyz:function(t,e,i){this.identity();var s=this.val;return s[12]=t,s[13]=e,s[14]=i,this},scaling:function(t,e,i){this.zero();var s=this.val;return s[0]=t,s[5]=e,s[10]=i,s[15]=1,this},identity:function(){return this.setValues(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)},transpose:function(){var t=this.val,e=t[1],i=t[2],s=t[3],r=t[6],n=t[7],a=t[11];return t[1]=t[4],t[2]=t[8],t[3]=t[12],t[4]=e,t[6]=t[9],t[7]=t[13],t[8]=i,t[9]=r,t[11]=t[14],t[12]=s,t[13]=n,t[14]=a,this},getInverse:function(t){return this.copy(t),this.invert()},invert:function(){var t=this.val,e=t[0],i=t[1],s=t[2],r=t[3],n=t[4],a=t[5],o=t[6],h=t[7],l=t[8],c=t[9],u=t[10],d=t[11],f=t[12],p=t[13],g=t[14],m=t[15],x=e*a-i*n,v=e*o-s*n,y=e*h-r*n,w=i*o-s*a,b=i*h-r*a,A=s*h-r*o,M=l*p-c*f,E=l*g-u*f,S=l*m-d*f,T=c*g-u*p,I=c*m-d*p,C=u*m-d*g,R=x*C-v*I+y*T+w*S-b*E+A*M;return R?(R=1/R,this.setValues((a*C-o*I+h*T)*R,(s*I-i*C-r*T)*R,(p*A-g*b+m*w)*R,(u*b-c*A-d*w)*R,(o*S-n*C-h*E)*R,(e*C-s*S+r*E)*R,(g*y-f*A-m*v)*R,(l*A-u*y+d*v)*R,(n*I-a*S+h*M)*R,(i*S-e*I-r*M)*R,(f*b-p*y+m*x)*R,(c*y-l*b-d*x)*R,(a*E-n*T-o*M)*R,(e*T-i*E+s*M)*R,(p*v-f*w-g*x)*R,(l*w-c*v+u*x)*R)):this},adjoint:function(){var t=this.val,e=t[0],i=t[1],s=t[2],r=t[3],n=t[4],a=t[5],o=t[6],h=t[7],l=t[8],c=t[9],u=t[10],d=t[11],f=t[12],p=t[13],g=t[14],m=t[15];return this.setValues(a*(u*m-d*g)-c*(o*m-h*g)+p*(o*d-h*u),-(i*(u*m-d*g)-c*(s*m-r*g)+p*(s*d-r*u)),i*(o*m-h*g)-a*(s*m-r*g)+p*(s*h-r*o),-(i*(o*d-h*u)-a*(s*d-r*u)+c*(s*h-r*o)),-(n*(u*m-d*g)-l*(o*m-h*g)+f*(o*d-h*u)),e*(u*m-d*g)-l*(s*m-r*g)+f*(s*d-r*u),-(e*(o*m-h*g)-n*(s*m-r*g)+f*(s*h-r*o)),e*(o*d-h*u)-n*(s*d-r*u)+l*(s*h-r*o),n*(c*m-d*p)-l*(a*m-h*p)+f*(a*d-h*c),-(e*(c*m-d*p)-l*(i*m-r*p)+f*(i*d-r*c)),e*(a*m-h*p)-n*(i*m-r*p)+f*(i*h-r*a),-(e*(a*d-h*c)-n*(i*d-r*c)+l*(i*h-r*a)),-(n*(c*g-u*p)-l*(a*g-o*p)+f*(a*u-o*c)),e*(c*g-u*p)-l*(i*g-s*p)+f*(i*u-s*c),-(e*(a*g-o*p)-n*(i*g-s*p)+f*(i*o-s*a)),e*(a*u-o*c)-n*(i*u-s*c)+l*(i*o-s*a))},determinant:function(){var t=this.val,e=t[0],i=t[1],s=t[2],r=t[3],n=t[4],a=t[5],o=t[6],h=t[7],l=t[8],c=t[9],u=t[10],d=t[11],f=t[12],p=t[13],g=t[14],m=t[15];return(e*a-i*n)*(u*m-d*g)-(e*o-s*n)*(c*m-d*p)+(e*h-r*n)*(c*g-u*p)+(i*o-s*a)*(l*m-d*f)-(i*h-r*a)*(l*g-u*f)+(s*h-r*o)*(l*p-c*f)},multiply:function(t){var e=this.val,i=e[0],s=e[1],r=e[2],n=e[3],a=e[4],o=e[5],h=e[6],l=e[7],c=e[8],u=e[9],d=e[10],f=e[11],p=e[12],g=e[13],m=e[14],x=e[15],v=t.val,y=v[0],w=v[1],b=v[2],A=v[3];return e[0]=y*i+w*a+b*c+A*p,e[1]=y*s+w*o+b*u+A*g,e[2]=y*r+w*h+b*d+A*m,e[3]=y*n+w*l+b*f+A*x,y=v[4],w=v[5],b=v[6],A=v[7],e[4]=y*i+w*a+b*c+A*p,e[5]=y*s+w*o+b*u+A*g,e[6]=y*r+w*h+b*d+A*m,e[7]=y*n+w*l+b*f+A*x,y=v[8],w=v[9],b=v[10],A=v[11],e[8]=y*i+w*a+b*c+A*p,e[9]=y*s+w*o+b*u+A*g,e[10]=y*r+w*h+b*d+A*m,e[11]=y*n+w*l+b*f+A*x,y=v[12],w=v[13],b=v[14],A=v[15],e[12]=y*i+w*a+b*c+A*p,e[13]=y*s+w*o+b*u+A*g,e[14]=y*r+w*h+b*d+A*m,e[15]=y*n+w*l+b*f+A*x,this},multiplyLocal:function(t){var e=this.val,i=t.val;return this.setValues(e[0]*i[0]+e[1]*i[4]+e[2]*i[8]+e[3]*i[12],e[0]*i[1]+e[1]*i[5]+e[2]*i[9]+e[3]*i[13],e[0]*i[2]+e[1]*i[6]+e[2]*i[10]+e[3]*i[14],e[0]*i[3]+e[1]*i[7]+e[2]*i[11]+e[3]*i[15],e[4]*i[0]+e[5]*i[4]+e[6]*i[8]+e[7]*i[12],e[4]*i[1]+e[5]*i[5]+e[6]*i[9]+e[7]*i[13],e[4]*i[2]+e[5]*i[6]+e[6]*i[10]+e[7]*i[14],e[4]*i[3]+e[5]*i[7]+e[6]*i[11]+e[7]*i[15],e[8]*i[0]+e[9]*i[4]+e[10]*i[8]+e[11]*i[12],e[8]*i[1]+e[9]*i[5]+e[10]*i[9]+e[11]*i[13],e[8]*i[2]+e[9]*i[6]+e[10]*i[10]+e[11]*i[14],e[8]*i[3]+e[9]*i[7]+e[10]*i[11]+e[11]*i[15],e[12]*i[0]+e[13]*i[4]+e[14]*i[8]+e[15]*i[12],e[12]*i[1]+e[13]*i[5]+e[14]*i[9]+e[15]*i[13],e[12]*i[2]+e[13]*i[6]+e[14]*i[10]+e[15]*i[14],e[12]*i[3]+e[13]*i[7]+e[14]*i[11]+e[15]*i[15])},premultiply:function(t){return this.multiplyMatrices(t,this)},multiplyMatrices:function(t,e){var i=t.val,s=e.val,r=i[0],n=i[4],a=i[8],o=i[12],h=i[1],l=i[5],c=i[9],u=i[13],d=i[2],f=i[6],p=i[10],g=i[14],m=i[3],x=i[7],v=i[11],y=i[15],w=s[0],b=s[4],A=s[8],M=s[12],E=s[1],S=s[5],T=s[9],I=s[13],C=s[2],R=s[6],k=s[10],F=s[14],L=s[3],P=s[7],O=s[11],Y=s[15];return this.setValues(r*w+n*E+a*C+o*L,h*w+l*E+c*C+u*L,d*w+f*E+p*C+g*L,m*w+x*E+v*C+y*L,r*b+n*S+a*R+o*P,h*b+l*S+c*R+u*P,d*b+f*S+p*R+g*P,m*b+x*S+v*R+y*P,r*A+n*T+a*k+o*O,h*A+l*T+c*k+u*O,d*A+f*T+p*k+g*O,m*A+x*T+v*k+y*O,r*M+n*I+a*F+o*Y,h*M+l*I+c*F+u*Y,d*M+f*I+p*F+g*Y,m*M+x*I+v*F+y*Y)},translate:function(t){return this.translateXYZ(t.x,t.y,t.z)},translateXYZ:function(t,e,i){var s=this.val;return s[12]=s[0]*t+s[4]*e+s[8]*i+s[12],s[13]=s[1]*t+s[5]*e+s[9]*i+s[13],s[14]=s[2]*t+s[6]*e+s[10]*i+s[14],s[15]=s[3]*t+s[7]*e+s[11]*i+s[15],this},scale:function(t){return this.scaleXYZ(t.x,t.y,t.z)},scaleXYZ:function(t,e,i){var s=this.val;return s[0]=s[0]*t,s[1]=s[1]*t,s[2]=s[2]*t,s[3]=s[3]*t,s[4]=s[4]*e,s[5]=s[5]*e,s[6]=s[6]*e,s[7]=s[7]*e,s[8]=s[8]*i,s[9]=s[9]*i,s[10]=s[10]*i,s[11]=s[11]*i,this},makeRotationAxis:function(t,e){var i=Math.cos(e),s=Math.sin(e),r=1-i,n=t.x,a=t.y,o=t.z,h=r*n,l=r*a;return this.setValues(h*n+i,h*a-s*o,h*o+s*a,0,h*a+s*o,l*a+i,l*o-s*n,0,h*o-s*a,l*o+s*n,r*o*o+i,0,0,0,0,1)},rotate:function(t,e){var i=this.val,s=e.x,r=e.y,a=e.z,o=Math.sqrt(s*s+r*r+a*a);if(Math.abs(o)<n)return this;s*=o=1/o,r*=o,a*=o;var h=Math.sin(t),l=Math.cos(t),c=1-l,u=i[0],d=i[1],f=i[2],p=i[3],g=i[4],m=i[5],x=i[6],v=i[7],y=i[8],w=i[9],b=i[10],A=i[11],M=i[12],E=i[13],S=i[14],T=i[15],I=s*s*c+l,C=r*s*c+a*h,R=a*s*c-r*h,k=s*r*c-a*h,F=r*r*c+l,L=a*r*c+s*h,P=s*a*c+r*h,O=r*a*c-s*h,Y=a*a*c+l;return this.setValues(u*I+g*C+y*R,d*I+m*C+w*R,f*I+x*C+b*R,p*I+v*C+A*R,u*k+g*F+y*L,d*k+m*F+w*L,f*k+x*F+b*L,p*k+v*F+A*L,u*P+g*O+y*Y,d*P+m*O+w*Y,f*P+x*O+b*Y,p*P+v*O+A*Y,M,E,S,T)},rotateX:function(t){var e=this.val,i=Math.sin(t),s=Math.cos(t),r=e[4],n=e[5],a=e[6],o=e[7],h=e[8],l=e[9],c=e[10],u=e[11];return e[4]=r*s+h*i,e[5]=n*s+l*i,e[6]=a*s+c*i,e[7]=o*s+u*i,e[8]=h*s-r*i,e[9]=l*s-n*i,e[10]=c*s-a*i,e[11]=u*s-o*i,this},rotateY:function(t){var e=this.val,i=Math.sin(t),s=Math.cos(t),r=e[0],n=e[1],a=e[2],o=e[3],h=e[8],l=e[9],c=e[10],u=e[11];return e[0]=r*s-h*i,e[1]=n*s-l*i,e[2]=a*s-c*i,e[3]=o*s-u*i,e[8]=r*i+h*s,e[9]=n*i+l*s,e[10]=a*i+c*s,e[11]=o*i+u*s,this},rotateZ:function(t){var e=this.val,i=Math.sin(t),s=Math.cos(t),r=e[0],n=e[1],a=e[2],o=e[3],h=e[4],l=e[5],c=e[6],u=e[7];return e[0]=r*s+h*i,e[1]=n*s+l*i,e[2]=a*s+c*i,e[3]=o*s+u*i,e[4]=h*s-r*i,e[5]=l*s-n*i,e[6]=c*s-a*i,e[7]=u*s-o*i,this},fromRotationTranslation:function(t,e){var i=t.x,s=t.y,r=t.z,n=t.w,a=i+i,o=s+s,h=r+r,l=i*a,c=i*o,u=i*h,d=s*o,f=s*h,p=r*h,g=n*a,m=n*o,x=n*h;return this.setValues(1-(d+p),c+x,u-m,0,c-x,1-(l+p),f+g,0,u+m,f-g,1-(l+d),0,e.x,e.y,e.z,1)},fromQuat:function(t){var e=t.x,i=t.y,s=t.z,r=t.w,n=e+e,a=i+i,o=s+s,h=e*n,l=e*a,c=e*o,u=i*a,d=i*o,f=s*o,p=r*n,g=r*a,m=r*o;return this.setValues(1-(u+f),l+m,c-g,0,l-m,1-(h+f),d+p,0,c+g,d-p,1-(h+u),0,0,0,0,1)},frustum:function(t,e,i,s,r,n){var a=1/(e-t),o=1/(s-i),h=1/(r-n);return this.setValues(2*r*a,0,0,0,0,2*r*o,0,0,(e+t)*a,(s+i)*o,(n+r)*h,-1,0,0,n*r*2*h,0)},perspective:function(t,e,i,s){var r=1/Math.tan(t/2),n=1/(i-s);return this.setValues(r/e,0,0,0,0,r,0,0,0,0,(s+i)*n,-1,0,0,2*s*i*n,0)},perspectiveLH:function(t,e,i,s){return this.setValues(2*i/t,0,0,0,0,2*i/e,0,0,0,0,-s/(i-s),1,0,0,i*s/(i-s),0)},ortho:function(t,e,i,s,r,n){var a=t-e,o=i-s,h=r-n;return a=0===a?a:1/a,o=0===o?o:1/o,h=0===h?h:1/h,this.setValues(-2*a,0,0,0,0,-2*o,0,0,0,0,2*h,0,(t+e)*a,(s+i)*o,(n+r)*h,1)},lookAtRH:function(t,e,i){var s=this.val;return u.subVectors(t,e),0===u.getLengthSquared()&&(u.z=1),u.normalize(),l.crossVectors(i,u),0===l.getLengthSquared()&&(1===Math.abs(i.z)?u.x+=1e-4:u.z+=1e-4,u.normalize(),l.crossVectors(i,u)),l.normalize(),c.crossVectors(u,l),s[0]=l.x,s[1]=l.y,s[2]=l.z,s[4]=c.x,s[5]=c.y,s[6]=c.z,s[8]=u.x,s[9]=u.y,s[10]=u.z,this},lookAt:function(t,e,i){var s=t.x,r=t.y,a=t.z,o=i.x,h=i.y,l=i.z,c=e.x,u=e.y,d=e.z;if(Math.abs(s-c)<n&&Math.abs(r-u)<n&&Math.abs(a-d)<n)return this.identity();var f=s-c,p=r-u,g=a-d,m=1/Math.sqrt(f*f+p*p+g*g),x=h*(g*=m)-l*(p*=m),v=l*(f*=m)-o*g,y=o*p-h*f;(m=Math.sqrt(x*x+v*v+y*y))?(x*=m=1/m,v*=m,y*=m):(x=0,v=0,y=0);var w=p*y-g*v,b=g*x-f*y,A=f*v-p*x;return(m=Math.sqrt(w*w+b*b+A*A))?(w*=m=1/m,b*=m,A*=m):(w=0,b=0,A=0),this.setValues(x,w,f,0,v,b,p,0,y,A,g,0,-(x*s+v*r+y*a),-(w*s+b*r+A*a),-(f*s+p*r+g*a),1)},yawPitchRoll:function(t,e,i){this.zero(),o.zero(),h.zero();var s=this.val,r=o.val,n=h.val,a=Math.sin(i),l=Math.cos(i);return s[10]=1,s[15]=1,s[0]=l,s[1]=a,s[4]=-a,s[5]=l,a=Math.sin(e),l=Math.cos(e),r[0]=1,r[15]=1,r[5]=l,r[10]=l,r[9]=-a,r[6]=a,a=Math.sin(t),l=Math.cos(t),n[5]=1,n[15]=1,n[0]=l,n[2]=-a,n[8]=a,n[10]=l,this.multiplyLocal(o),this.multiplyLocal(h),this},setWorldMatrix:function(t,e,i,s,r){return this.yawPitchRoll(t.y,t.x,t.z),o.scaling(i.x,i.y,i.z),h.xyz(e.x,e.y,e.z),this.multiplyLocal(o),this.multiplyLocal(h),s&&this.multiplyLocal(s),r&&this.multiplyLocal(r),this},multiplyToMat4:function(t,e){var i=this.val,s=t.val,r=i[0],n=i[1],a=i[2],o=i[3],h=i[4],l=i[5],c=i[6],u=i[7],d=i[8],f=i[9],p=i[10],g=i[11],m=i[12],x=i[13],v=i[14],y=i[15],w=s[0],b=s[1],A=s[2],M=s[3],E=s[4],S=s[5],T=s[6],I=s[7],C=s[8],R=s[9],k=s[10],F=s[11],L=s[12],P=s[13],O=s[14],Y=s[15];return e.setValues(w*r+b*h+A*d+M*m,b*n+b*l+A*f+M*x,A*a+b*c+A*p+M*v,M*o+b*u+A*g+M*y,E*r+S*h+T*d+I*m,E*n+S*l+T*f+I*x,E*a+S*c+T*p+I*v,E*o+S*u+T*g+I*y,C*r+R*h+k*d+F*m,C*n+R*l+k*f+F*x,C*a+R*c+k*p+F*v,C*o+R*u+k*g+F*y,L*r+P*h+O*d+Y*m,L*n+P*l+O*f+Y*x,L*a+P*c+O*p+Y*v,L*o+P*u+O*g+Y*y)},fromRotationXYTranslation:function(t,e,i){var s=e.x,r=e.y,n=e.z,a=Math.sin(t.x),o=Math.cos(t.x),h=Math.sin(t.y),l=Math.cos(t.y),c=s,u=r,d=n,f=-a,p=0-f*h,g=0-o*h,m=f*l,x=o*l;return i||(c=l*s+h*n,u=p*s+o*r+m*n,d=g*s+a*r+x*n),this.setValues(l,p,g,0,0,o,a,0,h,m,x,0,c,u,d,1)},getMaxScaleOnAxis:function(){var t=this.val,e=t[0]*t[0]+t[1]*t[1]+t[2]*t[2],i=t[4]*t[4]+t[5]*t[5]+t[6]*t[6],s=t[8]*t[8]+t[9]*t[9]+t[10]*t[10];return Math.sqrt(Math.max(e,i,s))}}),o=new a,h=new a,l=new r,c=new r,u=new r;t.exports=a},3733:t=>{t.exports=function(t,e,i){return Math.min(t+e,i)}},44:t=>{t.exports=function(t){var e=t.length;if(0===e)return 0;t.sort((function(t,e){return t-e}));var i=Math.floor(e/2);return e%2==0?(t[i]+t[i-1])/2:t[i]}},5385:t=>{t.exports=function(t,e,i){return Math.max(t-e,i)}},8585:t=>{t.exports=function(t,e,i,s){void 0===i&&(i=e+1);var r=(t-e)/(i-e);return r>1?void 0!==s?(r=(s-t)/(s-i))<0&&(r=0):r=1:r<0&&(r=0),r}},372:(t,e,i)=>{var s=i(7473),r=i(2149),n=i(1984),a=i(5689),o=1e-6,h=new Int8Array([1,2,0]),l=new Float32Array([0,0,0]),c=new a(1,0,0),u=new a(0,1,0),d=new a,f=new r,p=new s({initialize:function(t,e,i,s){this.onChangeCallback=n,this.set(t,e,i,s)},x:{get:function(){return this._x},set:function(t){this._x=t,this.onChangeCallback(this)}},y:{get:function(){return this._y},set:function(t){this._y=t,this.onChangeCallback(this)}},z:{get:function(){return this._z},set:function(t){this._z=t,this.onChangeCallback(this)}},w:{get:function(){return this._w},set:function(t){this._w=t,this.onChangeCallback(this)}},copy:function(t){return this.set(t)},set:function(t,e,i,s,r){return void 0===r&&(r=!0),"object"==typeof t?(this._x=t.x||0,this._y=t.y||0,this._z=t.z||0,this._w=t.w||0):(this._x=t||0,this._y=e||0,this._z=i||0,this._w=s||0),r&&this.onChangeCallback(this),this},add:function(t){return this._x+=t.x,this._y+=t.y,this._z+=t.z,this._w+=t.w,this.onChangeCallback(this),this},subtract:function(t){return this._x-=t.x,this._y-=t.y,this._z-=t.z,this._w-=t.w,this.onChangeCallback(this),this},scale:function(t){return this._x*=t,this._y*=t,this._z*=t,this._w*=t,this.onChangeCallback(this),this},length:function(){var t=this.x,e=this.y,i=this.z,s=this.w;return Math.sqrt(t*t+e*e+i*i+s*s)},lengthSq:function(){var t=this.x,e=this.y,i=this.z,s=this.w;return t*t+e*e+i*i+s*s},normalize:function(){var t=this.x,e=this.y,i=this.z,s=this.w,r=t*t+e*e+i*i+s*s;return r>0&&(r=1/Math.sqrt(r),this._x=t*r,this._y=e*r,this._z=i*r,this._w=s*r),this.onChangeCallback(this),this},dot:function(t){return this.x*t.x+this.y*t.y+this.z*t.z+this.w*t.w},lerp:function(t,e){void 0===e&&(e=0);var i=this.x,s=this.y,r=this.z,n=this.w;return this.set(i+e*(t.x-i),s+e*(t.y-s),r+e*(t.z-r),n+e*(t.w-n))},rotationTo:function(t,e){var i=t.x*e.x+t.y*e.y+t.z*e.z;return i<-.999999?(d.copy(c).cross(t).length()<o&&d.copy(u).cross(t),d.normalize(),this.setAxisAngle(d,Math.PI)):i>.999999?this.set(0,0,0,1):(d.copy(t).cross(e),this._x=d.x,this._y=d.y,this._z=d.z,this._w=1+i,this.normalize())},setAxes:function(t,e,i){var s=f.val;return s[0]=e.x,s[3]=e.y,s[6]=e.z,s[1]=i.x,s[4]=i.y,s[7]=i.z,s[2]=-t.x,s[5]=-t.y,s[8]=-t.z,this.fromMat3(f).normalize()},identity:function(){return this.set(0,0,0,1)},setAxisAngle:function(t,e){e*=.5;var i=Math.sin(e);return this.set(i*t.x,i*t.y,i*t.z,Math.cos(e))},multiply:function(t){var e=this.x,i=this.y,s=this.z,r=this.w,n=t.x,a=t.y,o=t.z,h=t.w;return this.set(e*h+r*n+i*o-s*a,i*h+r*a+s*n-e*o,s*h+r*o+e*a-i*n,r*h-e*n-i*a-s*o)},slerp:function(t,e){var i=this.x,s=this.y,r=this.z,n=this.w,a=t.x,h=t.y,l=t.z,c=t.w,u=i*a+s*h+r*l+n*c;u<0&&(u=-u,a=-a,h=-h,l=-l,c=-c);var d=1-e,f=e;if(1-u>o){var p=Math.acos(u),g=Math.sin(p);d=Math.sin((1-e)*p)/g,f=Math.sin(e*p)/g}return this.set(d*i+f*a,d*s+f*h,d*r+f*l,d*n+f*c)},invert:function(){var t=this.x,e=this.y,i=this.z,s=this.w,r=t*t+e*e+i*i+s*s,n=r?1/r:0;return this.set(-t*n,-e*n,-i*n,s*n)},conjugate:function(){return this._x=-this.x,this._y=-this.y,this._z=-this.z,this.onChangeCallback(this),this},rotateX:function(t){t*=.5;var e=this.x,i=this.y,s=this.z,r=this.w,n=Math.sin(t),a=Math.cos(t);return this.set(e*a+r*n,i*a+s*n,s*a-i*n,r*a-e*n)},rotateY:function(t){t*=.5;var e=this.x,i=this.y,s=this.z,r=this.w,n=Math.sin(t),a=Math.cos(t);return this.set(e*a-s*n,i*a+r*n,s*a+e*n,r*a-i*n)},rotateZ:function(t){t*=.5;var e=this.x,i=this.y,s=this.z,r=this.w,n=Math.sin(t),a=Math.cos(t);return this.set(e*a+i*n,i*a-e*n,s*a+r*n,r*a-s*n)},calculateW:function(){var t=this.x,e=this.y,i=this.z;return this.w=-Math.sqrt(1-t*t-e*e-i*i),this},setFromEuler:function(t,e){var i=t.x/2,s=t.y/2,r=t.z/2,n=Math.cos(i),a=Math.cos(s),o=Math.cos(r),h=Math.sin(i),l=Math.sin(s),c=Math.sin(r);switch(t.order){case"XYZ":this.set(h*a*o+n*l*c,n*l*o-h*a*c,n*a*c+h*l*o,n*a*o-h*l*c,e);break;case"YXZ":this.set(h*a*o+n*l*c,n*l*o-h*a*c,n*a*c-h*l*o,n*a*o+h*l*c,e);break;case"ZXY":this.set(h*a*o-n*l*c,n*l*o+h*a*c,n*a*c+h*l*o,n*a*o-h*l*c,e);break;case"ZYX":this.set(h*a*o-n*l*c,n*l*o+h*a*c,n*a*c-h*l*o,n*a*o+h*l*c,e);break;case"YZX":this.set(h*a*o+n*l*c,n*l*o+h*a*c,n*a*c-h*l*o,n*a*o-h*l*c,e);break;case"XZY":this.set(h*a*o-n*l*c,n*l*o-h*a*c,n*a*c+h*l*o,n*a*o+h*l*c,e)}return this},setFromRotationMatrix:function(t){var e,i=t.val,s=i[0],r=i[4],n=i[8],a=i[1],o=i[5],h=i[9],l=i[2],c=i[6],u=i[10],d=s+o+u;return d>0?(e=.5/Math.sqrt(d+1),this.set((c-h)*e,(n-l)*e,(a-r)*e,.25/e)):s>o&&s>u?(e=2*Math.sqrt(1+s-o-u),this.set(.25*e,(r+a)/e,(n+l)/e,(c-h)/e)):o>u?(e=2*Math.sqrt(1+o-s-u),this.set((r+a)/e,.25*e,(h+c)/e,(n-l)/e)):(e=2*Math.sqrt(1+u-s-o),this.set((n+l)/e,(h+c)/e,.25*e,(a-r)/e)),this},fromMat3:function(t){var e,i=t.val,s=i[0]+i[4]+i[8];if(s>0)e=Math.sqrt(s+1),this.w=.5*e,e=.5/e,this._x=(i[7]-i[5])*e,this._y=(i[2]-i[6])*e,this._z=(i[3]-i[1])*e;else{var r=0;i[4]>i[0]&&(r=1),i[8]>i[3*r+r]&&(r=2);var n=h[r],a=h[n];e=Math.sqrt(i[3*r+r]-i[3*n+n]-i[3*a+a]+1),l[r]=.5*e,e=.5/e,l[n]=(i[3*n+r]+i[3*r+n])*e,l[a]=(i[3*a+r]+i[3*r+a])*e,this._x=l[0],this._y=l[1],this._z=l[2],this._w=(i[3*a+n]-i[3*n+a])*e}return this.onChangeCallback(this),this}});t.exports=p},4208:(t,e,i)=>{var s=i(7425);t.exports=function(t){return t*s.RAD_TO_DEG}},1705:t=>{t.exports=function(t,e){void 0===e&&(e=1);var i=2*Math.random()*Math.PI;return t.x=Math.cos(i)*e,t.y=Math.sin(i)*e,t}},6650:t=>{t.exports=function(t,e){void 0===e&&(e=1);var i=2*Math.random()*Math.PI,s=2*Math.random()-1,r=Math.sqrt(1-s*s)*e;return t.x=Math.cos(i)*r,t.y=Math.sin(i)*r,t.z=s*e,t}},2037:t=>{t.exports=function(t,e){return void 0===e&&(e=1),t.x=(2*Math.random()-1)*e,t.y=(2*Math.random()-1)*e,t.z=(2*Math.random()-1)*e,t.w=(2*Math.random()-1)*e,t}},6283:t=>{t.exports=function(t,e){var i=t.x,s=t.y;return t.x=i*Math.cos(e)-s*Math.sin(e),t.y=i*Math.sin(e)+s*Math.cos(e),t}},9876:t=>{t.exports=function(t,e,i,s){var r=Math.cos(s),n=Math.sin(s),a=t.x-e,o=t.y-i;return t.x=a*r-o*n+e,t.y=a*n+o*r+i,t}},8348:t=>{t.exports=function(t,e,i,s,r){var n=s+Math.atan2(t.y-i,t.x-e);return t.x=e+r*Math.cos(n),t.y=i+r*Math.sin(n),t}},4497:t=>{t.exports=function(t,e,i,s,r){return t.x=e+r*Math.cos(s),t.y=i+r*Math.sin(s),t}},9640:(t,e,i)=>{var s=i(5689),r=i(9652),n=i(372),a=new r,o=new n,h=new s;t.exports=function(t,e,i){return o.setAxisAngle(e,i),a.fromRotationTranslation(o,h.set(0,0,0)),t.transformMat4(a)}},4078:t=>{t.exports=function(t){return t>0?Math.ceil(t):Math.floor(t)}},855:t=>{t.exports=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=10);var s=Math.pow(i,-e);return Math.round(t*s)/s}},4936:t=>{t.exports=function(t,e,i,s){void 0===e&&(e=1),void 0===i&&(i=1),void 0===s&&(s=1),s*=Math.PI/t;for(var r=[],n=[],a=0;a<t;a++)e+=(i-=e*s)*s,r[a]=i,n[a]=e;return{sin:n,cos:r,length:t}}},2733:t=>{t.exports=function(t,e,i){return t<=e?0:t>=i?1:(t=(t-e)/(i-e))*t*(3-2*t)}},278:t=>{t.exports=function(t,e,i){return(t=Math.max(0,Math.min(1,(t-e)/(i-e))))*t*t*(t*(6*t-15)+10)}},163:(t,e,i)=>{var s=i(2529);t.exports=function(t,e,i,r){void 0===r&&(r=new s);var n=0,a=0;return t>0&&t<=e*i&&(n=t>e-1?t-(a=Math.floor(t/e))*e:t),r.set(n,a)}},7556:(t,e,i)=>{var s=i(2529);t.exports=function(t,e,i,r,n,a,o,h){void 0===h&&(h=new s);var l=Math.sin(n),c=Math.cos(n),u=c*a,d=l*a,f=-l*o,p=c*o,g=1/(u*p+f*-d);return h.x=p*g*t+-f*g*e+(r*f-i*p)*g,h.y=u*g*e+-d*g*t+(-r*u+i*d)*g,h}},2529:(t,e,i)=>{var s=i(7473),r=i(12),n=new s({initialize:function(t,e){this.x=0,this.y=0,"object"==typeof t?(this.x=t.x||0,this.y=t.y||0):(void 0===e&&(e=t),this.x=t||0,this.y=e||0)},clone:function(){return new n(this.x,this.y)},copy:function(t){return this.x=t.x||0,this.y=t.y||0,this},setFromObject:function(t){return this.x=t.x||0,this.y=t.y||0,this},set:function(t,e){return void 0===e&&(e=t),this.x=t,this.y=e,this},setTo:function(t,e){return this.set(t,e)},setToPolar:function(t,e){return null==e&&(e=1),this.x=Math.cos(t)*e,this.y=Math.sin(t)*e,this},equals:function(t){return this.x===t.x&&this.y===t.y},fuzzyEquals:function(t,e){return r(this.x,t.x,e)&&r(this.y,t.y,e)},angle:function(){var t=Math.atan2(this.y,this.x);return t<0&&(t+=2*Math.PI),t},setAngle:function(t){return this.setToPolar(t,this.length())},add:function(t){return this.x+=t.x,this.y+=t.y,this},subtract:function(t){return this.x-=t.x,this.y-=t.y,this},multiply:function(t){return this.x*=t.x,this.y*=t.y,this},scale:function(t){return isFinite(t)?(this.x*=t,this.y*=t):(this.x=0,this.y=0),this},divide:function(t){return this.x/=t.x,this.y/=t.y,this},negate:function(){return this.x=-this.x,this.y=-this.y,this},distance:function(t){var e=t.x-this.x,i=t.y-this.y;return Math.sqrt(e*e+i*i)},distanceSq:function(t){var e=t.x-this.x,i=t.y-this.y;return e*e+i*i},length:function(){var t=this.x,e=this.y;return Math.sqrt(t*t+e*e)},setLength:function(t){return this.normalize().scale(t)},lengthSq:function(){var t=this.x,e=this.y;return t*t+e*e},normalize:function(){var t=this.x,e=this.y,i=t*t+e*e;return i>0&&(i=1/Math.sqrt(i),this.x=t*i,this.y=e*i),this},normalizeRightHand:function(){var t=this.x;return this.x=-1*this.y,this.y=t,this},normalizeLeftHand:function(){var t=this.x;return this.x=this.y,this.y=-1*t,this},dot:function(t){return this.x*t.x+this.y*t.y},cross:function(t){return this.x*t.y-this.y*t.x},lerp:function(t,e){void 0===e&&(e=0);var i=this.x,s=this.y;return this.x=i+e*(t.x-i),this.y=s+e*(t.y-s),this},transformMat3:function(t){var e=this.x,i=this.y,s=t.val;return this.x=s[0]*e+s[3]*i+s[6],this.y=s[1]*e+s[4]*i+s[7],this},transformMat4:function(t){var e=this.x,i=this.y,s=t.val;return this.x=s[0]*e+s[4]*i+s[12],this.y=s[1]*e+s[5]*i+s[13],this},reset:function(){return this.x=0,this.y=0,this},limit:function(t){var e=this.length();return e&&e>t&&this.scale(t/e),this},reflect:function(t){return t=t.clone().normalize(),this.subtract(t.scale(2*this.dot(t)))},mirror:function(t){return this.reflect(t).negate()},rotate:function(t){var e=Math.cos(t),i=Math.sin(t);return this.set(e*this.x-i*this.y,i*this.x+e*this.y)},project:function(t){var e=this.dot(t)/t.dot(t);return this.copy(t).scale(e)}});n.ZERO=new n,n.RIGHT=new n(1,0),n.LEFT=new n(-1,0),n.UP=new n(0,-1),n.DOWN=new n(0,1),n.ONE=new n(1,1),t.exports=n},5689:(t,e,i)=>{var s=new(i(7473))({initialize:function(t,e,i){this.x=0,this.y=0,this.z=0,"object"==typeof t?(this.x=t.x||0,this.y=t.y||0,this.z=t.z||0):(this.x=t||0,this.y=e||0,this.z=i||0)},up:function(){return this.x=0,this.y=1,this.z=0,this},min:function(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this.z=Math.min(this.z,t.z),this},max:function(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this.z=Math.max(this.z,t.z),this},clone:function(){return new s(this.x,this.y,this.z)},addVectors:function(t,e){return this.x=t.x+e.x,this.y=t.y+e.y,this.z=t.z+e.z,this},crossVectors:function(t,e){var i=t.x,s=t.y,r=t.z,n=e.x,a=e.y,o=e.z;return this.x=s*o-r*a,this.y=r*n-i*o,this.z=i*a-s*n,this},equals:function(t){return this.x===t.x&&this.y===t.y&&this.z===t.z},copy:function(t){return this.x=t.x,this.y=t.y,this.z=t.z||0,this},set:function(t,e,i){return"object"==typeof t?(this.x=t.x||0,this.y=t.y||0,this.z=t.z||0):(this.x=t||0,this.y=e||0,this.z=i||0),this},setFromMatrixPosition:function(t){return this.fromArray(t.val,12)},setFromMatrixColumn:function(t,e){return this.fromArray(t.val,4*e)},fromArray:function(t,e){return void 0===e&&(e=0),this.x=t[e],this.y=t[e+1],this.z=t[e+2],this},add:function(t){return this.x+=t.x,this.y+=t.y,this.z+=t.z||0,this},addScalar:function(t){return this.x+=t,this.y+=t,this.z+=t,this},addScale:function(t,e){return this.x+=t.x*e,this.y+=t.y*e,this.z+=t.z*e||0,this},subtract:function(t){return this.x-=t.x,this.y-=t.y,this.z-=t.z||0,this},multiply:function(t){return this.x*=t.x,this.y*=t.y,this.z*=t.z||1,this},scale:function(t){return isFinite(t)?(this.x*=t,this.y*=t,this.z*=t):(this.x=0,this.y=0,this.z=0),this},divide:function(t){return this.x/=t.x,this.y/=t.y,this.z/=t.z||1,this},negate:function(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this},distance:function(t){var e=t.x-this.x,i=t.y-this.y,s=t.z-this.z||0;return Math.sqrt(e*e+i*i+s*s)},distanceSq:function(t){var e=t.x-this.x,i=t.y-this.y,s=t.z-this.z||0;return e*e+i*i+s*s},length:function(){var t=this.x,e=this.y,i=this.z;return Math.sqrt(t*t+e*e+i*i)},lengthSq:function(){var t=this.x,e=this.y,i=this.z;return t*t+e*e+i*i},normalize:function(){var t=this.x,e=this.y,i=this.z,s=t*t+e*e+i*i;return s>0&&(s=1/Math.sqrt(s),this.x=t*s,this.y=e*s,this.z=i*s),this},dot:function(t){return this.x*t.x+this.y*t.y+this.z*t.z},cross:function(t){var e=this.x,i=this.y,s=this.z,r=t.x,n=t.y,a=t.z;return this.x=i*a-s*n,this.y=s*r-e*a,this.z=e*n-i*r,this},lerp:function(t,e){void 0===e&&(e=0);var i=this.x,s=this.y,r=this.z;return this.x=i+e*(t.x-i),this.y=s+e*(t.y-s),this.z=r+e*(t.z-r),this},applyMatrix3:function(t){var e=this.x,i=this.y,s=this.z,r=t.val;return this.x=r[0]*e+r[3]*i+r[6]*s,this.y=r[1]*e+r[4]*i+r[7]*s,this.z=r[2]*e+r[5]*i+r[8]*s,this},applyMatrix4:function(t){var e=this.x,i=this.y,s=this.z,r=t.val,n=1/(r[3]*e+r[7]*i+r[11]*s+r[15]);return this.x=(r[0]*e+r[4]*i+r[8]*s+r[12])*n,this.y=(r[1]*e+r[5]*i+r[9]*s+r[13])*n,this.z=(r[2]*e+r[6]*i+r[10]*s+r[14])*n,this},transformMat3:function(t){var e=this.x,i=this.y,s=this.z,r=t.val;return this.x=e*r[0]+i*r[3]+s*r[6],this.y=e*r[1]+i*r[4]+s*r[7],this.z=e*r[2]+i*r[5]+s*r[8],this},transformMat4:function(t){var e=this.x,i=this.y,s=this.z,r=t.val;return this.x=r[0]*e+r[4]*i+r[8]*s+r[12],this.y=r[1]*e+r[5]*i+r[9]*s+r[13],this.z=r[2]*e+r[6]*i+r[10]*s+r[14],this},transformCoordinates:function(t){var e=this.x,i=this.y,s=this.z,r=t.val,n=e*r[0]+i*r[4]+s*r[8]+r[12],a=e*r[1]+i*r[5]+s*r[9]+r[13],o=e*r[2]+i*r[6]+s*r[10]+r[14],h=e*r[3]+i*r[7]+s*r[11]+r[15];return this.x=n/h,this.y=a/h,this.z=o/h,this},transformQuat:function(t){var e=this.x,i=this.y,s=this.z,r=t.x,n=t.y,a=t.z,o=t.w,h=o*e+n*s-a*i,l=o*i+a*e-r*s,c=o*s+r*i-n*e,u=-r*e-n*i-a*s;return this.x=h*o+u*-r+l*-a-c*-n,this.y=l*o+u*-n+c*-r-h*-a,this.z=c*o+u*-a+h*-n-l*-r,this},project:function(t){var e=this.x,i=this.y,s=this.z,r=t.val,n=r[0],a=r[1],o=r[2],h=r[3],l=r[4],c=r[5],u=r[6],d=r[7],f=r[8],p=r[9],g=r[10],m=r[11],x=r[12],v=r[13],y=r[14],w=1/(e*h+i*d+s*m+r[15]);return this.x=(e*n+i*l+s*f+x)*w,this.y=(e*a+i*c+s*p+v)*w,this.z=(e*o+i*u+s*g+y)*w,this},projectViewMatrix:function(t,e){return this.applyMatrix4(t).applyMatrix4(e)},unprojectViewMatrix:function(t,e){return this.applyMatrix4(t).applyMatrix4(e)},unproject:function(t,e){var i=t.x,s=t.y,r=t.z,n=t.w,a=this.x-i,o=n-this.y-1-s,h=this.z;return this.x=2*a/r-1,this.y=2*o/n-1,this.z=2*h-1,this.project(e)},reset:function(){return this.x=0,this.y=0,this.z=0,this}});s.ZERO=new s,s.RIGHT=new s(1,0,0),s.LEFT=new s(-1,0,0),s.UP=new s(0,-1,0),s.DOWN=new s(0,1,0),s.FORWARD=new s(0,0,1),s.BACK=new s(0,0,-1),s.ONE=new s(1,1,1),t.exports=s},9279:(t,e,i)=>{var s=new(i(7473))({initialize:function(t,e,i,s){this.x=0,this.y=0,this.z=0,this.w=0,"object"==typeof t?(this.x=t.x||0,this.y=t.y||0,this.z=t.z||0,this.w=t.w||0):(this.x=t||0,this.y=e||0,this.z=i||0,this.w=s||0)},clone:function(){return new s(this.x,this.y,this.z,this.w)},copy:function(t){return this.x=t.x,this.y=t.y,this.z=t.z||0,this.w=t.w||0,this},equals:function(t){return this.x===t.x&&this.y===t.y&&this.z===t.z&&this.w===t.w},set:function(t,e,i,s){return"object"==typeof t?(this.x=t.x||0,this.y=t.y||0,this.z=t.z||0,this.w=t.w||0):(this.x=t||0,this.y=e||0,this.z=i||0,this.w=s||0),this},add:function(t){return this.x+=t.x,this.y+=t.y,this.z+=t.z||0,this.w+=t.w||0,this},subtract:function(t){return this.x-=t.x,this.y-=t.y,this.z-=t.z||0,this.w-=t.w||0,this},scale:function(t){return this.x*=t,this.y*=t,this.z*=t,this.w*=t,this},length:function(){var t=this.x,e=this.y,i=this.z,s=this.w;return Math.sqrt(t*t+e*e+i*i+s*s)},lengthSq:function(){var t=this.x,e=this.y,i=this.z,s=this.w;return t*t+e*e+i*i+s*s},normalize:function(){var t=this.x,e=this.y,i=this.z,s=this.w,r=t*t+e*e+i*i+s*s;return r>0&&(r=1/Math.sqrt(r),this.x=t*r,this.y=e*r,this.z=i*r,this.w=s*r),this},dot:function(t){return this.x*t.x+this.y*t.y+this.z*t.z+this.w*t.w},lerp:function(t,e){void 0===e&&(e=0);var i=this.x,s=this.y,r=this.z,n=this.w;return this.x=i+e*(t.x-i),this.y=s+e*(t.y-s),this.z=r+e*(t.z-r),this.w=n+e*(t.w-n),this},multiply:function(t){return this.x*=t.x,this.y*=t.y,this.z*=t.z||1,this.w*=t.w||1,this},divide:function(t){return this.x/=t.x,this.y/=t.y,this.z/=t.z||1,this.w/=t.w||1,this},distance:function(t){var e=t.x-this.x,i=t.y-this.y,s=t.z-this.z||0,r=t.w-this.w||0;return Math.sqrt(e*e+i*i+s*s+r*r)},distanceSq:function(t){var e=t.x-this.x,i=t.y-this.y,s=t.z-this.z||0,r=t.w-this.w||0;return e*e+i*i+s*s+r*r},negate:function(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this.w=-this.w,this},transformMat4:function(t){var e=this.x,i=this.y,s=this.z,r=this.w,n=t.val;return this.x=n[0]*e+n[4]*i+n[8]*s+n[12]*r,this.y=n[1]*e+n[5]*i+n[9]*s+n[13]*r,this.z=n[2]*e+n[6]*i+n[10]*s+n[14]*r,this.w=n[3]*e+n[7]*i+n[11]*s+n[15]*r,this},transformQuat:function(t){var e=this.x,i=this.y,s=this.z,r=t.x,n=t.y,a=t.z,o=t.w,h=o*e+n*s-a*i,l=o*i+a*e-r*s,c=o*s+r*i-n*e,u=-r*e-n*i-a*s;return this.x=h*o+u*-r+l*-a-c*-n,this.y=l*o+u*-n+c*-r-h*-a,this.z=c*o+u*-a+h*-n-l*-r,this},reset:function(){return this.x=0,this.y=0,this.z=0,this.w=0,this}});s.prototype.sub=s.prototype.subtract,s.prototype.mul=s.prototype.multiply,s.prototype.div=s.prototype.divide,s.prototype.dist=s.prototype.distance,s.prototype.distSq=s.prototype.distanceSq,s.prototype.len=s.prototype.length,s.prototype.lenSq=s.prototype.lengthSq,t.exports=s},4119:t=>{t.exports=function(t,e,i){return Math.abs(t-e)<=i}},8445:t=>{t.exports=function(t,e,i){var s=i-e;return e+((t-e)%s+s)%s}},6412:t=>{t.exports=function(t,e,i,s){return Math.atan2(s-e,i-t)}},760:t=>{t.exports=function(t,e){return Math.atan2(e.y-t.y,e.x-t.x)}},6909:t=>{t.exports=function(t,e){return Math.atan2(e.x-t.x,e.y-t.y)}},6947:t=>{t.exports=function(t,e,i,s){return Math.atan2(i-t,s-e)}},3426:(t,e,i)=>{var s=i(7425);t.exports=function(t){return t>Math.PI&&(t-=s.PI2),Math.abs(((t+s.TAU)%s.PI2-s.PI2)%s.PI2)}},6906:t=>{t.exports=function(t){return(t%=2*Math.PI)>=0?t:t+2*Math.PI}},3270:(t,e,i)=>{var s=i(104);t.exports=function(){return s(-Math.PI,Math.PI)}},2748:(t,e,i)=>{var s=i(104);t.exports=function(){return s(-180,180)}},936:(t,e,i)=>{var s=i(6906);t.exports=function(t){return s(t+Math.PI)}},1935:(t,e,i)=>{var s=i(7425);t.exports=function(t,e,i){return void 0===i&&(i=.05),t===e||(Math.abs(e-t)<=i||Math.abs(e-t)>=s.PI2-i?t=e:(Math.abs(e-t)>Math.PI&&(e<t?e+=s.PI2:e-=s.PI2),e>t?t+=i:e<t&&(t-=i))),t}},5393:t=>{t.exports=function(t,e){var i=e-t;return 0===i?0:i-360*Math.floor((i- -180)/360)}},3692:(t,e,i)=>{var s=i(8445);t.exports=function(t){return s(t,-Math.PI,Math.PI)}},2820:(t,e,i)=>{var s=i(8445);t.exports=function(t){return s(t,-180,180)}},1833:(t,e,i)=>{t.exports={Between:i(6412),BetweenPoints:i(760),BetweenPointsY:i(6909),BetweenY:i(6947),CounterClockwise:i(3426),Normalize:i(6906),Random:i(3270),RandomDegrees:i(2748),Reverse:i(936),RotateTo:i(1935),ShortestBetween:i(5393),Wrap:i(3692),WrapDegrees:i(2820)}},7425:t=>{var e={PI2:2*Math.PI,TAU:.5*Math.PI,EPSILON:1e-6,DEG_TO_RAD:Math.PI/180,RAD_TO_DEG:180/Math.PI,RND:null,MIN_SAFE_INTEGER:Number.MIN_SAFE_INTEGER||-9007199254740991,MAX_SAFE_INTEGER:Number.MAX_SAFE_INTEGER||9007199254740991};t.exports=e},1518:t=>{t.exports=function(t,e,i,s){var r=t-i,n=e-s;return Math.sqrt(r*r+n*n)}},5372:t=>{t.exports=function(t,e){var i=t.x-e.x,s=t.y-e.y;return Math.sqrt(i*i+s*s)}},4430:t=>{t.exports=function(t,e){var i=t.x-e.x,s=t.y-e.y;return i*i+s*s}},4361:t=>{t.exports=function(t,e,i,s){return Math.max(Math.abs(t-i),Math.abs(e-s))}},7798:t=>{t.exports=function(t,e,i,s,r){return void 0===r&&(r=2),Math.sqrt(Math.pow(i-t,r)+Math.pow(s-e,r))}},8290:t=>{t.exports=function(t,e,i,s){return Math.abs(t-i)+Math.abs(e-s)}},3788:t=>{t.exports=function(t,e,i,s){var r=t-i,n=e-s;return r*r+n*n}},6338:(t,e,i)=>{t.exports={Between:i(1518),BetweenPoints:i(5372),BetweenPointsSquared:i(4430),Chebyshev:i(4361),Power:i(7798),Snake:i(8290),Squared:i(3788)}},5751:t=>{t.exports=function(t,e){return void 0===e&&(e=1.70158),t*t*((e+1)*t-e)}},6203:t=>{t.exports=function(t,e){void 0===e&&(e=1.70158);var i=1.525*e;return(t*=2)<1?t*t*((i+1)*t-i)*.5:.5*((t-=2)*t*((i+1)*t+i)+2)}},9103:t=>{t.exports=function(t,e){return void 0===e&&(e=1.70158),--t*t*((e+1)*t+e)+1}},4938:(t,e,i)=>{t.exports={In:i(5751),Out:i(9103),InOut:i(6203)}},8677:t=>{t.exports=function(t){return(t=1-t)<1/2.75?1-7.5625*t*t:t<2/2.75?1-(7.5625*(t-=1.5/2.75)*t+.75):t<2.5/2.75?1-(7.5625*(t-=2.25/2.75)*t+.9375):1-(7.5625*(t-=2.625/2.75)*t+.984375)}},4649:t=>{t.exports=function(t){var e=!1;return t<.5?(t=1-2*t,e=!0):t=2*t-1,t<1/2.75?t*=7.5625*t:t=t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375,e?.5*(1-t):.5*t+.5}},504:t=>{t.exports=function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375}},8872:(t,e,i)=>{t.exports={In:i(8677),Out:i(504),InOut:i(4649)}},3170:t=>{t.exports=function(t){return 1-Math.sqrt(1-t*t)}},2627:t=>{t.exports=function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)}},1349:t=>{t.exports=function(t){return Math.sqrt(1- --t*t)}},5006:(t,e,i)=>{t.exports={In:i(3170),Out:i(1349),InOut:i(2627)}},6046:t=>{t.exports=function(t){return t*t*t}},9531:t=>{t.exports=function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)}},4836:t=>{t.exports=function(t){return--t*t*t+1}},875:(t,e,i)=>{t.exports={In:i(6046),Out:i(4836),InOut:i(9531)}},7619:t=>{t.exports=function(t,e,i){if(void 0===e&&(e=.1),void 0===i&&(i=.1),0===t)return 0;if(1===t)return 1;var s=i/4;return e<1?e=1:s=i*Math.asin(1/e)/(2*Math.PI),-e*Math.pow(2,10*(t-=1))*Math.sin((t-s)*(2*Math.PI)/i)}},7437:t=>{t.exports=function(t,e,i){if(void 0===e&&(e=.1),void 0===i&&(i=.1),0===t)return 0;if(1===t)return 1;var s=i/4;return e<1?e=1:s=i*Math.asin(1/e)/(2*Math.PI),(t*=2)<1?e*Math.pow(2,10*(t-=1))*Math.sin((t-s)*(2*Math.PI)/i)*-.5:e*Math.pow(2,-10*(t-=1))*Math.sin((t-s)*(2*Math.PI)/i)*.5+1}},8119:t=>{t.exports=function(t,e,i){if(void 0===e&&(e=.1),void 0===i&&(i=.1),0===t)return 0;if(1===t)return 1;var s=i/4;return e<1?e=1:s=i*Math.asin(1/e)/(2*Math.PI),e*Math.pow(2,-10*t)*Math.sin((t-s)*(2*Math.PI)/i)+1}},2884:(t,e,i)=>{t.exports={In:i(7619),Out:i(8119),InOut:i(7437)}},5456:t=>{t.exports=function(t){return Math.pow(2,10*(t-1))-.001}},3461:t=>{t.exports=function(t){return(t*=2)<1?.5*Math.pow(2,10*(t-1)):.5*(2-Math.pow(2,-10*(t-1)))}},2711:t=>{t.exports=function(t){return 1-Math.pow(2,-10*t)}},6287:(t,e,i)=>{t.exports={In:i(5456),Out:i(2711),InOut:i(3461)}},8613:(t,e,i)=>{t.exports={Back:i(4938),Bounce:i(8872),Circular:i(5006),Cubic:i(875),Elastic:i(2884),Expo:i(6287),Linear:i(4233),Quadratic:i(6341),Quartic:i(762),Quintic:i(345),Sine:i(8698),Stepped:i(7051)}},744:t=>{t.exports=function(t){return t}},4233:(t,e,i)=>{t.exports=i(744)},9810:t=>{t.exports=function(t){return t*t}},8163:t=>{t.exports=function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)}},6123:t=>{t.exports=function(t){return t*(2-t)}},6341:(t,e,i)=>{t.exports={In:i(9810),Out:i(6123),InOut:i(8163)}},7337:t=>{t.exports=function(t){return t*t*t*t}},4878:t=>{t.exports=function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)}},9012:t=>{t.exports=function(t){return 1- --t*t*t*t}},762:(t,e,i)=>{t.exports={In:i(7337),Out:i(9012),InOut:i(4878)}},303:t=>{t.exports=function(t){return t*t*t*t*t}},553:t=>{t.exports=function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)}},1632:t=>{t.exports=function(t){return--t*t*t*t*t+1}},345:(t,e,i)=>{t.exports={In:i(303),Out:i(1632),InOut:i(553)}},8455:t=>{t.exports=function(t){return 0===t?0:1===t?1:1-Math.cos(t*Math.PI/2)}},1844:t=>{t.exports=function(t){return 0===t?0:1===t?1:.5*(1-Math.cos(Math.PI*t))}},990:t=>{t.exports=function(t){return 0===t?0:1===t?1:Math.sin(t*Math.PI/2)}},8698:(t,e,i)=>{t.exports={In:i(8455),Out:i(990),InOut:i(1844)}},6745:t=>{t.exports=function(t,e){return void 0===e&&(e=1),t<=0?0:t>=1?1:1/e*(1+(e*t|0))}},7051:(t,e,i)=>{t.exports=i(6745)},3158:t=>{t.exports=function(t,e){return void 0===e&&(e=1e-4),Math.ceil(t-e)}},12:t=>{t.exports=function(t,e,i){return void 0===i&&(i=1e-4),Math.abs(t-e)<i}},1326:t=>{t.exports=function(t,e){return void 0===e&&(e=1e-4),Math.floor(t+e)}},7373:t=>{t.exports=function(t,e,i){return void 0===i&&(i=1e-4),t>e-i}},2622:t=>{t.exports=function(t,e,i){return void 0===i&&(i=1e-4),t<e+i}},7927:(t,e,i)=>{t.exports={Ceil:i(3158),Equal:i(12),Floor:i(1326),GreaterThan:i(7373),LessThan:i(2622)}},4675:(t,e,i)=>{var s=i(7425),r=i(1030),n={Angle:i(1833),Distance:i(6338),Easing:i(8613),Fuzzy:i(7927),Interpolation:i(2140),Pow2:i(7897),Snap:i(3943),RandomDataGenerator:i(6957),Average:i(3136),Bernstein:i(785),Between:i(7025),CatmullRom:i(48),CeilTo:i(5035),Clamp:i(2915),DegToRad:i(7149),Difference:i(2975),Euler:i(2107),Factorial:i(3916),FloatBetween:i(104),FloorTo:i(4941),FromPercent:i(1555),GetSpeed:i(5005),IsEven:i(3702),IsEvenStrict:i(8820),Linear:i(1743),LinearXY:i(3416),MaxAdd:i(3733),Median:i(44),MinSub:i(5385),Percent:i(8585),RadToDeg:i(4208),RandomXY:i(1705),RandomXYZ:i(6650),RandomXYZW:i(2037),Rotate:i(6283),RotateAround:i(9876),RotateAroundDistance:i(8348),RotateTo:i(4497),RoundAwayFromZero:i(4078),RoundTo:i(855),SinCosTableGenerator:i(4936),SmootherStep:i(278),SmoothStep:i(2733),ToXY:i(163),TransformXY:i(7556),Within:i(4119),Wrap:i(8445),Vector2:i(2529),Vector3:i(5689),Vector4:i(9279),Matrix3:i(2149),Matrix4:i(9652),Quaternion:i(372),RotateVec3:i(9640)};n=r(!1,n,s),t.exports=n},1640:(t,e,i)=>{var s=i(785);t.exports=function(t,e){for(var i=0,r=t.length-1,n=0;n<=r;n++)i+=Math.pow(1-e,r-n)*Math.pow(e,n)*t[n]*s(r,n);return i}},6105:(t,e,i)=>{var s=i(48);t.exports=function(t,e){var i=t.length-1,r=i*e,n=Math.floor(r);return t[0]===t[i]?(e<0&&(n=Math.floor(r=i*(1+e))),s(r-n,t[(n-1+i)%i],t[n],t[(n+1)%i],t[(n+2)%i])):e<0?t[0]-(s(-r,t[0],t[0],t[1],t[1])-t[0]):e>1?t[i]-(s(r-i,t[i],t[i],t[i-1],t[i-1])-t[i]):s(r-n,t[n?n-1:0],t[n],t[i<n+1?i:n+1],t[i<n+2?i:n+2])}},4002:t=>{t.exports=function(t,e,i,s,r){return function(t,e){var i=1-t;return i*i*i*e}(t,e)+function(t,e){var i=1-t;return 3*i*i*t*e}(t,i)+function(t,e){return 3*(1-t)*t*t*e}(t,s)+function(t,e){return t*t*t*e}(t,r)}},6765:(t,e,i)=>{var s=i(1743);t.exports=function(t,e){var i=t.length-1,r=i*e,n=Math.floor(r);return e<0?s(t[0],t[1],r):e>1?s(t[i],t[i-1],i-r):s(t[n],t[n+1>i?i:n+1],r-n)}},6388:t=>{t.exports=function(t,e,i,s){return function(t,e){var i=1-t;return i*i*e}(t,e)+function(t,e){return 2*(1-t)*t*e}(t,i)+function(t,e){return t*t*e}(t,s)}},5735:(t,e,i)=>{var s=i(2733);t.exports=function(t,e,i){return e+(i-e)*s(t,0,1)}},8705:(t,e,i)=>{var s=i(278);t.exports=function(t,e,i){return e+(i-e)*s(t,0,1)}},2140:(t,e,i)=>{t.exports={Bezier:i(1640),CatmullRom:i(6105),CubicBezier:i(4002),Linear:i(6765),QuadraticBezier:i(6388),SmoothStep:i(5735),SmootherStep:i(8705)}},5443:t=>{t.exports=function(t){var e=Math.log(t)/.6931471805599453;return 1<<Math.ceil(e)}},725:t=>{t.exports=function(t,e){return t>0&&0==(t&t-1)&&e>0&&0==(e&e-1)}},167:t=>{t.exports=function(t){return t>0&&0==(t&t-1)}},7897:(t,e,i)=>{t.exports={GetNext:i(5443),IsSize:i(725),IsValue:i(167)}},6957:(t,e,i)=>{var s=new(i(7473))({initialize:function(t){void 0===t&&(t=[(Date.now()*Math.random()).toString()]),this.c=1,this.s0=0,this.s1=0,this.s2=0,this.n=0,this.signs=[-1,1],t&&this.init(t)},rnd:function(){var t=2091639*this.s0+2.3283064365386963e-10*this.c;return this.c=0|t,this.s0=this.s1,this.s1=this.s2,this.s2=t-this.c,this.s2},hash:function(t){var e,i=this.n;t=t.toString();for(var s=0;s<t.length;s++)e=.02519603282416938*(i+=t.charCodeAt(s)),e-=i=e>>>0,i=(e*=i)>>>0,i+=4294967296*(e-=i);return this.n=i,2.3283064365386963e-10*(i>>>0)},init:function(t){"string"==typeof t?this.state(t):this.sow(t)},sow:function(t){if(this.n=4022871197,this.s0=this.hash(" "),this.s1=this.hash(" "),this.s2=this.hash(" "),this.c=1,t)for(var e=0;e<t.length&&null!=t[e];e++){var i=t[e];this.s0-=this.hash(i),this.s0+=~~(this.s0<0),this.s1-=this.hash(i),this.s1+=~~(this.s1<0),this.s2-=this.hash(i),this.s2+=~~(this.s2<0)}},integer:function(){return 4294967296*this.rnd()},frac:function(){return this.rnd()+11102230246251565e-32*(2097152*this.rnd()|0)},real:function(){return this.integer()+this.frac()},integerInRange:function(t,e){return Math.floor(this.realInRange(0,e-t+1)+t)},between:function(t,e){return Math.floor(this.realInRange(0,e-t+1)+t)},realInRange:function(t,e){return this.frac()*(e-t)+t},normal:function(){return 1-2*this.frac()},uuid:function(){var t="",e="";for(e=t="";t++<36;e+=~t%5|3*t&4?(15^t?8^this.frac()*(20^t?16:4):4).toString(16):"-");return e},pick:function(t){return t[this.integerInRange(0,t.length-1)]},sign:function(){return this.pick(this.signs)},weightedPick:function(t){return t[~~(Math.pow(this.frac(),2)*t.length+.5)]},timestamp:function(t,e){return this.realInRange(t||9466848e5,e||1577862e6)},angle:function(){return this.integerInRange(-180,180)},rotation:function(){return this.realInRange(-3.1415926,3.1415926)},state:function(t){return"string"==typeof t&&t.match(/^!rnd/)&&(t=t.split(","),this.c=parseFloat(t[1]),this.s0=parseFloat(t[2]),this.s1=parseFloat(t[3]),this.s2=parseFloat(t[4])),["!rnd",this.c,this.s0,this.s1,this.s2].join(",")},shuffle:function(t){for(var e=t.length-1;e>0;e--){var i=Math.floor(this.frac()*(e+1)),s=t[i];t[i]=t[e],t[e]=s}return t}});t.exports=s},5659:t=>{t.exports=function(t,e,i,s){return void 0===i&&(i=0),0===e?t:(t-=i,t=e*Math.ceil(t/e),s?(i+t)/e:i+t)}},5461:t=>{t.exports=function(t,e,i,s){return void 0===i&&(i=0),0===e?t:(t-=i,t=e*Math.floor(t/e),s?(i+t)/e:i+t)}},5131:t=>{t.exports=function(t,e,i,s){return void 0===i&&(i=0),0===e?t:(t-=i,t=e*Math.round(t/e),s?(i+t)/e:i+t)}},3943:(t,e,i)=>{t.exports={Ceil:i(5659),Floor:i(5461),To:i(5131)}},8666:(t,e,i)=>{var s=new(i(7473))({initialize:function(t){this.pluginManager=t,this.game=t.game},init:function(){},start:function(){},stop:function(){},destroy:function(){this.pluginManager=null,this.game=null,this.scene=null,this.systems=null}});t.exports=s},8456:t=>{var e={},i={},s={register:function(t,i,s,r){void 0===r&&(r=!1),e[t]={plugin:i,mapping:s,custom:r}},registerCustom:function(t,e,s,r){i[t]={plugin:e,mapping:s,data:r}},hasCore:function(t){return e.hasOwnProperty(t)},hasCustom:function(t){return i.hasOwnProperty(t)},getCore:function(t){return e[t]},getCustom:function(t){return i[t]},getCustomClass:function(t){return i.hasOwnProperty(t)?i[t].plugin:null},remove:function(t){e.hasOwnProperty(t)&&delete e[t]},removeCustom:function(t){i.hasOwnProperty(t)&&delete i[t]},destroyCorePlugins:function(){for(var t in e)e.hasOwnProperty(t)&&delete e[t]},destroyCustomPlugins:function(){for(var t in i)i.hasOwnProperty(t)&&delete i[t]}};t.exports=s},5722:(t,e,i)=>{var s=i(8666),r=i(7473),n=i(204),a=new r({Extends:s,initialize:function(t,e,i){s.call(this,e),this.scene=t,this.systems=t.sys,this.pluginKey=i,t.sys.events.once(n.BOOT,this.boot,this)},boot:function(){},destroy:function(){this.pluginManager=null,this.game=null,this.scene=null,this.systems=null}});t.exports=a},8351:t=>{t.exports={SKIP_CHECK:-1,NORMAL:0,ADD:1,MULTIPLY:2,SCREEN:3,OVERLAY:4,DARKEN:5,LIGHTEN:6,COLOR_DODGE:7,COLOR_BURN:8,HARD_LIGHT:9,SOFT_LIGHT:10,DIFFERENCE:11,EXCLUSION:12,HUE:13,SATURATION:14,COLOR:15,LUMINOSITY:16,ERASE:17,SOURCE_IN:18,SOURCE_OUT:19,SOURCE_ATOP:20,DESTINATION_OVER:21,DESTINATION_IN:22,DESTINATION_OUT:23,DESTINATION_ATOP:24,LIGHTER:25,COPY:26,XOR:27}},8196:t=>{t.exports={DEFAULT:0,LINEAR:0,NEAREST:1}},3527:t=>{t.exports="resize"},8618:t=>{t.exports="addedtoscene"},4328:t=>{t.exports="boot"},6099:t=>{t.exports="create"},7645:t=>{t.exports="destroy"},2710:t=>{t.exports="pause"},2547:t=>{t.exports="postupdate"},8577:t=>{t.exports="prerender"},8197:t=>{t.exports="preupdate"},8997:t=>{t.exports="ready"},7604:t=>{t.exports="removedfromscene"},8999:t=>{t.exports="render"},9742:t=>{t.exports="resume"},3667:t=>{t.exports="shutdown"},3468:t=>{t.exports="sleep"},7840:t=>{t.exports="start"},9896:t=>{t.exports="transitioncomplete"},5103:t=>{t.exports="transitioninit"},3162:t=>{t.exports="transitionout"},7841:t=>{t.exports="transitionstart"},6454:t=>{t.exports="transitionwake"},6536:t=>{t.exports="update"},3875:t=>{t.exports="wake"},204:(t,e,i)=>{t.exports={ADDED_TO_SCENE:i(8618),BOOT:i(4328),CREATE:i(6099),DESTROY:i(7645),PAUSE:i(2710),POST_UPDATE:i(2547),PRE_RENDER:i(8577),PRE_UPDATE:i(8197),READY:i(8997),REMOVED_FROM_SCENE:i(7604),RENDER:i(8999),RESUME:i(9742),SHUTDOWN:i(3667),SLEEP:i(3468),START:i(7840),TRANSITION_COMPLETE:i(9896),TRANSITION_INIT:i(5103),TRANSITION_OUT:i(3162),TRANSITION_START:i(7841),TRANSITION_WAKE:i(6454),UPDATE:i(6536),WAKE:i(3875)}},2362:(t,e,i)=>{var s=i(7473),r=i(2915),n=i(1030),a=new s({initialize:function(t,e,i,s,r,n,a){this.texture=t,this.name=e,this.source=t.source[i],this.sourceIndex=i,this.glTexture=this.source.glTexture,this.cutX,this.cutY,this.cutWidth,this.cutHeight,this.x=0,this.y=0,this.width,this.height,this.halfWidth,this.halfHeight,this.centerX,this.centerY,this.pivotX=0,this.pivotY=0,this.customPivot=!1,this.rotated=!1,this.autoRound=-1,this.customData={},this.u0=0,this.v0=0,this.u1=0,this.v1=0,this.data={cut:{x:0,y:0,w:0,h:0,r:0,b:0},trim:!1,sourceSize:{w:0,h:0},spriteSourceSize:{x:0,y:0,w:0,h:0,r:0,b:0},radius:0,drawImage:{x:0,y:0,width:0,height:0},is3Slice:!1,scale9:!1,scale9Borders:{x:0,y:0,w:0,h:0}},this.setSize(n,a,s,r)},setSize:function(t,e,i,s){void 0===i&&(i=0),void 0===s&&(s=0),this.cutX=i,this.cutY=s,this.cutWidth=t,this.cutHeight=e,this.width=t,this.height=e,this.halfWidth=Math.floor(.5*t),this.halfHeight=Math.floor(.5*e),this.centerX=Math.floor(t/2),this.centerY=Math.floor(e/2);var r=this.data,n=r.cut;n.x=i,n.y=s,n.w=t,n.h=e,n.r=i+t,n.b=s+e,r.sourceSize.w=t,r.sourceSize.h=e,r.spriteSourceSize.w=t,r.spriteSourceSize.h=e,r.radius=.5*Math.sqrt(t*t+e*e);var a=r.drawImage;return a.x=i,a.y=s,a.width=t,a.height=e,this.updateUVs()},setTrim:function(t,e,i,s,r,n){var a=this.data,o=a.spriteSourceSize;return a.trim=!0,a.sourceSize.w=t,a.sourceSize.h=e,o.x=i,o.y=s,o.w=r,o.h=n,o.r=i+r,o.b=s+n,this.x=i,this.y=s,this.width=r,this.height=n,this.halfWidth=.5*r,this.halfHeight=.5*n,this.centerX=Math.floor(r/2),this.centerY=Math.floor(n/2),this.updateUVs()},setScale9:function(t,e,i,s){var r=this.data;return r.scale9=!0,r.is3Slice=0===e&&s===this.height,r.scale9Borders.x=t,r.scale9Borders.y=e,r.scale9Borders.w=i,r.scale9Borders.h=s,this},setCropUVs:function(t,e,i,s,n,a,o){var h=this.cutX,l=this.cutY,c=this.cutWidth,u=this.cutHeight,d=this.realWidth,f=this.realHeight,p=h+(e=r(e,0,d)),g=l+(i=r(i,0,f)),m=s=r(s,0,d-e),x=n=r(n,0,f-i),v=this.data;if(v.trim){var y=v.spriteSourceSize,w=e+(s=r(s,0,c-e)),b=i+(n=r(n,0,u-i));if(!(y.r<e||y.b<i||y.x>w||y.y>b)){var A=Math.max(y.x,e),M=Math.max(y.y,i),E=Math.min(y.r,w)-A,S=Math.min(y.b,b)-M;m=E,x=S,p=a?h+(c-(A-y.x)-E):h+(A-y.x),g=o?l+(u-(M-y.y)-S):l+(M-y.y),e=A,i=M,s=E,n=S}else p=0,g=0,m=0,x=0}else a&&(p=h+(c-e-s)),o&&(g=l+(u-i-n));var T=this.source.width,I=this.source.height;return t.u0=Math.max(0,p/T),t.v0=Math.max(0,g/I),t.u1=Math.min(1,(p+m)/T),t.v1=Math.min(1,(g+x)/I),t.x=e,t.y=i,t.cx=p,t.cy=g,t.cw=m,t.ch=x,t.width=s,t.height=n,t.flipX=a,t.flipY=o,t},updateCropUVs:function(t,e,i){return this.setCropUVs(t,t.x,t.y,t.width,t.height,e,i)},setUVs:function(t,e,i,s,r,n){var a=this.data.drawImage;return a.width=t,a.height=e,this.u0=i,this.v0=s,this.u1=r,this.v1=n,this},updateUVs:function(){var t=this.cutX,e=this.cutY,i=this.cutWidth,s=this.cutHeight,r=this.data.drawImage;r.width=i,r.height=s;var n=this.source.width,a=this.source.height;return this.u0=t/n,this.v0=e/a,this.u1=(t+i)/n,this.v1=(e+s)/a,this},updateUVsInverted:function(){var t=this.source.width,e=this.source.height;return this.u0=(this.cutX+this.cutHeight)/t,this.v0=this.cutY/e,this.u1=this.cutX/t,this.v1=(this.cutY+this.cutWidth)/e,this},clone:function(){var t=new a(this.texture,this.name,this.sourceIndex);return t.cutX=this.cutX,t.cutY=this.cutY,t.cutWidth=this.cutWidth,t.cutHeight=this.cutHeight,t.x=this.x,t.y=this.y,t.width=this.width,t.height=this.height,t.halfWidth=this.halfWidth,t.halfHeight=this.halfHeight,t.centerX=this.centerX,t.centerY=this.centerY,t.rotated=this.rotated,t.data=n(!0,t.data,this.data),t.updateUVs(),t},destroy:function(){this.texture=null,this.source=null,this.glTexture=null,this.customData=null,this.data=null},realWidth:{get:function(){return this.data.sourceSize.w}},realHeight:{get:function(){return this.data.sourceSize.h}},radius:{get:function(){return this.data.radius}},trimmed:{get:function(){return this.data.trim}},scale9:{get:function(){return this.data.scale9}},is3Slice:{get:function(){return this.data.is3Slice}},canvasData:{get:function(){return this.data.drawImage}}});t.exports=a},1864:t=>{t.exports=function(t,e,i){return t&&t.hasOwnProperty(e)?t[e]:i}},3747:t=>{t.exports={CREATED:0,DELAY:2,PENDING_RENDER:4,PLAYING_FORWARD:5,PLAYING_BACKWARD:6,HOLD_DELAY:7,REPEAT_DELAY:8,COMPLETE:9,PENDING:20,ACTIVE:21,LOOP_DELAY:22,COMPLETE_DELAY:23,START_DELAY:24,PENDING_REMOVE:25,REMOVED:26,FINISHED:27,DESTROYED:28,MAX:999999999999}},7473:t=>{function e(t,e,i){var s=i?t[e]:Object.getOwnPropertyDescriptor(t,e);return!i&&s.value&&"object"==typeof s.value&&(s=s.value),!(!s||!function(t){return!!t.get&&"function"==typeof t.get||!!t.set&&"function"==typeof t.set}(s))&&(void 0===s.enumerable&&(s.enumerable=!0),void 0===s.configurable&&(s.configurable=!0),s)}function i(t,e){var i=Object.getOwnPropertyDescriptor(t,e);return!!i&&(i.value&&"object"==typeof i.value&&(i=i.value),!1===i.configurable)}function s(t,s,r,a){for(var o in s)if(s.hasOwnProperty(o)){var h=e(s,o,r);if(!1!==h){if(i((a||t).prototype,o)){if(n.ignoreFinals)continue;throw new Error("cannot override final property '"+o+"', set Class.ignoreFinals = true to skip")}Object.defineProperty(t.prototype,o,h)}else t.prototype[o]=s[o]}}function r(t,e){if(e){Array.isArray(e)||(e=[e]);for(var i=0;i<e.length;i++)s(t,e[i].prototype||e[i])}}function n(t){var e,i;if(t||(t={}),t.initialize){if("function"!=typeof t.initialize)throw new Error("initialize must be a function");e=t.initialize,delete t.initialize}else if(t.Extends){var n=t.Extends;e=function(){n.apply(this,arguments)}}else e=function(){};t.Extends?(e.prototype=Object.create(t.Extends.prototype),e.prototype.constructor=e,i=t.Extends,delete t.Extends):e.prototype.constructor=e;var a=null;return t.Mixins&&(a=t.Mixins,delete t.Mixins),r(e,a),s(e,t,!0,i),e}n.extend=s,n.mixin=r,n.ignoreFinals=!1,t.exports=n},1984:t=>{t.exports=function(){}},1792:t=>{t.exports=function(t,e,i,s,r){if(void 0===r&&(r=t),i>0){var n=i-t.length;if(n<=0)return null}if(!Array.isArray(e))return-1===t.indexOf(e)?(t.push(e),s&&s.call(r,e),e):null;for(var a=e.length-1;a>=0;)-1!==t.indexOf(e[a])&&e.splice(a,1),a--;if(0===(a=e.length))return null;i>0&&a>n&&(e.splice(n),a=n);for(var o=0;o<a;o++){var h=e[o];t.push(h),s&&s.call(r,h)}return e}},2280:t=>{t.exports=function(t,e,i,s,r,n){if(void 0===i&&(i=0),void 0===n&&(n=t),s>0){var a=s-t.length;if(a<=0)return null}if(!Array.isArray(e))return-1===t.indexOf(e)?(t.splice(i,0,e),r&&r.call(n,e),e):null;for(var o=e.length-1;o>=0;)-1!==t.indexOf(e[o])&&e.pop(),o--;if(0===(o=e.length))return null;s>0&&o>a&&(e.splice(a),o=a);for(var h=o-1;h>=0;h--){var l=e[h];t.splice(i,0,l),r&&r.call(n,l)}return e}},2513:t=>{t.exports=function(t,e){var i=t.indexOf(e);return-1!==i&&i<t.length&&(t.splice(i,1),t.push(e)),e}},1771:(t,e,i)=>{var s=i(2497);t.exports=function(t,e,i,r,n){void 0===r&&(r=0),void 0===n&&(n=t.length);var a=0;if(s(t,r,n))for(var o=r;o<n;o++){t[o][e]===i&&a++}return a}},7883:t=>{t.exports=function(t,e,i){var s,r=[null];for(s=3;s<arguments.length;s++)r.push(arguments[s]);for(s=0;s<t.length;s++)r[0]=t[s],e.apply(i,r);return t}},5856:(t,e,i)=>{var s=i(2497);t.exports=function(t,e,i,r,n){if(void 0===r&&(r=0),void 0===n&&(n=t.length),s(t,r,n)){var a,o=[null];for(a=5;a<arguments.length;a++)o.push(arguments[a]);for(a=r;a<n;a++)o[0]=t[a],e.apply(i,o)}return t}},3957:t=>{t.exports=function(t,e,i){if(!e.length)return NaN;if(1===e.length)return e[0];var s,r,n=1;if(i){if(t<e[0][i])return e[0];for(;e[n][i]<t;)n++}else for(;e[n]<t;)n++;return n>e.length&&(n=e.length),i?(s=e[n-1][i],(r=e[n][i])-t<=t-s?e[n]:e[n-1]):(s=e[n-1],(r=e[n])-t<=t-s?r:s)}},4493:t=>{var e=function(t,i){void 0===i&&(i=[]);for(var s=0;s<t.length;s++)Array.isArray(t[s])?e(t[s],i):i.push(t[s]);return i};t.exports=e},6245:(t,e,i)=>{var s=i(2497);t.exports=function(t,e,i,r,n){void 0===r&&(r=0),void 0===n&&(n=t.length);var a=[];if(s(t,r,n))for(var o=r;o<n;o++){var h=t[o];(!e||e&&void 0===i&&h.hasOwnProperty(e)||e&&void 0!==i&&h[e]===i)&&a.push(h)}return a}},1647:(t,e,i)=>{var s=i(2497);t.exports=function(t,e,i,r,n){if(void 0===r&&(r=0),void 0===n&&(n=t.length),s(t,r,n))for(var a=r;a<n;a++){var o=t[a];if(!e||e&&void 0===i&&o.hasOwnProperty(e)||e&&void 0!==i&&o[e]===i)return o}return null}},5301:t=>{t.exports=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=t.length);var s=e+Math.floor(Math.random()*i);return void 0===t[s]?null:t[s]}},8683:t=>{t.exports=function(t,e,i){if(e===i)return t;var s=t.indexOf(e),r=t.indexOf(i);if(s<0||r<0)throw new Error("Supplied items must be elements of the same array");return s>r||(t.splice(s,1),r===t.length-1?t.push(e):t.splice(r,0,e)),t}},546:t=>{t.exports=function(t,e,i){if(e===i)return t;var s=t.indexOf(e),r=t.indexOf(i);if(s<0||r<0)throw new Error("Supplied items must be elements of the same array");return s<r||(t.splice(s,1),0===r?t.unshift(e):t.splice(r,0,e)),t}},1842:t=>{t.exports=function(t,e){var i=t.indexOf(e);if(i>0){var s=t[i-1],r=t.indexOf(s);t[i]=s,t[r]=e}return t}},1419:t=>{t.exports=function(t,e,i){var s=t.indexOf(e);if(-1===s||i<0||i>=t.length)throw new Error("Supplied index out of bounds");return s!==i&&(t.splice(s,1),t.splice(i,0,e)),e}},6512:t=>{t.exports=function(t,e){var i=t.indexOf(e);if(-1!==i&&i<t.length-1){var s=t[i+1],r=t.indexOf(s);t[i]=s,t[r]=e}return t}},4130:t=>{t.exports=function(t,e,i,s){var r,n=[],a=!1;if((i||s)&&(a=!0,i||(i=""),s||(s="")),e<t)for(r=t;r>=e;r--)a?n.push(i+r.toString()+s):n.push(r);else for(r=t;r<=e;r++)a?n.push(i+r.toString()+s):n.push(r);return n}},1316:(t,e,i)=>{var s=i(4078);t.exports=function(t,e,i){void 0===t&&(t=0),void 0===e&&(e=null),void 0===i&&(i=1),null===e&&(e=t,t=0);for(var r=[],n=Math.max(s((e-t)/(i||1)),0),a=0;a<n;a++)r.push(t),t+=i;return r}},9465:t=>{function e(t,e,i){var s=t[e];t[e]=t[i],t[i]=s}function i(t,e){return t<e?-1:t>e?1:0}var s=function(t,r,n,a,o){for(void 0===n&&(n=0),void 0===a&&(a=t.length-1),void 0===o&&(o=i);a>n;){if(a-n>600){var h=a-n+1,l=r-n+1,c=Math.log(h),u=.5*Math.exp(2*c/3),d=.5*Math.sqrt(c*u*(h-u)/h)*(l-h/2<0?-1:1),f=Math.max(n,Math.floor(r-l*u/h+d)),p=Math.min(a,Math.floor(r+(h-l)*u/h+d));s(t,r,f,p,o)}var g=t[r],m=n,x=a;for(e(t,n,r),o(t[a],g)>0&&e(t,n,a);m<x;){for(e(t,m,x),m++,x--;o(t[m],g)<0;)m++;for(;o(t[x],g)>0;)x--}0===o(t[n],g)?e(t,n,x):e(t,++x,a),x<=r&&(n=x+1),r<=x&&(a=x-1)}};t.exports=s},9703:(t,e,i)=>{var s=i(5851),r=i(4912),n=function(t,e,i){for(var s=[],r=0;r<t.length;r++)for(var n=0;n<e.length;n++)for(var a=0;a<i;a++)s.push({a:t[r],b:e[n]});return s};t.exports=function(t,e,i){var a=s(i,"max",0),o=s(i,"qty",1),h=s(i,"random",!1),l=s(i,"randomB",!1),c=s(i,"repeat",0),u=s(i,"yoyo",!1),d=[];if(l&&r(e),-1===c)if(0===a)c=0;else{var f=t.length*e.length*o;u&&(f*=2),c=Math.ceil(a/f)}for(var p=0;p<=c;p++){var g=n(t,e,o);h&&r(g),d=d.concat(g),u&&(g.reverse(),d=d.concat(g))}return a&&d.splice(a),d}},7161:(t,e,i)=>{var s=i(8935);t.exports=function(t,e,i,r){var n;if(void 0===r&&(r=t),!Array.isArray(e))return-1!==(n=t.indexOf(e))?(s(t,n),i&&i.call(r,e),e):null;for(var a=e.length-1,o=[];a>=0;){var h=e[a];-1!==(n=t.indexOf(h))&&(s(t,n),o.push(h),i&&i.call(r,h)),a--}return o}},4725:(t,e,i)=>{var s=i(8935);t.exports=function(t,e,i,r){if(void 0===r&&(r=t),e<0||e>t.length-1)throw new Error("Index out of bounds");var n=s(t,e);return i&&i.call(r,n),n}},8780:(t,e,i)=>{var s=i(2497);t.exports=function(t,e,i,r,n){if(void 0===e&&(e=0),void 0===i&&(i=t.length),void 0===n&&(n=t),s(t,e,i)){var a=i-e,o=t.splice(e,a);if(r)for(var h=0;h<o.length;h++){var l=o[h];r.call(n,l)}return o}return[]}},5744:(t,e,i)=>{var s=i(8935);t.exports=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=t.length);var r=e+Math.floor(Math.random()*i);return s(t,r)}},6960:t=>{t.exports=function(t,e,i){var s=t.indexOf(e),r=t.indexOf(i);return-1!==s&&-1===r&&(t[s]=i,!0)}},1021:t=>{t.exports=function(t,e){void 0===e&&(e=1);for(var i=null,s=0;s<e;s++)i=t.shift(),t.push(i);return i}},4027:t=>{t.exports=function(t,e){void 0===e&&(e=1);for(var i=null,s=0;s<e;s++)i=t.pop(),t.unshift(i);return i}},2497:t=>{t.exports=function(t,e,i,s){var r=t.length;if(e<0||e>r||e>=i||i>r){if(s)throw new Error("Range Error: Values outside acceptable range");return!1}return!0}},5361:t=>{t.exports=function(t,e){var i=t.indexOf(e);return-1!==i&&i>0&&(t.splice(i,1),t.unshift(e)),e}},3718:(t,e,i)=>{var s=i(2497);t.exports=function(t,e,i,r,n){if(void 0===r&&(r=0),void 0===n&&(n=t.length),s(t,r,n))for(var a=r;a<n;a++){var o=t[a];o.hasOwnProperty(e)&&(o[e]=i)}return t}},4912:t=>{t.exports=function(t){for(var e=t.length-1;e>0;e--){var i=Math.floor(Math.random()*(e+1)),s=t[e];t[e]=t[i],t[i]=s}return t}},2071:t=>{t.exports=function(t){var e=/\D/g;return t.sort((function(t,i){return parseInt(t.replace(e,""),10)-parseInt(i.replace(e,""),10)})),t}},8935:t=>{t.exports=function(t,e){if(!(e>=t.length)){for(var i=t.length-1,s=t[e],r=e;r<i;r++)t[r]=t[r+1];return t.length=i,s}}},9992:(t,e,i)=>{var s=i(9356);function r(t,e){return String(t).localeCompare(e)}function n(t,e,i,s){var r,n,a,o,h,l=t.length,c=0,u=2*i;for(r=0;r<l;r+=u)for(a=(n=r+i)+i,n>l&&(n=l),a>l&&(a=l),o=r,h=n;;)if(o<n&&h<a)e(t[o],t[h])<=0?s[c++]=t[o++]:s[c++]=t[h++];else if(o<n)s[c++]=t[o++];else{if(!(h<a))break;s[c++]=t[h++]}}t.exports=function(t,e){if(void 0===e&&(e=r),!t||t.length<2)return t;if(s.features.stableSort)return t.sort(e);var i=function(t,e){var i=t.length;if(i<=1)return t;for(var s=new Array(i),r=1;r<i;r*=2){n(t,e,r,s);var a=t;t=s,s=a}return t}(t,e);return i!==t&&n(i,null,t.length,t),t}},2372:t=>{t.exports=function(t,e,i){if(e===i)return t;var s=t.indexOf(e),r=t.indexOf(i);if(s<0||r<0)throw new Error("Supplied items must be elements of the same array");return t[s]=i,t[r]=e,t}},1953:(t,e,i)=>{t.exports={Matrix:i(1237),Add:i(1792),AddAt:i(2280),BringToTop:i(2513),CountAllMatching:i(1771),Each:i(7883),EachInRange:i(5856),FindClosestInSorted:i(3957),Flatten:i(4493),GetAll:i(6245),GetFirst:i(1647),GetRandom:i(5301),MoveDown:i(1842),MoveTo:i(1419),MoveUp:i(6512),MoveAbove:i(8683),MoveBelow:i(546),NumberArray:i(4130),NumberArrayStep:i(1316),QuickSelect:i(9465),Range:i(9703),Remove:i(7161),RemoveAt:i(4725),RemoveBetween:i(8780),RemoveRandomElement:i(5744),Replace:i(6960),RotateLeft:i(1021),RotateRight:i(4027),SafeRange:i(2497),SendToBack:i(5361),SetAll:i(3718),Shuffle:i(4912),SortByDigits:i(2071),SpliceOne:i(8935),StableSort:i(9992),Swap:i(2372)}},1816:t=>{t.exports=function(t){if(!Array.isArray(t)||!Array.isArray(t[0]))return!1;for(var e=t[0].length,i=1;i<t.length;i++)if(t[i].length!==e)return!1;return!0}},6655:(t,e,i)=>{var s=i(7222),r=i(1816);t.exports=function(t){var e="";if(!r(t))return e;for(var i=0;i<t.length;i++){for(var n=0;n<t[i].length;n++){var a=t[i][n].toString();e+="undefined"!==a?s(a,2):"?",n<t[i].length-1&&(e+=" |")}if(i<t.length-1){e+="\n";for(var o=0;o<t[i].length;o++)e+="---",o<t[i].length-1&&(e+="+");e+="\n"}}return e}},582:t=>{t.exports=function(t){return t.reverse()}},6063:t=>{t.exports=function(t){for(var e=0;e<t.length;e++)t[e].reverse();return t}},8321:(t,e,i)=>{var s=i(7116);t.exports=function(t){return s(t,180)}},2597:(t,e,i)=>{var s=i(7116);t.exports=function(t,e){void 0===e&&(e=1);for(var i=0;i<e;i++)t=s(t,90);return t}},7116:(t,e,i)=>{var s=i(1816),r=i(4780);t.exports=function(t,e){if(void 0===e&&(e=90),!s(t))return null;if("string"!=typeof e&&(e=(e%360+360)%360),90===e||-270===e||"rotateLeft"===e)(t=r(t)).reverse();else if(-90===e||270===e||"rotateRight"===e)t.reverse(),t=r(t);else if(180===Math.abs(e)||"rotate180"===e){for(var i=0;i<t.length;i++)t[i].reverse();t.reverse()}return t}},6285:(t,e,i)=>{var s=i(7116);t.exports=function(t,e){void 0===e&&(e=1);for(var i=0;i<e;i++)t=s(t,-90);return t}},7711:(t,e,i)=>{var s=i(1021),r=i(4027);t.exports=function(t,e,i){if(void 0===e&&(e=0),void 0===i&&(i=0),0!==i&&(i<0?s(t,Math.abs(i)):r(t,i)),0!==e)for(var n=0;n<t.length;n++){var a=t[n];e<0?s(a,Math.abs(e)):r(a,e)}return t}},4780:t=>{t.exports=function(t){for(var e=t.length,i=t[0].length,s=new Array(i),r=0;r<i;r++){s[r]=new Array(e);for(var n=e-1;n>-1;n--)s[r][n]=t[n][r]}return s}},1237:(t,e,i)=>{t.exports={CheckMatrix:i(1816),MatrixToString:i(6655),ReverseColumns:i(582),ReverseRows:i(6063),Rotate180:i(8321),RotateLeft:i(2597),RotateMatrix:i(7116),RotateRight:i(6285),Translate:i(7711),TransposeMatrix:i(4780)}},3911:t=>{var e=function(t){var i,s,r;if("object"!=typeof t||null===t)return t;for(r in i=Array.isArray(t)?[]:{},t)s=t[r],i[r]=e(s);return i};t.exports=e},1030:(t,e,i)=>{var s=i(2482),r=function(){var t,e,i,n,a,o,h=arguments[0]||{},l=1,c=arguments.length,u=!1;for("boolean"==typeof h&&(u=h,h=arguments[1]||{},l=2),c===l&&(h=this,--l);l<c;l++)if(null!=(t=arguments[l]))for(e in t)i=h[e],h!==(n=t[e])&&(u&&n&&(s(n)||(a=Array.isArray(n)))?(a?(a=!1,o=i&&Array.isArray(i)?i:[]):o=i&&s(i)?i:{},h[e]=r(u,o,n)):void 0!==n&&(h[e]=n));return h};t.exports=r},8361:(t,e,i)=>{var s=i(4675),r=i(5851);t.exports=function(t,e,i){var n=r(t,e,null);if(null===n)return i;if(Array.isArray(n))return s.RND.pick(n);if("object"==typeof n){if(n.hasOwnProperty("randInt"))return s.RND.integerInRange(n.randInt[0],n.randInt[1]);if(n.hasOwnProperty("randFloat"))return s.RND.realInRange(n.randFloat[0],n.randFloat[1])}else if("function"==typeof n)return n(e);return n}},4597:t=>{t.exports=function(t,e,i){var s=typeof t;return t&&"number"!==s&&"string"!==s&&t.hasOwnProperty(e)&&void 0!==t[e]?t[e]:i}},5851:t=>{t.exports=function(t,e,i,s){if(!t&&!s||"number"==typeof t)return i;if(t&&t.hasOwnProperty(e))return t[e];if(s&&s.hasOwnProperty(e))return s[e];if(-1!==e.indexOf(".")){for(var r=e.split("."),n=t,a=s,o=i,h=i,l=!0,c=!0,u=0;u<r.length;u++)n&&n.hasOwnProperty(r[u])?(o=n[r[u]],n=n[r[u]]):l=!1,a&&a.hasOwnProperty(r[u])?(h=a[r[u]],a=a[r[u]]):c=!1;return l?o:c?h:i}return i}},2482:t=>{t.exports=function(t){if(!t||"object"!=typeof t||t.nodeType||t===t.window)return!1;try{if(t.constructor&&!{}.hasOwnProperty.call(t.constructor.prototype,"isPrototypeOf"))return!1}catch(t){return!1}return!0}},7222:t=>{t.exports=function(t,e,i,s){void 0===e&&(e=0),void 0===i&&(i=" "),void 0===s&&(s=3);var r=0;if(e+1>=(t=t.toString()).length)switch(s){case 1:t=new Array(e+1-t.length).join(i)+t;break;case 3:var n=Math.ceil((r=e-t.length)/2);t=new Array(r-n+1).join(i)+t+new Array(n+1).join(i);break;default:t+=new Array(e+1-t.length).join(i)}return t}}},e={};var i=function i(s){var r=e[s];if(void 0!==r)return r.exports;var n=e[s]={exports:{}};return t[s](n,n.exports,i),n.exports}(4513);window.SpinePlugin=i})();