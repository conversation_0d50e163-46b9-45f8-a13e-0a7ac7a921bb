/**
 * <AUTHOR> <<EMAIL>>
 * @copyright    2013-2025 Phaser Studio Inc.
 * @license      {@link https://opensource.org/licenses/MIT|MIT License}
 */

/**
 * PlayStation DualShock 4 Gamepad Configuration.
 * Sony PlayStation DualShock 4 (v2) wireless controller
 *
 * @name Phaser.Input.Gamepad.Configs.DUALSHOCK_4
 * @namespace
 * @since 3.0.0
 */
module.exports = {

    /**
     * D-Pad up
     *
     * @name Phaser.Input.Gamepad.Configs.DUALSHOCK_4.UP
     * @const
     * @type {number}
     * @since 3.0.0
     */
    UP: 12,

    /**
     * D-Pad down
     *
     * @name Phaser.Input.Gamepad.Configs.DUALSHOCK_4.DOWN
     * @const
     * @type {number}
     * @since 3.0.0
     */
    DOWN: 13,

    /**
     * D-Pad left
     *
     * @name Phaser.Input.Gamepad.Configs.DUALSHOCK_4.LEFT
     * @const
     * @type {number}
     * @since 3.0.0
     */
    LEFT: 14,

    /**
     * D-Pad up
     *
     * @name Phaser.Input.Gamepad.Configs.DUALSHOCK_4.RIGHT
     * @const
     * @type {number}
     * @since 3.0.0
     */
    RIGHT: 15,

    /**
     * Share button
     *
     * @name Phaser.Input.Gamepad.Configs.DUALSHOCK_4.SHARE
     * @const
     * @type {number}
     * @since 3.0.0
     */
    SHARE: 8,

    /**
     * Options button
     *
     * @name Phaser.Input.Gamepad.Configs.DUALSHOCK_4.OPTIONS
     * @const
     * @type {number}
     * @since 3.0.0
     */
    OPTIONS: 9,

    /**
     * PlayStation logo button
     *
     * @name Phaser.Input.Gamepad.Configs.DUALSHOCK_4.PS
     * @const
     * @type {number}
     * @since 3.0.0
     */
    PS: 16,

    /**
     * Touchpad click
     *
     * @name Phaser.Input.Gamepad.Configs.DUALSHOCK_4.TOUCHBAR
     * @const
     * @type {number}
     * @since 3.0.0
     */
    TOUCHBAR: 17,

    /**
     * Cross button (Bottom)
     *
     * @name Phaser.Input.Gamepad.Configs.DUALSHOCK_4.X
     * @const
     * @type {number}
     * @since 3.0.0
     */
    X: 0,

    /**
     * Circle button (Right)
     *
     * @name Phaser.Input.Gamepad.Configs.DUALSHOCK_4.CIRCLE
     * @const
     * @type {number}
     * @since 3.0.0
     */
    CIRCLE: 1,

    /**
     * Square button (Left)
     *
     * @name Phaser.Input.Gamepad.Configs.DUALSHOCK_4.SQUARE
     * @const
     * @type {number}
     * @since 3.0.0
     */
    SQUARE: 2,

    /**
     * Triangle button (Top)
     *
     * @name Phaser.Input.Gamepad.Configs.DUALSHOCK_4.TRIANGLE
     * @const
     * @type {number}
     * @since 3.0.0
     */
    TRIANGLE: 3,

    /**
     * Left bumper (L1)
     *
     * @name Phaser.Input.Gamepad.Configs.DUALSHOCK_4.L1
     * @const
     * @type {number}
     * @since 3.0.0
     */
    L1: 4,

    /**
     * Right bumper (R1)
     *
     * @name Phaser.Input.Gamepad.Configs.DUALSHOCK_4.R1
     * @const
     * @type {number}
     * @since 3.0.0
     */
    R1: 5,

    /**
     * Left trigger (L2)
     *
     * @name Phaser.Input.Gamepad.Configs.DUALSHOCK_4.L2
     * @const
     * @type {number}
     * @since 3.0.0
     */
    L2: 6,

    /**
     * Right trigger (R2)
     *
     * @name Phaser.Input.Gamepad.Configs.DUALSHOCK_4.R2
     * @const
     * @type {number}
     * @since 3.0.0
     */
    R2: 7,

    /**
     * Left stick click (L3)
     *
     * @name Phaser.Input.Gamepad.Configs.DUALSHOCK_4.L3
     * @const
     * @type {number}
     * @since 3.0.0
     */
    L3: 10,

    /**
     * Right stick click (R3)
     *
     * @name Phaser.Input.Gamepad.Configs.DUALSHOCK_4.R3
     * @const
     * @type {number}
     * @since 3.0.0
     */
    R3: 11,

    /**
     * Left stick horizontal
     *
     * @name Phaser.Input.Gamepad.Configs.DUALSHOCK_4.LEFT_STICK_H
     * @const
     * @type {number}
     * @since 3.0.0
     */
    LEFT_STICK_H: 0,

    /**
     * Left stick vertical
     *
     * @name Phaser.Input.Gamepad.Configs.DUALSHOCK_4.LEFT_STICK_V
     * @const
     * @type {number}
     * @since 3.0.0
     */
    LEFT_STICK_V: 1,

    /**
     * Right stick horizontal
     *
     * @name Phaser.Input.Gamepad.Configs.DUALSHOCK_4.RIGHT_STICK_H
     * @const
     * @type {number}
     * @since 3.0.0
     */
    RIGHT_STICK_H: 2,

    /**
     * Right stick vertical
     *
     * @name Phaser.Input.Gamepad.Configs.DUALSHOCK_4.RIGHT_STICK_V
     * @const
     * @type {number}
     * @since 3.0.0
     */
    RIGHT_STICK_V: 3

};
