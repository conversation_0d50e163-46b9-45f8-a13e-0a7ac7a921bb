(()=>{var t={4399:t=>{"use strict";var e=Object.prototype.hasOwnProperty,i="~";function n(){}function r(t,e,i){this.fn=t,this.context=e,this.once=i||!1}function s(t,e,n,s,a){if("function"!=typeof n)throw new TypeError("The listener must be a function");var o=new r(n,s||t,a),h=i?i+e:e;return t._events[h]?t._events[h].fn?t._events[h]=[t._events[h],o]:t._events[h].push(o):(t._events[h]=o,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function o(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(i=!1)),o.prototype.eventNames=function(){var t,n,r=[];if(0===this._eventsCount)return r;for(n in t=this._events)e.call(t,n)&&r.push(i?n.slice(1):n);return Object.getOwnPropertySymbols?r.concat(Object.getOwnPropertySymbols(t)):r},o.prototype.listeners=function(t){var e=i?i+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var r=0,s=n.length,a=new Array(s);r<s;r++)a[r]=n[r].fn;return a},o.prototype.listenerCount=function(t){var e=i?i+t:t,n=this._events[e];return n?n.fn?1:n.length:0},o.prototype.emit=function(t,e,n,r,s,a){var o=i?i+t:t;if(!this._events[o])return!1;var h,l,u=this._events[o],c=arguments.length;if(u.fn){switch(u.once&&this.removeListener(t,u.fn,void 0,!0),c){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,e),!0;case 3:return u.fn.call(u.context,e,n),!0;case 4:return u.fn.call(u.context,e,n,r),!0;case 5:return u.fn.call(u.context,e,n,r,s),!0;case 6:return u.fn.call(u.context,e,n,r,s,a),!0}for(l=1,h=new Array(c-1);l<c;l++)h[l-1]=arguments[l];u.fn.apply(u.context,h)}else{var d,f=u.length;for(l=0;l<f;l++)switch(u[l].once&&this.removeListener(t,u[l].fn,void 0,!0),c){case 1:u[l].fn.call(u[l].context);break;case 2:u[l].fn.call(u[l].context,e);break;case 3:u[l].fn.call(u[l].context,e,n);break;case 4:u[l].fn.call(u[l].context,e,n,r);break;default:if(!h)for(d=1,h=new Array(c-1);d<c;d++)h[d-1]=arguments[d];u[l].fn.apply(u[l].context,h)}}return!0},o.prototype.on=function(t,e,i){return s(this,t,e,i,!1)},o.prototype.once=function(t,e,i){return s(this,t,e,i,!0)},o.prototype.removeListener=function(t,e,n,r){var s=i?i+t:t;if(!this._events[s])return this;if(!e)return a(this,s),this;var o=this._events[s];if(o.fn)o.fn!==e||r&&!o.once||n&&o.context!==n||a(this,s);else{for(var h=0,l=[],u=o.length;h<u;h++)(o[h].fn!==e||r&&!o[h].once||n&&o[h].context!==n)&&l.push(o[h]);l.length?this._events[s]=1===l.length?l[0]:l:a(this,s)}return this},o.prototype.removeAllListeners=function(t){var e;return t?(e=i?i+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=i,o.EventEmitter=o,t.exports=o},6937:t=>{(function(){var e,i,n,r=this&&this.__extends||(e=function(t,i){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},e(t,i)},function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function n(){this.constructor=t}e(t,i),t.prototype=null===i?Object.create(i):(n.prototype=i.prototype,new n)});!function(t){var e,i,n,s=function(){function t(t,e,i){if(null==t)throw new Error("name cannot be null.");if(null==e)throw new Error("timelines cannot be null.");this.name=t,this.timelines=e,this.timelineIds=[];for(var n=0;n<e.length;n++)this.timelineIds[e[n].getPropertyId()]=!0;this.duration=i}return t.prototype.hasTimeline=function(t){return 1==this.timelineIds[t]},t.prototype.apply=function(t,e,i,n,r,s,a,o){if(null==t)throw new Error("skeleton cannot be null.");n&&0!=this.duration&&(i%=this.duration,e>0&&(e%=this.duration));for(var h=this.timelines,l=0,u=h.length;l<u;l++)h[l].apply(t,e,i,r,s,a,o)},t.binarySearch=function(t,e,i){void 0===i&&(i=1);var n=0,r=t.length/i-2;if(0==r)return i;for(var s=r>>>1;;){if(t[(s+1)*i]<=e?n=s+1:r=s,n==r)return(n+1)*i;s=n+r>>>1}},t.linearSearch=function(t,e,i){for(var n=0,r=t.length-i;n<=r;n+=i)if(t[n]>e)return n;return-1},t}();t.Animation=s,function(t){t[t.setup=0]="setup",t[t.first=1]="first",t[t.replace=2]="replace",t[t.add=3]="add"}(e=t.MixBlend||(t.MixBlend={})),function(t){t[t.mixIn=0]="mixIn",t[t.mixOut=1]="mixOut"}(i=t.MixDirection||(t.MixDirection={})),function(t){t[t.rotate=0]="rotate",t[t.translate=1]="translate",t[t.scale=2]="scale",t[t.shear=3]="shear",t[t.attachment=4]="attachment",t[t.color=5]="color",t[t.deform=6]="deform",t[t.event=7]="event",t[t.drawOrder=8]="drawOrder",t[t.ikConstraint=9]="ikConstraint",t[t.transformConstraint=10]="transformConstraint",t[t.pathConstraintPosition=11]="pathConstraintPosition",t[t.pathConstraintSpacing=12]="pathConstraintSpacing",t[t.pathConstraintMix=13]="pathConstraintMix",t[t.twoColor=14]="twoColor"}(n=t.TimelineType||(t.TimelineType={}));var a=function(){function e(i){if(i<=0)throw new Error("frameCount must be > 0: "+i);this.curves=t.Utils.newFloatArray((i-1)*e.BEZIER_SIZE)}return e.prototype.getFrameCount=function(){return this.curves.length/e.BEZIER_SIZE+1},e.prototype.setLinear=function(t){this.curves[t*e.BEZIER_SIZE]=e.LINEAR},e.prototype.setStepped=function(t){this.curves[t*e.BEZIER_SIZE]=e.STEPPED},e.prototype.getCurveType=function(t){var i=t*e.BEZIER_SIZE;if(i==this.curves.length)return e.LINEAR;var n=this.curves[i];return n==e.LINEAR?e.LINEAR:n==e.STEPPED?e.STEPPED:e.BEZIER},e.prototype.setCurve=function(t,i,n,r,s){var a=.03*(2*-i+r),o=.03*(2*-n+s),h=.006*(3*(i-r)+1),l=.006*(3*(n-s)+1),u=2*a+h,c=2*o+l,d=.3*i+a+.16666667*h,f=.3*n+o+.16666667*l,p=t*e.BEZIER_SIZE,v=this.curves;v[p++]=e.BEZIER;for(var g=d,m=f,M=p+e.BEZIER_SIZE-1;p<M;p+=2)v[p]=g,v[p+1]=m,d+=u,f+=c,u+=h,c+=l,g+=d,m+=f},e.prototype.getCurvePercent=function(i,n){n=t.MathUtils.clamp(n,0,1);var r=this.curves,s=i*e.BEZIER_SIZE,a=r[s];if(a==e.LINEAR)return n;if(a==e.STEPPED)return 0;for(var o=0,h=++s,l=s+e.BEZIER_SIZE-1;s<l;s+=2)if((o=r[s])>=n){var u=void 0,c=void 0;return s==h?(u=0,c=0):(u=r[s-2],c=r[s-1]),c+(r[s+1]-c)*(n-u)/(o-u)}var d=r[s-1];return d+(1-d)*(n-o)/(1-o)},e.LINEAR=0,e.STEPPED=1,e.BEZIER=2,e.BEZIER_SIZE=19,e}();t.CurveTimeline=a;var o=function(i){function a(e){var n=i.call(this,e)||this;return n.frames=t.Utils.newFloatArray(e<<1),n}return r(a,i),a.prototype.getPropertyId=function(){return(n.rotate<<24)+this.boneIndex},a.prototype.setFrame=function(t,e,i){t<<=1,this.frames[t]=e,this.frames[t+a.ROTATION]=i},a.prototype.apply=function(t,i,n,r,o,h,l){var u=this.frames,c=t.bones[this.boneIndex];if(c.active)if(n<u[0])switch(h){case e.setup:return void(c.rotation=c.data.rotation);case e.first:var d=c.data.rotation-c.rotation;c.rotation+=(d-360*(16384-(16384.499999999996-d/360|0)))*o}else if(n>=u[u.length-a.ENTRIES]){var f=u[u.length+a.PREV_ROTATION];switch(h){case e.setup:c.rotation=c.data.rotation+f*o;break;case e.first:case e.replace:f+=c.data.rotation-c.rotation,f-=360*(16384-(16384.499999999996-f/360|0));case e.add:c.rotation+=f*o}}else{var p=s.binarySearch(u,n,a.ENTRIES),v=u[p+a.PREV_ROTATION],g=u[p],m=this.getCurvePercent((p>>1)-1,1-(n-g)/(u[p+a.PREV_TIME]-g)),M=u[p+a.ROTATION]-v;switch(M=v+(M-360*(16384-(16384.499999999996-M/360|0)))*m,h){case e.setup:c.rotation=c.data.rotation+(M-360*(16384-(16384.499999999996-M/360|0)))*o;break;case e.first:case e.replace:M+=c.data.rotation-c.rotation;case e.add:c.rotation+=(M-360*(16384-(16384.499999999996-M/360|0)))*o}}},a.ENTRIES=2,a.PREV_TIME=-2,a.PREV_ROTATION=-1,a.ROTATION=1,a}(a);t.RotateTimeline=o;var h=function(i){function a(e){var n=i.call(this,e)||this;return n.frames=t.Utils.newFloatArray(e*a.ENTRIES),n}return r(a,i),a.prototype.getPropertyId=function(){return(n.translate<<24)+this.boneIndex},a.prototype.setFrame=function(t,e,i,n){t*=a.ENTRIES,this.frames[t]=e,this.frames[t+a.X]=i,this.frames[t+a.Y]=n},a.prototype.apply=function(t,i,n,r,o,h,l){var u=this.frames,c=t.bones[this.boneIndex];if(c.active)if(n<u[0])switch(h){case e.setup:return c.x=c.data.x,void(c.y=c.data.y);case e.first:c.x+=(c.data.x-c.x)*o,c.y+=(c.data.y-c.y)*o}else{var d=0,f=0;if(n>=u[u.length-a.ENTRIES])d=u[u.length+a.PREV_X],f=u[u.length+a.PREV_Y];else{var p=s.binarySearch(u,n,a.ENTRIES);d=u[p+a.PREV_X],f=u[p+a.PREV_Y];var v=u[p],g=this.getCurvePercent(p/a.ENTRIES-1,1-(n-v)/(u[p+a.PREV_TIME]-v));d+=(u[p+a.X]-d)*g,f+=(u[p+a.Y]-f)*g}switch(h){case e.setup:c.x=c.data.x+d*o,c.y=c.data.y+f*o;break;case e.first:case e.replace:c.x+=(c.data.x+d-c.x)*o,c.y+=(c.data.y+f-c.y)*o;break;case e.add:c.x+=d*o,c.y+=f*o}}},a.ENTRIES=3,a.PREV_TIME=-3,a.PREV_X=-2,a.PREV_Y=-1,a.X=1,a.Y=2,a}(a);t.TranslateTimeline=h;var l=function(a){function o(t){return a.call(this,t)||this}return r(o,a),o.prototype.getPropertyId=function(){return(n.scale<<24)+this.boneIndex},o.prototype.apply=function(n,r,a,h,l,u,c){var d=this.frames,f=n.bones[this.boneIndex];if(f.active)if(a<d[0])switch(u){case e.setup:return f.scaleX=f.data.scaleX,void(f.scaleY=f.data.scaleY);case e.first:f.scaleX+=(f.data.scaleX-f.scaleX)*l,f.scaleY+=(f.data.scaleY-f.scaleY)*l}else{var p=0,v=0;if(a>=d[d.length-o.ENTRIES])p=d[d.length+o.PREV_X]*f.data.scaleX,v=d[d.length+o.PREV_Y]*f.data.scaleY;else{var g=s.binarySearch(d,a,o.ENTRIES);p=d[g+o.PREV_X],v=d[g+o.PREV_Y];var m=d[g],M=this.getCurvePercent(g/o.ENTRIES-1,1-(a-m)/(d[g+o.PREV_TIME]-m));p=(p+(d[g+o.X]-p)*M)*f.data.scaleX,v=(v+(d[g+o.Y]-v)*M)*f.data.scaleY}if(1==l)u==e.add?(f.scaleX+=p-f.data.scaleX,f.scaleY+=v-f.data.scaleY):(f.scaleX=p,f.scaleY=v);else{var x=0,y=0;if(c==i.mixOut)switch(u){case e.setup:x=f.data.scaleX,y=f.data.scaleY,f.scaleX=x+(Math.abs(p)*t.MathUtils.signum(x)-x)*l,f.scaleY=y+(Math.abs(v)*t.MathUtils.signum(y)-y)*l;break;case e.first:case e.replace:x=f.scaleX,y=f.scaleY,f.scaleX=x+(Math.abs(p)*t.MathUtils.signum(x)-x)*l,f.scaleY=y+(Math.abs(v)*t.MathUtils.signum(y)-y)*l;break;case e.add:x=f.scaleX,y=f.scaleY,f.scaleX=x+(Math.abs(p)*t.MathUtils.signum(x)-f.data.scaleX)*l,f.scaleY=y+(Math.abs(v)*t.MathUtils.signum(y)-f.data.scaleY)*l}else switch(u){case e.setup:x=Math.abs(f.data.scaleX)*t.MathUtils.signum(p),y=Math.abs(f.data.scaleY)*t.MathUtils.signum(v),f.scaleX=x+(p-x)*l,f.scaleY=y+(v-y)*l;break;case e.first:case e.replace:x=Math.abs(f.scaleX)*t.MathUtils.signum(p),y=Math.abs(f.scaleY)*t.MathUtils.signum(v),f.scaleX=x+(p-x)*l,f.scaleY=y+(v-y)*l;break;case e.add:x=t.MathUtils.signum(p),y=t.MathUtils.signum(v),f.scaleX=Math.abs(f.scaleX)*x+(p-Math.abs(f.data.scaleX)*x)*l,f.scaleY=Math.abs(f.scaleY)*y+(v-Math.abs(f.data.scaleY)*y)*l}}}},o}(h);t.ScaleTimeline=l;var u=function(t){function i(e){return t.call(this,e)||this}return r(i,t),i.prototype.getPropertyId=function(){return(n.shear<<24)+this.boneIndex},i.prototype.apply=function(t,n,r,a,o,h,l){var u=this.frames,c=t.bones[this.boneIndex];if(c.active)if(r<u[0])switch(h){case e.setup:return c.shearX=c.data.shearX,void(c.shearY=c.data.shearY);case e.first:c.shearX+=(c.data.shearX-c.shearX)*o,c.shearY+=(c.data.shearY-c.shearY)*o}else{var d=0,f=0;if(r>=u[u.length-i.ENTRIES])d=u[u.length+i.PREV_X],f=u[u.length+i.PREV_Y];else{var p=s.binarySearch(u,r,i.ENTRIES);d=u[p+i.PREV_X],f=u[p+i.PREV_Y];var v=u[p],g=this.getCurvePercent(p/i.ENTRIES-1,1-(r-v)/(u[p+i.PREV_TIME]-v));d+=(u[p+i.X]-d)*g,f+=(u[p+i.Y]-f)*g}switch(h){case e.setup:c.shearX=c.data.shearX+d*o,c.shearY=c.data.shearY+f*o;break;case e.first:case e.replace:c.shearX+=(c.data.shearX+d-c.shearX)*o,c.shearY+=(c.data.shearY+f-c.shearY)*o;break;case e.add:c.shearX+=d*o,c.shearY+=f*o}}},i}(h);t.ShearTimeline=u;var c=function(i){function a(e){var n=i.call(this,e)||this;return n.frames=t.Utils.newFloatArray(e*a.ENTRIES),n}return r(a,i),a.prototype.getPropertyId=function(){return(n.color<<24)+this.slotIndex},a.prototype.setFrame=function(t,e,i,n,r,s){t*=a.ENTRIES,this.frames[t]=e,this.frames[t+a.R]=i,this.frames[t+a.G]=n,this.frames[t+a.B]=r,this.frames[t+a.A]=s},a.prototype.apply=function(t,i,n,r,o,h,l){var u=t.slots[this.slotIndex];if(u.bone.active){var c=this.frames;if(n<c[0])switch(h){case e.setup:return void u.color.setFromColor(u.data.color);case e.first:var d=u.color,f=u.data.color;d.add((f.r-d.r)*o,(f.g-d.g)*o,(f.b-d.b)*o,(f.a-d.a)*o)}else{var p=0,v=0,g=0,m=0;if(n>=c[c.length-a.ENTRIES]){var M=c.length;p=c[M+a.PREV_R],v=c[M+a.PREV_G],g=c[M+a.PREV_B],m=c[M+a.PREV_A]}else{var x=s.binarySearch(c,n,a.ENTRIES);p=c[x+a.PREV_R],v=c[x+a.PREV_G],g=c[x+a.PREV_B],m=c[x+a.PREV_A];var y=c[x],w=this.getCurvePercent(x/a.ENTRIES-1,1-(n-y)/(c[x+a.PREV_TIME]-y));p+=(c[x+a.R]-p)*w,v+=(c[x+a.G]-v)*w,g+=(c[x+a.B]-g)*w,m+=(c[x+a.A]-m)*w}if(1==o)u.color.set(p,v,g,m);else{d=u.color;h==e.setup&&d.setFromColor(u.data.color),d.add((p-d.r)*o,(v-d.g)*o,(g-d.b)*o,(m-d.a)*o)}}}},a.ENTRIES=5,a.PREV_TIME=-5,a.PREV_R=-4,a.PREV_G=-3,a.PREV_B=-2,a.PREV_A=-1,a.R=1,a.G=2,a.B=3,a.A=4,a}(a);t.ColorTimeline=c;var d=function(i){function a(e){var n=i.call(this,e)||this;return n.frames=t.Utils.newFloatArray(e*a.ENTRIES),n}return r(a,i),a.prototype.getPropertyId=function(){return(n.twoColor<<24)+this.slotIndex},a.prototype.setFrame=function(t,e,i,n,r,s,o,h,l){t*=a.ENTRIES,this.frames[t]=e,this.frames[t+a.R]=i,this.frames[t+a.G]=n,this.frames[t+a.B]=r,this.frames[t+a.A]=s,this.frames[t+a.R2]=o,this.frames[t+a.G2]=h,this.frames[t+a.B2]=l},a.prototype.apply=function(t,i,n,r,o,h,l){var u=t.slots[this.slotIndex];if(u.bone.active){var c=this.frames;if(n<c[0])switch(h){case e.setup:return u.color.setFromColor(u.data.color),void u.darkColor.setFromColor(u.data.darkColor);case e.first:var d=u.color,f=u.darkColor,p=u.data.color,v=u.data.darkColor;d.add((p.r-d.r)*o,(p.g-d.g)*o,(p.b-d.b)*o,(p.a-d.a)*o),f.add((v.r-f.r)*o,(v.g-f.g)*o,(v.b-f.b)*o,0)}else{var g=0,m=0,M=0,x=0,y=0,w=0,E=0;if(n>=c[c.length-a.ENTRIES]){var b=c.length;g=c[b+a.PREV_R],m=c[b+a.PREV_G],M=c[b+a.PREV_B],x=c[b+a.PREV_A],y=c[b+a.PREV_R2],w=c[b+a.PREV_G2],E=c[b+a.PREV_B2]}else{var T=s.binarySearch(c,n,a.ENTRIES);g=c[T+a.PREV_R],m=c[T+a.PREV_G],M=c[T+a.PREV_B],x=c[T+a.PREV_A],y=c[T+a.PREV_R2],w=c[T+a.PREV_G2],E=c[T+a.PREV_B2];var A=c[T],S=this.getCurvePercent(T/a.ENTRIES-1,1-(n-A)/(c[T+a.PREV_TIME]-A));g+=(c[T+a.R]-g)*S,m+=(c[T+a.G]-m)*S,M+=(c[T+a.B]-M)*S,x+=(c[T+a.A]-x)*S,y+=(c[T+a.R2]-y)*S,w+=(c[T+a.G2]-w)*S,E+=(c[T+a.B2]-E)*S}if(1==o)u.color.set(g,m,M,x),u.darkColor.set(y,w,E,1);else{d=u.color,f=u.darkColor;h==e.setup&&(d.setFromColor(u.data.color),f.setFromColor(u.data.darkColor)),d.add((g-d.r)*o,(m-d.g)*o,(M-d.b)*o,(x-d.a)*o),f.add((y-f.r)*o,(w-f.g)*o,(E-f.b)*o,0)}}}},a.ENTRIES=8,a.PREV_TIME=-8,a.PREV_R=-7,a.PREV_G=-6,a.PREV_B=-5,a.PREV_A=-4,a.PREV_R2=-3,a.PREV_G2=-2,a.PREV_B2=-1,a.R=1,a.G=2,a.B=3,a.A=4,a.R2=5,a.G2=6,a.B2=7,a}(a);t.TwoColorTimeline=d;var f=function(){function r(e){this.frames=t.Utils.newFloatArray(e),this.attachmentNames=new Array(e)}return r.prototype.getPropertyId=function(){return(n.attachment<<24)+this.slotIndex},r.prototype.getFrameCount=function(){return this.frames.length},r.prototype.setFrame=function(t,e,i){this.frames[t]=e,this.attachmentNames[t]=i},r.prototype.apply=function(t,n,r,a,o,h,l){var u=t.slots[this.slotIndex];if(u.bone.active)if(l!=i.mixOut){var c=this.frames;if(r<c[0])h!=e.setup&&h!=e.first||this.setAttachment(t,u,u.data.attachmentName);else{var d=0;d=r>=c[c.length-1]?c.length-1:s.binarySearch(c,r,1)-1;var f=this.attachmentNames[d];t.slots[this.slotIndex].setAttachment(null==f?null:t.getAttachment(this.slotIndex,f))}}else h==e.setup&&this.setAttachment(t,u,u.data.attachmentName)},r.prototype.setAttachment=function(t,e,i){e.setAttachment(null==i?null:t.getAttachment(this.slotIndex,i))},r}();t.AttachmentTimeline=f;var p=null,v=function(i){function a(e){var n=i.call(this,e)||this;return n.frames=t.Utils.newFloatArray(e),n.frameVertices=new Array(e),null==p&&(p=t.Utils.newFloatArray(64)),n}return r(a,i),a.prototype.getPropertyId=function(){return(n.deform<<27)+ +this.attachment.id+this.slotIndex},a.prototype.setFrame=function(t,e,i){this.frames[t]=e,this.frameVertices[t]=i},a.prototype.apply=function(i,n,r,a,o,h,l){var u=i.slots[this.slotIndex];if(u.bone.active){var c=u.getAttachment();if(c instanceof t.VertexAttachment&&c.deformAttachment==this.attachment){var d=u.deform;0==d.length&&(h=e.setup);var f=this.frameVertices,p=f[0].length,v=this.frames;if(r<v[0]){var g=c;switch(h){case e.setup:return void(d.length=0);case e.first:if(1==o){d.length=0;break}var m=t.Utils.setArraySize(d,p);if(null==g.bones)for(var M=g.vertices,x=0;x<p;x++)m[x]+=(M[x]-m[x])*o;else{o=1-o;for(x=0;x<p;x++)m[x]*=o}}}else{var y=t.Utils.setArraySize(d,p);if(r>=v[v.length-1]){var w=f[v.length-1];if(1==o)if(h==e.add)if(null==(g=c).bones){M=g.vertices;for(var E=0;E<p;E++)y[E]+=w[E]-M[E]}else for(var b=0;b<p;b++)y[b]+=w[b];else t.Utils.arrayCopy(w,0,y,0,p);else switch(h){case e.setup:var T=c;if(null==T.bones){M=T.vertices;for(var A=0;A<p;A++){var S=M[A];y[A]=S+(w[A]-S)*o}}else for(var R=0;R<p;R++)y[R]=w[R]*o;break;case e.first:case e.replace:for(var C=0;C<p;C++)y[C]+=(w[C]-y[C])*o;break;case e.add:if(null==(g=c).bones){M=g.vertices;for(var I=0;I<p;I++)y[I]+=(w[I]-M[I])*o}else for(var P=0;P<p;P++)y[P]+=w[P]*o}}else{var L=s.binarySearch(v,r),O=f[L-1],_=f[L],k=v[L],F=this.getCurvePercent(L-1,1-(r-k)/(v[L-1]-k));if(1==o)if(h==e.add)if(null==(g=c).bones){M=g.vertices;for(var D=0;D<p;D++){var V=O[D];y[D]+=V+(_[D]-V)*F-M[D]}}else for(var N=0;N<p;N++){V=O[N];y[N]+=V+(_[N]-V)*F}else for(var B=0;B<p;B++){V=O[B];y[B]=V+(_[B]-V)*F}else switch(h){case e.setup:var X=c;if(null==X.bones){M=X.vertices;for(var U=0;U<p;U++){V=O[U],S=M[U];y[U]=S+(V+(_[U]-V)*F-S)*o}}else for(var Y=0;Y<p;Y++){V=O[Y];y[Y]=(V+(_[Y]-V)*F)*o}break;case e.first:case e.replace:for(var z=0;z<p;z++){V=O[z];y[z]+=(V+(_[z]-V)*F-y[z])*o}break;case e.add:if(null==(g=c).bones){M=g.vertices;for(var W=0;W<p;W++){V=O[W];y[W]+=(V+(_[W]-V)*F-M[W])*o}}else for(var G=0;G<p;G++){V=O[G];y[G]+=(V+(_[G]-V)*F)*o}}}}}}},a}(a);t.DeformTimeline=v;var g=function(){function e(e){this.frames=t.Utils.newFloatArray(e),this.events=new Array(e)}return e.prototype.getPropertyId=function(){return n.event<<24},e.prototype.getFrameCount=function(){return this.frames.length},e.prototype.setFrame=function(t,e){this.frames[t]=e.time,this.events[t]=e},e.prototype.apply=function(t,e,i,n,r,a,o){if(null!=n){var h=this.frames,l=this.frames.length;if(e>i)this.apply(t,e,Number.MAX_VALUE,n,r,a,o),e=-1;else if(e>=h[l-1])return;if(!(i<h[0])){var u=0;if(e<h[0])u=0;else for(var c=h[u=s.binarySearch(h,e)];u>0&&h[u-1]==c;)u--;for(;u<l&&i>=h[u];u++)n.push(this.events[u])}}},e}();t.EventTimeline=g;var m=function(){function r(e){this.frames=t.Utils.newFloatArray(e),this.drawOrders=new Array(e)}return r.prototype.getPropertyId=function(){return n.drawOrder<<24},r.prototype.getFrameCount=function(){return this.frames.length},r.prototype.setFrame=function(t,e,i){this.frames[t]=e,this.drawOrders[t]=i},r.prototype.apply=function(n,r,a,o,h,l,u){var c=n.drawOrder,d=n.slots;if(u!=i.mixOut){var f=this.frames;if(a<f[0])l!=e.setup&&l!=e.first||t.Utils.arrayCopy(n.slots,0,n.drawOrder,0,n.slots.length);else{var p=0;p=a>=f[f.length-1]?f.length-1:s.binarySearch(f,a)-1;var v=this.drawOrders[p];if(null==v)t.Utils.arrayCopy(d,0,c,0,d.length);else for(var g=0,m=v.length;g<m;g++)c[g]=d[v[g]]}}else l==e.setup&&t.Utils.arrayCopy(n.slots,0,n.drawOrder,0,n.slots.length)},r}();t.DrawOrderTimeline=m;var M=function(a){function o(e){var i=a.call(this,e)||this;return i.frames=t.Utils.newFloatArray(e*o.ENTRIES),i}return r(o,a),o.prototype.getPropertyId=function(){return(n.ikConstraint<<24)+this.ikConstraintIndex},o.prototype.setFrame=function(t,e,i,n,r,s,a){t*=o.ENTRIES,this.frames[t]=e,this.frames[t+o.MIX]=i,this.frames[t+o.SOFTNESS]=n,this.frames[t+o.BEND_DIRECTION]=r,this.frames[t+o.COMPRESS]=s?1:0,this.frames[t+o.STRETCH]=a?1:0},o.prototype.apply=function(t,n,r,a,h,l,u){var c=this.frames,d=t.ikConstraints[this.ikConstraintIndex];if(d.active)if(r<c[0])switch(l){case e.setup:return d.mix=d.data.mix,d.softness=d.data.softness,d.bendDirection=d.data.bendDirection,d.compress=d.data.compress,void(d.stretch=d.data.stretch);case e.first:d.mix+=(d.data.mix-d.mix)*h,d.softness+=(d.data.softness-d.softness)*h,d.bendDirection=d.data.bendDirection,d.compress=d.data.compress,d.stretch=d.data.stretch}else if(r>=c[c.length-o.ENTRIES])l==e.setup?(d.mix=d.data.mix+(c[c.length+o.PREV_MIX]-d.data.mix)*h,d.softness=d.data.softness+(c[c.length+o.PREV_SOFTNESS]-d.data.softness)*h,u==i.mixOut?(d.bendDirection=d.data.bendDirection,d.compress=d.data.compress,d.stretch=d.data.stretch):(d.bendDirection=c[c.length+o.PREV_BEND_DIRECTION],d.compress=0!=c[c.length+o.PREV_COMPRESS],d.stretch=0!=c[c.length+o.PREV_STRETCH])):(d.mix+=(c[c.length+o.PREV_MIX]-d.mix)*h,d.softness+=(c[c.length+o.PREV_SOFTNESS]-d.softness)*h,u==i.mixIn&&(d.bendDirection=c[c.length+o.PREV_BEND_DIRECTION],d.compress=0!=c[c.length+o.PREV_COMPRESS],d.stretch=0!=c[c.length+o.PREV_STRETCH]));else{var f=s.binarySearch(c,r,o.ENTRIES),p=c[f+o.PREV_MIX],v=c[f+o.PREV_SOFTNESS],g=c[f],m=this.getCurvePercent(f/o.ENTRIES-1,1-(r-g)/(c[f+o.PREV_TIME]-g));l==e.setup?(d.mix=d.data.mix+(p+(c[f+o.MIX]-p)*m-d.data.mix)*h,d.softness=d.data.softness+(v+(c[f+o.SOFTNESS]-v)*m-d.data.softness)*h,u==i.mixOut?(d.bendDirection=d.data.bendDirection,d.compress=d.data.compress,d.stretch=d.data.stretch):(d.bendDirection=c[f+o.PREV_BEND_DIRECTION],d.compress=0!=c[f+o.PREV_COMPRESS],d.stretch=0!=c[f+o.PREV_STRETCH])):(d.mix+=(p+(c[f+o.MIX]-p)*m-d.mix)*h,d.softness+=(v+(c[f+o.SOFTNESS]-v)*m-d.softness)*h,u==i.mixIn&&(d.bendDirection=c[f+o.PREV_BEND_DIRECTION],d.compress=0!=c[f+o.PREV_COMPRESS],d.stretch=0!=c[f+o.PREV_STRETCH]))}},o.ENTRIES=6,o.PREV_TIME=-6,o.PREV_MIX=-5,o.PREV_SOFTNESS=-4,o.PREV_BEND_DIRECTION=-3,o.PREV_COMPRESS=-2,o.PREV_STRETCH=-1,o.MIX=1,o.SOFTNESS=2,o.BEND_DIRECTION=3,o.COMPRESS=4,o.STRETCH=5,o}(a);t.IkConstraintTimeline=M;var x=function(i){function a(e){var n=i.call(this,e)||this;return n.frames=t.Utils.newFloatArray(e*a.ENTRIES),n}return r(a,i),a.prototype.getPropertyId=function(){return(n.transformConstraint<<24)+this.transformConstraintIndex},a.prototype.setFrame=function(t,e,i,n,r,s){t*=a.ENTRIES,this.frames[t]=e,this.frames[t+a.ROTATE]=i,this.frames[t+a.TRANSLATE]=n,this.frames[t+a.SCALE]=r,this.frames[t+a.SHEAR]=s},a.prototype.apply=function(t,i,n,r,o,h,l){var u=this.frames,c=t.transformConstraints[this.transformConstraintIndex];if(c.active)if(n<u[0]){var d=c.data;switch(h){case e.setup:return c.rotateMix=d.rotateMix,c.translateMix=d.translateMix,c.scaleMix=d.scaleMix,void(c.shearMix=d.shearMix);case e.first:c.rotateMix+=(d.rotateMix-c.rotateMix)*o,c.translateMix+=(d.translateMix-c.translateMix)*o,c.scaleMix+=(d.scaleMix-c.scaleMix)*o,c.shearMix+=(d.shearMix-c.shearMix)*o}}else{var f=0,p=0,v=0,g=0;if(n>=u[u.length-a.ENTRIES]){var m=u.length;f=u[m+a.PREV_ROTATE],p=u[m+a.PREV_TRANSLATE],v=u[m+a.PREV_SCALE],g=u[m+a.PREV_SHEAR]}else{var M=s.binarySearch(u,n,a.ENTRIES);f=u[M+a.PREV_ROTATE],p=u[M+a.PREV_TRANSLATE],v=u[M+a.PREV_SCALE],g=u[M+a.PREV_SHEAR];var x=u[M],y=this.getCurvePercent(M/a.ENTRIES-1,1-(n-x)/(u[M+a.PREV_TIME]-x));f+=(u[M+a.ROTATE]-f)*y,p+=(u[M+a.TRANSLATE]-p)*y,v+=(u[M+a.SCALE]-v)*y,g+=(u[M+a.SHEAR]-g)*y}if(h==e.setup){d=c.data;c.rotateMix=d.rotateMix+(f-d.rotateMix)*o,c.translateMix=d.translateMix+(p-d.translateMix)*o,c.scaleMix=d.scaleMix+(v-d.scaleMix)*o,c.shearMix=d.shearMix+(g-d.shearMix)*o}else c.rotateMix+=(f-c.rotateMix)*o,c.translateMix+=(p-c.translateMix)*o,c.scaleMix+=(v-c.scaleMix)*o,c.shearMix+=(g-c.shearMix)*o}},a.ENTRIES=5,a.PREV_TIME=-5,a.PREV_ROTATE=-4,a.PREV_TRANSLATE=-3,a.PREV_SCALE=-2,a.PREV_SHEAR=-1,a.ROTATE=1,a.TRANSLATE=2,a.SCALE=3,a.SHEAR=4,a}(a);t.TransformConstraintTimeline=x;var y=function(i){function a(e){var n=i.call(this,e)||this;return n.frames=t.Utils.newFloatArray(e*a.ENTRIES),n}return r(a,i),a.prototype.getPropertyId=function(){return(n.pathConstraintPosition<<24)+this.pathConstraintIndex},a.prototype.setFrame=function(t,e,i){t*=a.ENTRIES,this.frames[t]=e,this.frames[t+a.VALUE]=i},a.prototype.apply=function(t,i,n,r,o,h,l){var u=this.frames,c=t.pathConstraints[this.pathConstraintIndex];if(c.active)if(n<u[0])switch(h){case e.setup:return void(c.position=c.data.position);case e.first:c.position+=(c.data.position-c.position)*o}else{var d=0;if(n>=u[u.length-a.ENTRIES])d=u[u.length+a.PREV_VALUE];else{var f=s.binarySearch(u,n,a.ENTRIES);d=u[f+a.PREV_VALUE];var p=u[f],v=this.getCurvePercent(f/a.ENTRIES-1,1-(n-p)/(u[f+a.PREV_TIME]-p));d+=(u[f+a.VALUE]-d)*v}h==e.setup?c.position=c.data.position+(d-c.data.position)*o:c.position+=(d-c.position)*o}},a.ENTRIES=2,a.PREV_TIME=-2,a.PREV_VALUE=-1,a.VALUE=1,a}(a);t.PathConstraintPositionTimeline=y;var w=function(t){function i(e){return t.call(this,e)||this}return r(i,t),i.prototype.getPropertyId=function(){return(n.pathConstraintSpacing<<24)+this.pathConstraintIndex},i.prototype.apply=function(t,n,r,a,o,h,l){var u=this.frames,c=t.pathConstraints[this.pathConstraintIndex];if(c.active)if(r<u[0])switch(h){case e.setup:return void(c.spacing=c.data.spacing);case e.first:c.spacing+=(c.data.spacing-c.spacing)*o}else{var d=0;if(r>=u[u.length-i.ENTRIES])d=u[u.length+i.PREV_VALUE];else{var f=s.binarySearch(u,r,i.ENTRIES);d=u[f+i.PREV_VALUE];var p=u[f],v=this.getCurvePercent(f/i.ENTRIES-1,1-(r-p)/(u[f+i.PREV_TIME]-p));d+=(u[f+i.VALUE]-d)*v}h==e.setup?c.spacing=c.data.spacing+(d-c.data.spacing)*o:c.spacing+=(d-c.spacing)*o}},i}(y);t.PathConstraintSpacingTimeline=w;var E=function(i){function a(e){var n=i.call(this,e)||this;return n.frames=t.Utils.newFloatArray(e*a.ENTRIES),n}return r(a,i),a.prototype.getPropertyId=function(){return(n.pathConstraintMix<<24)+this.pathConstraintIndex},a.prototype.setFrame=function(t,e,i,n){t*=a.ENTRIES,this.frames[t]=e,this.frames[t+a.ROTATE]=i,this.frames[t+a.TRANSLATE]=n},a.prototype.apply=function(t,i,n,r,o,h,l){var u=this.frames,c=t.pathConstraints[this.pathConstraintIndex];if(c.active)if(n<u[0])switch(h){case e.setup:return c.rotateMix=c.data.rotateMix,void(c.translateMix=c.data.translateMix);case e.first:c.rotateMix+=(c.data.rotateMix-c.rotateMix)*o,c.translateMix+=(c.data.translateMix-c.translateMix)*o}else{var d=0,f=0;if(n>=u[u.length-a.ENTRIES])d=u[u.length+a.PREV_ROTATE],f=u[u.length+a.PREV_TRANSLATE];else{var p=s.binarySearch(u,n,a.ENTRIES);d=u[p+a.PREV_ROTATE],f=u[p+a.PREV_TRANSLATE];var v=u[p],g=this.getCurvePercent(p/a.ENTRIES-1,1-(n-v)/(u[p+a.PREV_TIME]-v));d+=(u[p+a.ROTATE]-d)*g,f+=(u[p+a.TRANSLATE]-f)*g}h==e.setup?(c.rotateMix=c.data.rotateMix+(d-c.data.rotateMix)*o,c.translateMix=c.data.translateMix+(f-c.data.translateMix)*o):(c.rotateMix+=(d-c.rotateMix)*o,c.translateMix+=(f-c.translateMix)*o)}},a.ENTRIES=3,a.PREV_TIME=-3,a.PREV_ROTATE=-2,a.PREV_TRANSLATE=-1,a.ROTATE=1,a.TRANSLATE=2,a}(a);t.PathConstraintMixTimeline=E}(n||(n={})),function(t){var e=function(){function e(e){this.tracks=new Array,this.timeScale=1,this.unkeyedState=0,this.events=new Array,this.listeners=new Array,this.queue=new r(this),this.propertyIDs=new t.IntSet,this.animationsChanged=!1,this.trackEntryPool=new t.Pool((function(){return new i})),this.data=e}return e.prototype.update=function(t){t*=this.timeScale;for(var e=this.tracks,i=0,n=e.length;i<n;i++){var r=e[i];if(null!=r){r.animationLast=r.nextAnimationLast,r.trackLast=r.nextTrackLast;var s=t*r.timeScale;if(r.delay>0){if(r.delay-=s,r.delay>0)continue;s=-r.delay,r.delay=0}var a=r.next;if(null!=a){var o=r.trackLast-a.delay;if(o>=0){for(a.delay=0,a.trackTime+=0==r.timeScale?0:(o/r.timeScale+t)*a.timeScale,r.trackTime+=s,this.setCurrent(i,a,!0);null!=a.mixingFrom;)a.mixTime+=t,a=a.mixingFrom;continue}}else if(r.trackLast>=r.trackEnd&&null==r.mixingFrom){e[i]=null,this.queue.end(r),this.disposeNext(r);continue}if(null!=r.mixingFrom&&this.updateMixingFrom(r,t)){var h=r.mixingFrom;for(r.mixingFrom=null,null!=h&&(h.mixingTo=null);null!=h;)this.queue.end(h),h=h.mixingFrom}r.trackTime+=s}}this.queue.drain()},e.prototype.updateMixingFrom=function(t,e){var i=t.mixingFrom;if(null==i)return!0;var n=this.updateMixingFrom(i,e);return i.animationLast=i.nextAnimationLast,i.trackLast=i.nextTrackLast,t.mixTime>0&&t.mixTime>=t.mixDuration?(0!=i.totalAlpha&&0!=t.mixDuration||(t.mixingFrom=i.mixingFrom,null!=i.mixingFrom&&(i.mixingFrom.mixingTo=t),t.interruptAlpha=i.interruptAlpha,this.queue.end(i)),n):(i.trackTime+=e*i.timeScale,t.mixTime+=e,!1)},e.prototype.apply=function(i){if(null==i)throw new Error("skeleton cannot be null.");this.animationsChanged&&this._animationsChanged();for(var n=this.events,r=this.tracks,s=!1,a=0,o=r.length;a<o;a++){var h=r[a];if(!(null==h||h.delay>0)){s=!0;var l=0==a?t.MixBlend.first:h.mixBlend,u=h.alpha;null!=h.mixingFrom?u*=this.applyMixingFrom(h,i,l):h.trackTime>=h.trackEnd&&null==h.next&&(u=0);var c=h.animationLast,d=h.getAnimationTime(),f=h.animation.timelines.length,p=h.animation.timelines;if(0==a&&1==u||l==t.MixBlend.add)for(var v=0;v<f;v++){t.Utils.webkit602BugfixHelper(u,l);var g=p[v];g instanceof t.AttachmentTimeline?this.applyAttachmentTimeline(g,i,d,l,!0):g.apply(i,c,d,n,u,l,t.MixDirection.mixIn)}else{var m=h.timelineMode,M=0==h.timelinesRotation.length;M&&t.Utils.setArraySize(h.timelinesRotation,f<<1,null);var x=h.timelinesRotation;for(v=0;v<f;v++){var y=p[v],w=m[v]==e.SUBSEQUENT?l:t.MixBlend.setup;y instanceof t.RotateTimeline?this.applyRotateTimeline(y,i,d,u,w,x,v<<1,M):y instanceof t.AttachmentTimeline?this.applyAttachmentTimeline(y,i,d,l,!0):(t.Utils.webkit602BugfixHelper(u,l),y.apply(i,c,d,n,u,w,t.MixDirection.mixIn))}}this.queueEvents(h,d),n.length=0,h.nextAnimationLast=d,h.nextTrackLast=h.trackTime}}for(var E=this.unkeyedState+e.SETUP,b=i.slots,T=0,A=i.slots.length;T<A;T++){var S=b[T];if(S.attachmentState==E){var R=S.data.attachmentName;S.setAttachment(null==R?null:i.getAttachment(S.data.index,R))}}return this.unkeyedState+=2,this.queue.drain(),s},e.prototype.applyMixingFrom=function(i,n,r){var s=i.mixingFrom;null!=s.mixingFrom&&this.applyMixingFrom(s,n,r);var a=0;0==i.mixDuration?(a=1,r==t.MixBlend.first&&(r=t.MixBlend.setup)):((a=i.mixTime/i.mixDuration)>1&&(a=1),r!=t.MixBlend.first&&(r=s.mixBlend));var o=a<s.eventThreshold?this.events:null,h=a<s.attachmentThreshold,l=a<s.drawOrderThreshold,u=s.animationLast,c=s.getAnimationTime(),d=s.animation.timelines.length,f=s.animation.timelines,p=s.alpha*i.interruptAlpha,v=p*(1-a);if(r==t.MixBlend.add)for(var g=0;g<d;g++)f[g].apply(n,u,c,o,v,r,t.MixDirection.mixOut);else{var m=s.timelineMode,M=s.timelineHoldMix,x=0==s.timelinesRotation.length;x&&t.Utils.setArraySize(s.timelinesRotation,d<<1,null);var y=s.timelinesRotation;s.totalAlpha=0;for(g=0;g<d;g++){var w=f[g],E=t.MixDirection.mixOut,b=void 0,T=0;switch(m[g]){case e.SUBSEQUENT:if(!l&&w instanceof t.DrawOrderTimeline)continue;b=r,T=v;break;case e.FIRST:b=t.MixBlend.setup,T=v;break;case e.HOLD_SUBSEQUENT:b=r,T=p;break;case e.HOLD_FIRST:b=t.MixBlend.setup,T=p;break;default:b=t.MixBlend.setup;var A=M[g];T=p*Math.max(0,1-A.mixTime/A.mixDuration)}s.totalAlpha+=T,w instanceof t.RotateTimeline?this.applyRotateTimeline(w,n,c,T,b,y,g<<1,x):w instanceof t.AttachmentTimeline?this.applyAttachmentTimeline(w,n,c,b,h):(t.Utils.webkit602BugfixHelper(T,r),l&&w instanceof t.DrawOrderTimeline&&b==t.MixBlend.setup&&(E=t.MixDirection.mixIn),w.apply(n,u,c,o,T,b,E))}}return i.mixDuration>0&&this.queueEvents(s,c),this.events.length=0,s.nextAnimationLast=c,s.nextTrackLast=s.trackTime,a},e.prototype.applyAttachmentTimeline=function(i,n,r,s,a){var o=n.slots[i.slotIndex];if(o.bone.active){var h,l=i.frames;if(r<l[0])s!=t.MixBlend.setup&&s!=t.MixBlend.first||this.setAttachment(n,o,o.data.attachmentName,a);else h=r>=l[l.length-1]?l.length-1:t.Animation.binarySearch(l,r)-1,this.setAttachment(n,o,i.attachmentNames[h],a);o.attachmentState<=this.unkeyedState&&(o.attachmentState=this.unkeyedState+e.SETUP)}},e.prototype.setAttachment=function(t,i,n,r){i.setAttachment(null==n?null:t.getAttachment(i.data.index,n)),r&&(i.attachmentState=this.unkeyedState+e.CURRENT)},e.prototype.applyRotateTimeline=function(e,i,n,r,s,a,o,h){if(h&&(a[o]=0),1!=r){var l=e,u=l.frames,c=i.bones[l.boneIndex];if(c.active){var d=0,f=0;if(n<u[0])switch(s){case t.MixBlend.setup:c.rotation=c.data.rotation;default:return;case t.MixBlend.first:d=c.rotation,f=c.data.rotation}else if(d=s==t.MixBlend.setup?c.data.rotation:c.rotation,n>=u[u.length-t.RotateTimeline.ENTRIES])f=c.data.rotation+u[u.length+t.RotateTimeline.PREV_ROTATION];else{var p=t.Animation.binarySearch(u,n,t.RotateTimeline.ENTRIES),v=u[p+t.RotateTimeline.PREV_ROTATION],g=u[p],m=l.getCurvePercent((p>>1)-1,1-(n-g)/(u[p+t.RotateTimeline.PREV_TIME]-g));f=u[p+t.RotateTimeline.ROTATION]-v,f=v+(f-=360*(16384-(16384.499999999996-f/360|0)))*m+c.data.rotation,f-=360*(16384-(16384.499999999996-f/360|0))}var M=0,x=f-d;if(0==(x-=360*(16384-(16384.499999999996-x/360|0))))M=a[o];else{var y=0,w=0;h?(y=0,w=x):(y=a[o],w=a[o+1]);var E=x>0,b=y>=0;t.MathUtils.signum(w)!=t.MathUtils.signum(x)&&Math.abs(w)<=90&&(Math.abs(y)>180&&(y+=360*t.MathUtils.signum(y)),b=E),M=x+y-y%360,b!=E&&(M+=360*t.MathUtils.signum(y)),a[o]=M}a[o+1]=x,d+=M*r,c.rotation=d-360*(16384-(16384.499999999996-d/360|0))}}else e.apply(i,0,n,null,1,s,t.MixDirection.mixIn)},e.prototype.queueEvents=function(t,e){for(var i=t.animationStart,n=t.animationEnd,r=n-i,s=t.trackLast%r,a=this.events,o=0,h=a.length;o<h;o++){var l=a[o];if(l.time<s)break;l.time>n||this.queue.event(t,l)}for((t.loop?0==r||s>t.trackTime%r:e>=n&&t.animationLast<n)&&this.queue.complete(t);o<h;o++){a[o].time<i||this.queue.event(t,a[o])}},e.prototype.clearTracks=function(){var t=this.queue.drainDisabled;this.queue.drainDisabled=!0;for(var e=0,i=this.tracks.length;e<i;e++)this.clearTrack(e);this.tracks.length=0,this.queue.drainDisabled=t,this.queue.drain()},e.prototype.clearTrack=function(t){if(!(t>=this.tracks.length)){var e=this.tracks[t];if(null!=e){this.queue.end(e),this.disposeNext(e);for(var i=e;;){var n=i.mixingFrom;if(null==n)break;this.queue.end(n),i.mixingFrom=null,i.mixingTo=null,i=n}this.tracks[e.trackIndex]=null,this.queue.drain()}}},e.prototype.setCurrent=function(t,e,i){var n=this.expandToIndex(t);this.tracks[t]=e,null!=n&&(i&&this.queue.interrupt(n),e.mixingFrom=n,n.mixingTo=e,e.mixTime=0,null!=n.mixingFrom&&n.mixDuration>0&&(e.interruptAlpha*=Math.min(1,n.mixTime/n.mixDuration)),n.timelinesRotation.length=0),this.queue.start(e)},e.prototype.setAnimation=function(t,e,i){var n=this.data.skeletonData.findAnimation(e);if(null==n)throw new Error("Animation not found: "+e);return this.setAnimationWith(t,n,i)},e.prototype.setAnimationWith=function(t,e,i){if(null==e)throw new Error("animation cannot be null.");var n=!0,r=this.expandToIndex(t);null!=r&&(-1==r.nextTrackLast?(this.tracks[t]=r.mixingFrom,this.queue.interrupt(r),this.queue.end(r),this.disposeNext(r),r=r.mixingFrom,n=!1):this.disposeNext(r));var s=this.trackEntry(t,e,i,r);return this.setCurrent(t,s,n),this.queue.drain(),s},e.prototype.addAnimation=function(t,e,i,n){var r=this.data.skeletonData.findAnimation(e);if(null==r)throw new Error("Animation not found: "+e);return this.addAnimationWith(t,r,i,n)},e.prototype.addAnimationWith=function(t,e,i,n){if(null==e)throw new Error("animation cannot be null.");var r=this.expandToIndex(t);if(null!=r)for(;null!=r.next;)r=r.next;var s=this.trackEntry(t,e,i,r);if(null==r)this.setCurrent(t,s,!0),this.queue.drain();else if(r.next=s,n<=0){var a=r.animationEnd-r.animationStart;0!=a?(r.loop?n+=a*(1+(r.trackTime/a|0)):n+=Math.max(a,r.trackTime),n-=this.data.getMix(r.animation,e)):n=r.trackTime}return s.delay=n,s},e.prototype.setEmptyAnimation=function(t,i){var n=this.setAnimationWith(t,e.emptyAnimation,!1);return n.mixDuration=i,n.trackEnd=i,n},e.prototype.addEmptyAnimation=function(t,i,n){n<=0&&(n-=i);var r=this.addAnimationWith(t,e.emptyAnimation,!1,n);return r.mixDuration=i,r.trackEnd=i,r},e.prototype.setEmptyAnimations=function(t){var e=this.queue.drainDisabled;this.queue.drainDisabled=!0;for(var i=0,n=this.tracks.length;i<n;i++){var r=this.tracks[i];null!=r&&this.setEmptyAnimation(r.trackIndex,t)}this.queue.drainDisabled=e,this.queue.drain()},e.prototype.expandToIndex=function(e){return e<this.tracks.length?this.tracks[e]:(t.Utils.ensureArrayCapacity(this.tracks,e+1,null),this.tracks.length=e+1,null)},e.prototype.trackEntry=function(e,i,n,r){var s=this.trackEntryPool.obtain();return s.trackIndex=e,s.animation=i,s.loop=n,s.holdPrevious=!1,s.eventThreshold=0,s.attachmentThreshold=0,s.drawOrderThreshold=0,s.animationStart=0,s.animationEnd=i.duration,s.animationLast=-1,s.nextAnimationLast=-1,s.delay=0,s.trackTime=0,s.trackLast=-1,s.nextTrackLast=-1,s.trackEnd=Number.MAX_VALUE,s.timeScale=1,s.alpha=1,s.interruptAlpha=1,s.mixTime=0,s.mixDuration=null==r?0:this.data.getMix(r.animation,i),s.mixBlend=t.MixBlend.replace,s},e.prototype.disposeNext=function(t){for(var e=t.next;null!=e;)this.queue.dispose(e),e=e.next;t.next=null},e.prototype._animationsChanged=function(){this.animationsChanged=!1,this.propertyIDs.clear();for(var e=0,i=this.tracks.length;e<i;e++){var n=this.tracks[e];if(null!=n){for(;null!=n.mixingFrom;)n=n.mixingFrom;do{null!=n.mixingFrom&&n.mixBlend==t.MixBlend.add||this.computeHold(n),n=n.mixingTo}while(null!=n)}}},e.prototype.computeHold=function(i){var n=i.mixingTo,r=i.animation.timelines,s=i.animation.timelines.length,a=t.Utils.setArraySize(i.timelineMode,s);i.timelineHoldMix.length=0;var o=t.Utils.setArraySize(i.timelineHoldMix,s),h=this.propertyIDs;if(null!=n&&n.holdPrevious)for(var l=0;l<s;l++)a[l]=h.add(r[l].getPropertyId())?e.HOLD_FIRST:e.HOLD_SUBSEQUENT;else t:for(l=0;l<s;l++){var u=r[l],c=u.getPropertyId();if(h.add(c))if(null==n||u instanceof t.AttachmentTimeline||u instanceof t.DrawOrderTimeline||u instanceof t.EventTimeline||!n.animation.hasTimeline(c))a[l]=e.FIRST;else{for(var d=n.mixingTo;null!=d;d=d.mixingTo)if(!d.animation.hasTimeline(c)){if(i.mixDuration>0){a[l]=e.HOLD_MIX,o[l]=d;continue t}break}a[l]=e.HOLD_FIRST}else a[l]=e.SUBSEQUENT}},e.prototype.getCurrent=function(t){return t>=this.tracks.length?null:this.tracks[t]},e.prototype.addListener=function(t){if(null==t)throw new Error("listener cannot be null.");this.listeners.push(t)},e.prototype.removeListener=function(t){var e=this.listeners.indexOf(t);e>=0&&this.listeners.splice(e,1)},e.prototype.clearListeners=function(){this.listeners.length=0},e.prototype.clearListenerNotifications=function(){this.queue.clear()},e.emptyAnimation=new t.Animation("<empty>",[],0),e.SUBSEQUENT=0,e.FIRST=1,e.HOLD_SUBSEQUENT=2,e.HOLD_FIRST=3,e.HOLD_MIX=4,e.SETUP=1,e.CURRENT=2,e}();t.AnimationState=e;var i=function(){function e(){this.mixBlend=t.MixBlend.replace,this.timelineMode=new Array,this.timelineHoldMix=new Array,this.timelinesRotation=new Array}return e.prototype.reset=function(){this.next=null,this.mixingFrom=null,this.mixingTo=null,this.animation=null,this.listener=null,this.timelineMode.length=0,this.timelineHoldMix.length=0,this.timelinesRotation.length=0},e.prototype.getAnimationTime=function(){if(this.loop){var t=this.animationEnd-this.animationStart;return 0==t?this.animationStart:this.trackTime%t+this.animationStart}return Math.min(this.trackTime+this.animationStart,this.animationEnd)},e.prototype.setAnimationLast=function(t){this.animationLast=t,this.nextAnimationLast=t},e.prototype.isComplete=function(){return this.trackTime>=this.animationEnd-this.animationStart},e.prototype.resetRotationDirections=function(){this.timelinesRotation.length=0},e}();t.TrackEntry=i;var n,r=function(){function t(t){this.objects=[],this.drainDisabled=!1,this.animState=t}return t.prototype.start=function(t){this.objects.push(n.start),this.objects.push(t),this.animState.animationsChanged=!0},t.prototype.interrupt=function(t){this.objects.push(n.interrupt),this.objects.push(t)},t.prototype.end=function(t){this.objects.push(n.end),this.objects.push(t),this.animState.animationsChanged=!0},t.prototype.dispose=function(t){this.objects.push(n.dispose),this.objects.push(t)},t.prototype.complete=function(t){this.objects.push(n.complete),this.objects.push(t)},t.prototype.event=function(t,e){this.objects.push(n.event),this.objects.push(t),this.objects.push(e)},t.prototype.drain=function(){if(!this.drainDisabled){this.drainDisabled=!0;for(var t=this.objects,e=this.animState.listeners,i=0;i<t.length;i+=2){var r=t[i],s=t[i+1];switch(r){case n.start:null!=s.listener&&s.listener.start&&s.listener.start(s);for(var a=0;a<e.length;a++)e[a].start&&e[a].start(s);break;case n.interrupt:null!=s.listener&&s.listener.interrupt&&s.listener.interrupt(s);for(a=0;a<e.length;a++)e[a].interrupt&&e[a].interrupt(s);break;case n.end:null!=s.listener&&s.listener.end&&s.listener.end(s);for(a=0;a<e.length;a++)e[a].end&&e[a].end(s);case n.dispose:null!=s.listener&&s.listener.dispose&&s.listener.dispose(s);for(a=0;a<e.length;a++)e[a].dispose&&e[a].dispose(s);this.animState.trackEntryPool.free(s);break;case n.complete:null!=s.listener&&s.listener.complete&&s.listener.complete(s);for(a=0;a<e.length;a++)e[a].complete&&e[a].complete(s);break;case n.event:var o=t[2+i++];null!=s.listener&&s.listener.event&&s.listener.event(s,o);for(a=0;a<e.length;a++)e[a].event&&e[a].event(s,o)}}this.clear(),this.drainDisabled=!1}},t.prototype.clear=function(){this.objects.length=0},t}();t.EventQueue=r,function(t){t[t.start=0]="start",t[t.interrupt=1]="interrupt",t[t.end=2]="end",t[t.dispose=3]="dispose",t[t.complete=4]="complete",t[t.event=5]="event"}(n=t.EventType||(t.EventType={}));var s=function(){function t(){}return t.prototype.start=function(t){},t.prototype.interrupt=function(t){},t.prototype.end=function(t){},t.prototype.dispose=function(t){},t.prototype.complete=function(t){},t.prototype.event=function(t,e){},t}();t.AnimationStateAdapter=s}(n||(n={})),function(t){var e=function(){function t(t){if(this.animationToMixTime={},this.defaultMix=0,null==t)throw new Error("skeletonData cannot be null.");this.skeletonData=t}return t.prototype.setMix=function(t,e,i){var n=this.skeletonData.findAnimation(t);if(null==n)throw new Error("Animation not found: "+t);var r=this.skeletonData.findAnimation(e);if(null==r)throw new Error("Animation not found: "+e);this.setMixWith(n,r,i)},t.prototype.setMixWith=function(t,e,i){if(null==t)throw new Error("from cannot be null.");if(null==e)throw new Error("to cannot be null.");var n=t.name+"."+e.name;this.animationToMixTime[n]=i},t.prototype.getMix=function(t,e){var i=t.name+"."+e.name,n=this.animationToMixTime[i];return void 0===n?this.defaultMix:n},t}();t.AnimationStateData=e}(n||(n={})),function(t){var e=function(){function e(t,e){void 0===e&&(e=""),this.assets={},this.errors={},this.toLoad=0,this.loaded=0,this.rawDataUris={},this.textureLoader=t,this.pathPrefix=e}return e.prototype.downloadText=function(t,e,i){var n=new XMLHttpRequest;n.overrideMimeType("text/html"),this.rawDataUris[t]&&(t=this.rawDataUris[t]),n.open("GET",t,!0),n.onload=function(){200==n.status?e(n.responseText):i(n.status,n.responseText)},n.onerror=function(){i(n.status,n.responseText)},n.send()},e.prototype.downloadBinary=function(t,e,i){var n=new XMLHttpRequest;this.rawDataUris[t]&&(t=this.rawDataUris[t]),n.open("GET",t,!0),n.responseType="arraybuffer",n.onload=function(){200==n.status?e(new Uint8Array(n.response)):i(n.status,n.responseText)},n.onerror=function(){i(n.status,n.responseText)},n.send()},e.prototype.setRawDataURI=function(t,e){this.rawDataUris[this.pathPrefix+t]=e},e.prototype.loadBinary=function(t,e,i){var n=this;void 0===e&&(e=null),void 0===i&&(i=null),t=this.pathPrefix+t,this.toLoad++,this.downloadBinary(t,(function(i){n.assets[t]=i,e&&e(t,i),n.toLoad--,n.loaded++}),(function(e,r){n.errors[t]="Couldn't load binary ".concat(t,": status ").concat(status,", ").concat(r),i&&i(t,"Couldn't load binary ".concat(t,": status ").concat(status,", ").concat(r)),n.toLoad--,n.loaded++}))},e.prototype.loadText=function(t,e,i){var n=this;void 0===e&&(e=null),void 0===i&&(i=null),t=this.pathPrefix+t,this.toLoad++,this.downloadText(t,(function(i){n.assets[t]=i,e&&e(t,i),n.toLoad--,n.loaded++}),(function(e,r){n.errors[t]="Couldn't load text ".concat(t,": status ").concat(status,", ").concat(r),i&&i(t,"Couldn't load text ".concat(t,": status ").concat(status,", ").concat(r)),n.toLoad--,n.loaded++}))},e.prototype.loadTexture=function(t,e,i){var n=this;void 0===e&&(e=null),void 0===i&&(i=null);var r=t=this.pathPrefix+t;this.toLoad++;var s=new Image;s.crossOrigin="anonymous",s.onload=function(i){var a=n.textureLoader(s);n.assets[r]=a,n.toLoad--,n.loaded++,e&&e(t,s)},s.onerror=function(e){n.errors[t]="Couldn't load image ".concat(t),n.toLoad--,n.loaded++,i&&i(t,"Couldn't load image ".concat(t))},this.rawDataUris[t]&&(t=this.rawDataUris[t]),s.src=t},e.prototype.loadTextureAtlas=function(e,i,n){var r=this;void 0===i&&(i=null),void 0===n&&(n=null);var s=e.lastIndexOf("/")>=0?e.substring(0,e.lastIndexOf("/")):"";e=this.pathPrefix+e,this.toLoad++,this.downloadText(e,(function(a){var o={count:0},h=new Array;try{new t.TextureAtlas(a,(function(e){h.push(""==s?e:s+"/"+e);var i=document.createElement("img");return i.width=16,i.height=16,new t.FakeTexture(i)}))}catch(t){var l=t;return r.errors[e]="Couldn't load texture atlas ".concat(e,": ").concat(l.message),n&&n(e,"Couldn't load texture atlas ".concat(e,": ").concat(l.message)),r.toLoad--,void r.loaded++}for(var u=function(l){var u=!1;r.loadTexture(l,(function(l,c){if(o.count++,o.count==h.length)if(u)r.errors[e]="Couldn't load texture atlas page ".concat(l,"} of atlas ").concat(e),n&&n(e,"Couldn't load texture atlas page ".concat(l," of atlas ").concat(e)),r.toLoad--,r.loaded++;else try{var d=new t.TextureAtlas(a,(function(t){return r.get(""==s?t:s+"/"+t)}));r.assets[e]=d,i&&i(e,d),r.toLoad--,r.loaded++}catch(t){var f=t;r.errors[e]="Couldn't load texture atlas ".concat(e,": ").concat(f.message),n&&n(e,"Couldn't load texture atlas ".concat(e,": ").concat(f.message)),r.toLoad--,r.loaded++}}),(function(t,i){u=!0,o.count++,o.count==h.length&&(r.errors[e]="Couldn't load texture atlas page ".concat(t,"} of atlas ").concat(e),n&&n(e,"Couldn't load texture atlas page ".concat(t," of atlas ").concat(e)),r.toLoad--,r.loaded++)}))},c=0,d=h;c<d.length;c++){u(d[c])}}),(function(t,i){r.errors[e]="Couldn't load texture atlas ".concat(e,": status ").concat(status,", ").concat(i),n&&n(e,"Couldn't load texture atlas ".concat(e,": status ").concat(status,", ").concat(i)),r.toLoad--,r.loaded++}))},e.prototype.get=function(t){return t=this.pathPrefix+t,this.assets[t]},e.prototype.remove=function(t){t=this.pathPrefix+t;var e=this.assets[t];e.dispose&&e.dispose(),this.assets[t]=null},e.prototype.removeAll=function(){for(var t in this.assets){var e=this.assets[t];e.dispose&&e.dispose()}this.assets={}},e.prototype.isLoadingComplete=function(){return 0==this.toLoad},e.prototype.getToLoad=function(){return this.toLoad},e.prototype.getLoaded=function(){return this.loaded},e.prototype.dispose=function(){this.removeAll()},e.prototype.hasErrors=function(){return Object.keys(this.errors).length>0},e.prototype.getErrors=function(){return this.errors},e}();t.AssetManager=e}(n||(n={})),function(t){var e=function(){function e(t){this.atlas=t}return e.prototype.newRegionAttachment=function(e,i,n){var r=this.atlas.findRegion(n);if(null==r)throw new Error("Region not found in atlas: "+n+" (region attachment: "+i+")");r.renderObject=r;var s=new t.RegionAttachment(i);return s.setRegion(r),s},e.prototype.newMeshAttachment=function(e,i,n){var r=this.atlas.findRegion(n);if(null==r)throw new Error("Region not found in atlas: "+n+" (mesh attachment: "+i+")");r.renderObject=r;var s=new t.MeshAttachment(i);return s.region=r,s},e.prototype.newBoundingBoxAttachment=function(e,i){return new t.BoundingBoxAttachment(i)},e.prototype.newPathAttachment=function(e,i){return new t.PathAttachment(i)},e.prototype.newPointAttachment=function(e,i){return new t.PointAttachment(i)},e.prototype.newClippingAttachment=function(e,i){return new t.ClippingAttachment(i)},e}();t.AtlasAttachmentLoader=e}(n||(n={})),function(t){!function(t){t[t.Normal=0]="Normal",t[t.Additive=1]="Additive",t[t.Multiply=2]="Multiply",t[t.Screen=3]="Screen"}(t.BlendMode||(t.BlendMode={}))}(n||(n={})),function(t){var e=function(){function e(t,e,i){if(this.children=new Array,this.x=0,this.y=0,this.rotation=0,this.scaleX=0,this.scaleY=0,this.shearX=0,this.shearY=0,this.ax=0,this.ay=0,this.arotation=0,this.ascaleX=0,this.ascaleY=0,this.ashearX=0,this.ashearY=0,this.appliedValid=!1,this.a=0,this.b=0,this.c=0,this.d=0,this.worldY=0,this.worldX=0,this.sorted=!1,this.active=!1,null==t)throw new Error("data cannot be null.");if(null==e)throw new Error("skeleton cannot be null.");this.data=t,this.skeleton=e,this.parent=i,this.setToSetupPose()}return e.prototype.isActive=function(){return this.active},e.prototype.update=function(){this.updateWorldTransformWith(this.x,this.y,this.rotation,this.scaleX,this.scaleY,this.shearX,this.shearY)},e.prototype.updateWorldTransform=function(){this.updateWorldTransformWith(this.x,this.y,this.rotation,this.scaleX,this.scaleY,this.shearX,this.shearY)},e.prototype.updateWorldTransformWith=function(e,i,n,r,s,a,o){this.ax=e,this.ay=i,this.arotation=n,this.ascaleX=r,this.ascaleY=s,this.ashearX=a,this.ashearY=o,this.appliedValid=!0;var h=this.parent;if(null==h){var l=this.skeleton,u=n+90+o,c=l.scaleX,d=l.scaleY;return this.a=t.MathUtils.cosDeg(n+a)*r*c,this.b=t.MathUtils.cosDeg(u)*s*c,this.c=t.MathUtils.sinDeg(n+a)*r*d,this.d=t.MathUtils.sinDeg(u)*s*d,this.worldX=e*c+l.x,void(this.worldY=i*d+l.y)}var f=h.a,p=h.b,v=h.c,g=h.d;switch(this.worldX=f*e+p*i+h.worldX,this.worldY=v*e+g*i+h.worldY,this.data.transformMode){case t.TransformMode.Normal:u=n+90+o;var m=t.MathUtils.cosDeg(n+a)*r,M=t.MathUtils.cosDeg(u)*s,x=t.MathUtils.sinDeg(n+a)*r,y=t.MathUtils.sinDeg(u)*s;return this.a=f*m+p*x,this.b=f*M+p*y,this.c=v*m+g*x,void(this.d=v*M+g*y);case t.TransformMode.OnlyTranslation:u=n+90+o;this.a=t.MathUtils.cosDeg(n+a)*r,this.b=t.MathUtils.cosDeg(u)*s,this.c=t.MathUtils.sinDeg(n+a)*r,this.d=t.MathUtils.sinDeg(u)*s;break;case t.TransformMode.NoRotationOrReflection:var w=0;(T=f*f+v*v)>1e-4?(T=Math.abs(f*g-p*v)/T,f/=this.skeleton.scaleX,p=(v/=this.skeleton.scaleY)*T,g=f*T,w=Math.atan2(v,f)*t.MathUtils.radDeg):(f=0,v=0,w=90-Math.atan2(g,p)*t.MathUtils.radDeg);var E=n+a-w,b=n+o-w+90;m=t.MathUtils.cosDeg(E)*r,M=t.MathUtils.cosDeg(b)*s,x=t.MathUtils.sinDeg(E)*r,y=t.MathUtils.sinDeg(b)*s;this.a=f*m-p*x,this.b=f*M-p*y,this.c=v*m+g*x,this.d=v*M+g*y;break;case t.TransformMode.NoScale:case t.TransformMode.NoScaleOrReflection:var T,A=t.MathUtils.cosDeg(n),S=t.MathUtils.sinDeg(n),R=(f*A+p*S)/this.skeleton.scaleX,C=(v*A+g*S)/this.skeleton.scaleY;(T=Math.sqrt(R*R+C*C))>1e-5&&(T=1/T),R*=T,C*=T,T=Math.sqrt(R*R+C*C),this.data.transformMode==t.TransformMode.NoScale&&f*g-p*v<0!=(this.skeleton.scaleX<0!=this.skeleton.scaleY<0)&&(T=-T);var I=Math.PI/2+Math.atan2(C,R),P=Math.cos(I)*T,L=Math.sin(I)*T;m=t.MathUtils.cosDeg(a)*r,M=t.MathUtils.cosDeg(90+o)*s,x=t.MathUtils.sinDeg(a)*r,y=t.MathUtils.sinDeg(90+o)*s;this.a=R*m+P*x,this.b=R*M+P*y,this.c=C*m+L*x,this.d=C*M+L*y}this.a*=this.skeleton.scaleX,this.b*=this.skeleton.scaleX,this.c*=this.skeleton.scaleY,this.d*=this.skeleton.scaleY},e.prototype.setToSetupPose=function(){var t=this.data;this.x=t.x,this.y=t.y,this.rotation=t.rotation,this.scaleX=t.scaleX,this.scaleY=t.scaleY,this.shearX=t.shearX,this.shearY=t.shearY},e.prototype.getWorldRotationX=function(){return Math.atan2(this.c,this.a)*t.MathUtils.radDeg},e.prototype.getWorldRotationY=function(){return Math.atan2(this.d,this.b)*t.MathUtils.radDeg},e.prototype.getWorldScaleX=function(){return Math.sqrt(this.a*this.a+this.c*this.c)},e.prototype.getWorldScaleY=function(){return Math.sqrt(this.b*this.b+this.d*this.d)},e.prototype.updateAppliedTransform=function(){this.appliedValid=!0;var e=this.parent;if(null==e)return this.ax=this.worldX,this.ay=this.worldY,this.arotation=Math.atan2(this.c,this.a)*t.MathUtils.radDeg,this.ascaleX=Math.sqrt(this.a*this.a+this.c*this.c),this.ascaleY=Math.sqrt(this.b*this.b+this.d*this.d),this.ashearX=0,void(this.ashearY=Math.atan2(this.a*this.b+this.c*this.d,this.a*this.d-this.b*this.c)*t.MathUtils.radDeg);var i=e.a,n=e.b,r=e.c,s=e.d,a=1/(i*s-n*r),o=this.worldX-e.worldX,h=this.worldY-e.worldY;this.ax=o*s*a-h*n*a,this.ay=h*i*a-o*r*a;var l=a*s,u=a*i,c=a*n,d=a*r,f=l*this.a-c*this.c,p=l*this.b-c*this.d,v=u*this.c-d*this.a,g=u*this.d-d*this.b;if(this.ashearX=0,this.ascaleX=Math.sqrt(f*f+v*v),this.ascaleX>1e-4){var m=f*g-p*v;this.ascaleY=m/this.ascaleX,this.ashearY=Math.atan2(f*p+v*g,m)*t.MathUtils.radDeg,this.arotation=Math.atan2(v,f)*t.MathUtils.radDeg}else this.ascaleX=0,this.ascaleY=Math.sqrt(p*p+g*g),this.ashearY=0,this.arotation=90-Math.atan2(g,p)*t.MathUtils.radDeg},e.prototype.worldToLocal=function(t){var e=this.a,i=this.b,n=this.c,r=this.d,s=1/(e*r-i*n),a=t.x-this.worldX,o=t.y-this.worldY;return t.x=a*r*s-o*i*s,t.y=o*e*s-a*n*s,t},e.prototype.localToWorld=function(t){var e=t.x,i=t.y;return t.x=e*this.a+i*this.b+this.worldX,t.y=e*this.c+i*this.d+this.worldY,t},e.prototype.worldToLocalRotation=function(e){var i=t.MathUtils.sinDeg(e),n=t.MathUtils.cosDeg(e);return Math.atan2(this.a*i-this.c*n,this.d*n-this.b*i)*t.MathUtils.radDeg+this.rotation-this.shearX},e.prototype.localToWorldRotation=function(e){e-=this.rotation-this.shearX;var i=t.MathUtils.sinDeg(e),n=t.MathUtils.cosDeg(e);return Math.atan2(n*this.c+i*this.d,n*this.a+i*this.b)*t.MathUtils.radDeg},e.prototype.rotateWorld=function(e){var i=this.a,n=this.b,r=this.c,s=this.d,a=t.MathUtils.cosDeg(e),o=t.MathUtils.sinDeg(e);this.a=a*i-o*r,this.b=a*n-o*s,this.c=o*i+a*r,this.d=o*n+a*s,this.appliedValid=!1},e}();t.Bone=e}(n||(n={})),function(t){var e,i=function(i,n,r){if(this.x=0,this.y=0,this.rotation=0,this.scaleX=1,this.scaleY=1,this.shearX=0,this.shearY=0,this.transformMode=e.Normal,this.skinRequired=!1,this.color=new t.Color,i<0)throw new Error("index must be >= 0.");if(null==n)throw new Error("name cannot be null.");this.index=i,this.name=n,this.parent=r};t.BoneData=i,function(t){t[t.Normal=0]="Normal",t[t.OnlyTranslation=1]="OnlyTranslation",t[t.NoRotationOrReflection=2]="NoRotationOrReflection",t[t.NoScale=3]="NoScale",t[t.NoScaleOrReflection=4]="NoScaleOrReflection"}(e=t.TransformMode||(t.TransformMode={}))}(n||(n={})),function(t){var e=function(t,e,i){this.name=t,this.order=e,this.skinRequired=i};t.ConstraintData=e}(n||(n={})),function(t){var e=function(t,e){if(null==e)throw new Error("data cannot be null.");this.time=t,this.data=e};t.Event=e}(n||(n={})),function(t){var e=function(t){this.name=t};t.EventData=e}(n||(n={})),function(t){var e=function(){function e(t,e){if(this.bendDirection=0,this.compress=!1,this.stretch=!1,this.mix=1,this.softness=0,this.active=!1,null==t)throw new Error("data cannot be null.");if(null==e)throw new Error("skeleton cannot be null.");this.data=t,this.mix=t.mix,this.softness=t.softness,this.bendDirection=t.bendDirection,this.compress=t.compress,this.stretch=t.stretch,this.bones=new Array;for(var i=0;i<t.bones.length;i++)this.bones.push(e.findBone(t.bones[i].name));this.target=e.findBone(t.target.name)}return e.prototype.isActive=function(){return this.active},e.prototype.apply=function(){this.update()},e.prototype.update=function(){var t=this.target,e=this.bones;switch(e.length){case 1:this.apply1(e[0],t.worldX,t.worldY,this.compress,this.stretch,this.data.uniform,this.mix);break;case 2:this.apply2(e[0],e[1],t.worldX,t.worldY,this.bendDirection,this.stretch,this.softness,this.mix)}},e.prototype.apply1=function(e,i,n,r,s,a,o){e.appliedValid||e.updateAppliedTransform();var h=e.parent,l=h.a,u=h.b,c=h.c,d=h.d,f=-e.ashearX-e.arotation,p=0,v=0;switch(e.data.transformMode){case t.TransformMode.OnlyTranslation:p=i-e.worldX,v=n-e.worldY;break;case t.TransformMode.NoRotationOrReflection:var g=Math.abs(l*d-u*c)/(l*l+c*c),m=l/e.skeleton.scaleX,M=c/e.skeleton.scaleY;u=-M*g*e.skeleton.scaleX,d=m*g*e.skeleton.scaleY,f+=Math.atan2(M,m)*t.MathUtils.radDeg;default:var x=i-h.worldX,y=n-h.worldY,w=l*d-u*c;p=(x*d-y*u)/w-e.ax,v=(y*l-x*c)/w-e.ay}f+=Math.atan2(v,p)*t.MathUtils.radDeg,e.ascaleX<0&&(f+=180),f>180?f-=360:f<-180&&(f+=360);var E=e.ascaleX,b=e.ascaleY;if(r||s){switch(e.data.transformMode){case t.TransformMode.NoScale:case t.TransformMode.NoScaleOrReflection:p=i-e.worldX,v=n-e.worldY}var T=e.data.length*E,A=Math.sqrt(p*p+v*v);if(r&&A<T||s&&A>T&&T>1e-4)E*=g=(A/T-1)*o+1,a&&(b*=g)}e.updateWorldTransformWith(e.ax,e.ay,e.arotation+f*o,E,b,e.ashearX,e.ashearY)},e.prototype.apply2=function(e,i,n,r,s,a,o,h){if(0!=h){e.appliedValid||e.updateAppliedTransform(),i.appliedValid||i.updateAppliedTransform();var l=e.ax,u=e.ay,c=e.ascaleX,d=c,f=e.ascaleY,p=i.ascaleX,v=0,g=0,m=0;c<0?(c=-c,v=180,m=-1):(v=0,m=1),f<0&&(f=-f,m=-m),p<0?(p=-p,g=180):g=0;var M=i.ax,x=0,y=0,w=0,E=e.a,b=e.b,T=e.c,A=e.d,S=Math.abs(c-f)<=1e-4;S?(y=E*M+b*(x=i.ay)+e.worldX,w=T*M+A*x+e.worldY):(x=0,y=E*M+e.worldX,w=T*M+e.worldY);var R=e.parent;E=R.a,b=R.b,T=R.c;var C,I,P=1/(E*(A=R.d)-b*T),L=y-R.worldX,O=w-R.worldY,_=(L*A-O*b)*P-l,k=(O*E-L*T)*P-u,F=Math.sqrt(_*_+k*k),D=i.data.length*p;if(F<1e-4)return this.apply1(e,n,r,!1,a,!1,h),void i.updateWorldTransformWith(M,x,0,i.ascaleX,i.ascaleY,i.ashearX,i.ashearY);var V=((L=n-R.worldX)*A-(O=r-R.worldY)*b)*P-l,N=(O*E-L*T)*P-u,B=V*V+N*N;if(0!=o){o*=c*(p+1)/2;var X=Math.sqrt(B),U=X-F-D*c+o;if(U>0){var Y=Math.min(1,U/(2*o))-1;B=(V-=(Y=(U-o*(1-Y*Y))/X)*V)*V+(N-=Y*N)*N}}t:if(S){var z=(B-F*F-(D*=c)*D)/(2*F*D);z<-1?z=-1:z>1&&(z=1,a&&(d*=(Math.sqrt(B)/(F+D)-1)*h+1)),I=Math.acos(z)*s,E=F+D*z,b=D*Math.sin(I),C=Math.atan2(N*E-V*b,V*E+N*b)}else{var W=(E=c*D)*E,G=(b=f*D)*b,H=Math.atan2(N,V),q=-2*G*F,j=G-W;if((A=q*q-4*j*(T=G*F*F+W*B-W*G))>=0){var K=Math.sqrt(A);q<0&&(K=-K);var Z=(K=-(q+K)/2)/j,Q=T/K,$=Math.abs(Z)<Math.abs(Q)?Z:Q;if($*$<=B){O=Math.sqrt(B-$*$)*s,C=H-Math.atan2(O,$),I=Math.atan2(O/f,($-F)/c);break t}}var J=t.MathUtils.PI,tt=F-E,et=tt*tt,it=0,nt=0,rt=F+E,st=rt*rt,at=0;(T=-E*F/(W-G))>=-1&&T<=1&&(T=Math.acos(T),(A=(L=E*Math.cos(T)+F)*L+(O=b*Math.sin(T))*O)<et&&(J=T,et=A,tt=L,it=O),A>st&&(nt=T,st=A,rt=L,at=O)),B<=(et+st)/2?(C=H-Math.atan2(it*s,tt),I=J*s):(C=H-Math.atan2(at*s,rt),I=nt*s)}var ot=Math.atan2(x,M)*m,ht=e.arotation;(C=(C-ot)*t.MathUtils.radDeg+v-ht)>180?C-=360:C<-180&&(C+=360),e.updateWorldTransformWith(l,u,ht+C*h,d,e.ascaleY,0,0),ht=i.arotation,(I=((I+ot)*t.MathUtils.radDeg-i.ashearX)*m+g-ht)>180?I-=360:I<-180&&(I+=360),i.updateWorldTransformWith(M,x,ht+I*h,i.ascaleX,i.ascaleY,i.ashearX,i.ashearY)}else i.updateWorldTransform()},e}();t.IkConstraint=e}(n||(n={})),function(t){var e=function(t){function e(e){var i=t.call(this,e,0,!1)||this;return i.bones=new Array,i.bendDirection=1,i.compress=!1,i.stretch=!1,i.uniform=!1,i.mix=1,i.softness=0,i}return r(e,t),e}(t.ConstraintData);t.IkConstraintData=e}(n||(n={})),function(t){var e=function(){function e(t,e){if(this.position=0,this.spacing=0,this.rotateMix=0,this.translateMix=0,this.spaces=new Array,this.positions=new Array,this.world=new Array,this.curves=new Array,this.lengths=new Array,this.segments=new Array,this.active=!1,null==t)throw new Error("data cannot be null.");if(null==e)throw new Error("skeleton cannot be null.");this.data=t,this.bones=new Array;for(var i=0,n=t.bones.length;i<n;i++)this.bones.push(e.findBone(t.bones[i].name));this.target=e.findSlot(t.target.name),this.position=t.position,this.spacing=t.spacing,this.rotateMix=t.rotateMix,this.translateMix=t.translateMix}return e.prototype.isActive=function(){return this.active},e.prototype.apply=function(){this.update()},e.prototype.update=function(){var i=this.target.getAttachment();if(i instanceof t.PathAttachment){var n=this.rotateMix,r=this.translateMix,s=n>0;if(r>0||s){var a=this.data,o=a.spacingMode==t.SpacingMode.Percent,h=a.rotateMode,l=h==t.RotateMode.Tangent,u=h==t.RotateMode.ChainScale,c=this.bones.length,d=l?c:c+1,f=this.bones,p=t.Utils.setArraySize(this.spaces,d),v=null,g=this.spacing;if(u||!o){u&&(v=t.Utils.setArraySize(this.lengths,c));for(var m=a.spacingMode==t.SpacingMode.Length,M=0,x=d-1;M<x;){var y=(L=f[M]).data.length;if(y<e.epsilon)u&&(v[M]=0),p[++M]=0;else if(o){if(u){var w=y*L.a,E=y*L.c,b=Math.sqrt(w*w+E*E);v[M]=b}p[++M]=g}else{w=y*L.a,E=y*L.c;var T=Math.sqrt(w*w+E*E);u&&(v[M]=T),p[++M]=(m?y+g:g)*T/y}}}else for(M=1;M<d;M++)p[M]=g;var A=this.computeWorldPositions(i,d,l,a.positionMode==t.PositionMode.Percent,o),S=A[0],R=A[1],C=a.offsetRotation,I=!1;if(0==C)I=h==t.RotateMode.Chain;else I=!1,C*=(P=this.target.bone).a*P.d-P.b*P.c>0?t.MathUtils.degRad:-t.MathUtils.degRad;M=0;for(var P=3;M<c;M++,P+=3){var L;(L=f[M]).worldX+=(S-L.worldX)*r,L.worldY+=(R-L.worldY)*r;var O=(w=A[P])-S,_=(E=A[P+1])-R;if(u){var k=v[M];if(0!=k){var F=(Math.sqrt(O*O+_*_)/k-1)*n+1;L.a*=F,L.c*=F}}if(S=w,R=E,s){var D=L.a,V=L.b,N=L.c,B=L.d,X=0,U=0,Y=0;if(X=l?A[P-1]:0==p[M+1]?A[P+2]:Math.atan2(_,O),X-=Math.atan2(N,D),I){U=Math.cos(X),Y=Math.sin(X);var z=L.data.length;S+=(z*(U*D-Y*N)-O)*n,R+=(z*(Y*D+U*N)-_)*n}else X+=C;X>t.MathUtils.PI?X-=t.MathUtils.PI2:X<-t.MathUtils.PI&&(X+=t.MathUtils.PI2),X*=n,U=Math.cos(X),Y=Math.sin(X),L.a=U*D-Y*N,L.b=U*V-Y*B,L.c=Y*D+U*N,L.d=Y*V+U*B}L.appliedValid=!1}}}},e.prototype.computeWorldPositions=function(i,n,r,s,a){var o=this.target,h=this.position,l=this.spaces,u=t.Utils.setArraySize(this.positions,3*n+2),c=null,d=i.closed,f=i.worldVerticesLength,p=f/6,v=e.NONE;if(!i.constantSpeed){var g=i.lengths,m=g[p-=d?1:2];if(s&&(h*=m),a)for(var M=1;M<n;M++)l[M]*=m;c=t.Utils.setArraySize(this.world,8);M=0;for(var x=0,y=0;M<n;M++,x+=3){var w=h+=G=l[M];if(d)(w%=m)<0&&(w+=m),y=0;else{if(w<0){v!=e.BEFORE&&(v=e.BEFORE,i.computeWorldVertices(o,2,4,c,0,2)),this.addBeforePosition(w,c,0,u,x);continue}if(w>m){v!=e.AFTER&&(v=e.AFTER,i.computeWorldVertices(o,f-6,4,c,0,2)),this.addAfterPosition(w-m,c,0,u,x);continue}}for(;;y++){var E=g[y];if(!(w>E)){if(0==y)w/=E;else w=(w-(K=g[y-1]))/(E-K);break}}y!=v&&(v=y,d&&y==p?(i.computeWorldVertices(o,f-4,4,c,0,2),i.computeWorldVertices(o,0,4,c,4,2)):i.computeWorldVertices(o,6*y+2,8,c,0,2)),this.addCurvePosition(w,c[0],c[1],c[2],c[3],c[4],c[5],c[6],c[7],u,x,r||M>0&&0==G)}return u}d?(f+=2,c=t.Utils.setArraySize(this.world,f),i.computeWorldVertices(o,2,f-4,c,0,2),i.computeWorldVertices(o,0,2,c,f-4,2),c[f-2]=c[0],c[f-1]=c[1]):(p--,f-=4,c=t.Utils.setArraySize(this.world,f),i.computeWorldVertices(o,2,f,c,0,2));for(var b=t.Utils.setArraySize(this.curves,p),T=0,A=c[0],S=c[1],R=0,C=0,I=0,P=0,L=0,O=0,_=0,k=0,F=0,D=0,V=0,N=0,B=0,X=0,U=(M=0,2);M<p;M++,U+=6)R=c[U],C=c[U+1],I=c[U+2],P=c[U+3],V=2*(_=.1875*(A-2*R+I))+(F=.09375*(3*(R-I)-A+(L=c[U+4]))),N=2*(k=.1875*(S-2*C+P))+(D=.09375*(3*(C-P)-S+(O=c[U+5]))),B=.75*(R-A)+_+.16666667*F,X=.75*(C-S)+k+.16666667*D,T+=Math.sqrt(B*B+X*X),B+=V,X+=N,V+=F,N+=D,T+=Math.sqrt(B*B+X*X),B+=V,X+=N,T+=Math.sqrt(B*B+X*X),B+=V+F,X+=N+D,T+=Math.sqrt(B*B+X*X),b[M]=T,A=L,S=O;if(h*=s?T:T/i.lengths[p-1],a)for(M=1;M<n;M++)l[M]*=T;for(var Y=this.segments,z=0,W=(M=0,x=0,y=0,0);M<n;M++,x+=3){var G;w=h+=G=l[M];if(d)(w%=T)<0&&(w+=T),y=0;else{if(w<0){this.addBeforePosition(w,c,0,u,x);continue}if(w>T){this.addAfterPosition(w-T,c,f-4,u,x);continue}}for(;;y++){var H=b[y];if(!(w>H)){if(0==y)w/=H;else w=(w-(K=b[y-1]))/(H-K);break}}if(y!=v){v=y;var q=6*y;for(A=c[q],S=c[q+1],R=c[q+2],C=c[q+3],I=c[q+4],P=c[q+5],V=2*(_=.03*(A-2*R+I))+(F=.006*(3*(R-I)-A+(L=c[q+6]))),N=2*(k=.03*(S-2*C+P))+(D=.006*(3*(C-P)-S+(O=c[q+7]))),B=.3*(R-A)+_+.16666667*F,X=.3*(C-S)+k+.16666667*D,z=Math.sqrt(B*B+X*X),Y[0]=z,q=1;q<8;q++)B+=V,X+=N,V+=F,N+=D,z+=Math.sqrt(B*B+X*X),Y[q]=z;B+=V,X+=N,z+=Math.sqrt(B*B+X*X),Y[8]=z,B+=V+F,X+=N+D,z+=Math.sqrt(B*B+X*X),Y[9]=z,W=0}for(w*=z;;W++){var j=Y[W];if(!(w>j)){var K;if(0==W)w/=j;else w=W+(w-(K=Y[W-1]))/(j-K);break}}this.addCurvePosition(.1*w,A,S,R,C,I,P,L,O,u,x,r||M>0&&0==G)}return u},e.prototype.addBeforePosition=function(t,e,i,n,r){var s=e[i],a=e[i+1],o=e[i+2]-s,h=e[i+3]-a,l=Math.atan2(h,o);n[r]=s+t*Math.cos(l),n[r+1]=a+t*Math.sin(l),n[r+2]=l},e.prototype.addAfterPosition=function(t,e,i,n,r){var s=e[i+2],a=e[i+3],o=s-e[i],h=a-e[i+1],l=Math.atan2(h,o);n[r]=s+t*Math.cos(l),n[r+1]=a+t*Math.sin(l),n[r+2]=l},e.prototype.addCurvePosition=function(t,e,i,n,r,s,a,o,h,l,u,c){if(0==t||isNaN(t))return l[u]=e,l[u+1]=i,void(l[u+2]=Math.atan2(r-i,n-e));var d=t*t,f=d*t,p=1-t,v=p*p,g=v*p,m=p*t,M=3*m,x=p*M,y=M*t,w=e*g+n*x+s*y+o*f,E=i*g+r*x+a*y+h*f;l[u]=w,l[u+1]=E,c&&(l[u+2]=t<.001?Math.atan2(r-i,n-e):Math.atan2(E-(i*v+r*m*2+a*d),w-(e*v+n*m*2+s*d)))},e.NONE=-1,e.BEFORE=-2,e.AFTER=-3,e.epsilon=1e-5,e}();t.PathConstraint=e}(n||(n={})),function(t){var e=function(t){function e(e){var i=t.call(this,e,0,!1)||this;return i.bones=new Array,i}return r(e,t),e}(t.ConstraintData);t.PathConstraintData=e,function(t){t[t.Fixed=0]="Fixed",t[t.Percent=1]="Percent"}(t.PositionMode||(t.PositionMode={})),function(t){t[t.Length=0]="Length",t[t.Fixed=1]="Fixed",t[t.Percent=2]="Percent"}(t.SpacingMode||(t.SpacingMode={})),function(t){t[t.Tangent=0]="Tangent",t[t.Chain=1]="Chain",t[t.ChainScale=2]="ChainScale"}(t.RotateMode||(t.RotateMode={}))}(n||(n={})),function(t){var e=function(){function t(t){this.toLoad=new Array,this.assets={},this.clientId=t}return t.prototype.loaded=function(){var t=0;for(var e in this.assets)t++;return t},t}(),i=function(){function t(t){void 0===t&&(t=""),this.clientAssets={},this.queuedAssets={},this.rawAssets={},this.errors={},this.pathPrefix=t}return t.prototype.queueAsset=function(t,i,n){var r=this.clientAssets[t];return null==r&&(r=new e(t),this.clientAssets[t]=r),null!==i&&(r.textureLoader=i),r.toLoad.push(n),this.queuedAssets[n]!==n&&(this.queuedAssets[n]=n,!0)},t.prototype.loadText=function(t,e){var i=this;if(e=this.pathPrefix+e,this.queueAsset(t,null,e)){var n=new XMLHttpRequest;n.overrideMimeType("text/html"),n.onreadystatechange=function(){n.readyState==XMLHttpRequest.DONE&&(n.status>=200&&n.status<300?i.rawAssets[e]=n.responseText:i.errors[e]="Couldn't load text ".concat(e,": status ").concat(n.status,", ").concat(n.responseText))},n.open("GET",e,!0),n.send()}},t.prototype.loadJson=function(t,e){var i=this;if(e=this.pathPrefix+e,this.queueAsset(t,null,e)){var n=new XMLHttpRequest;n.overrideMimeType("text/html"),n.onreadystatechange=function(){n.readyState==XMLHttpRequest.DONE&&(n.status>=200&&n.status<300?i.rawAssets[e]=JSON.parse(n.responseText):i.errors[e]="Couldn't load text ".concat(e,": status ").concat(n.status,", ").concat(n.responseText))},n.open("GET",e,!0),n.send()}},t.prototype.loadTexture=function(t,e,i){var n=this;if(i=this.pathPrefix+i,this.queueAsset(t,e,i))if(!!("undefined"==typeof window||"undefined"==typeof navigator||!window.document)&&"undefined"!=typeof importScripts){fetch(i,{mode:"cors"}).then((function(t){return t.ok||(n.errors[i]="Couldn't load image "+i),t.blob()})).then((function(t){return createImageBitmap(t,{premultiplyAlpha:"none",colorSpaceConversion:"none"})})).then((function(t){n.rawAssets[i]=t}))}else{var r=new Image;r.crossOrigin="anonymous",r.onload=function(t){n.rawAssets[i]=r},r.onerror=function(t){n.errors[i]="Couldn't load image ".concat(i)},r.src=i}},t.prototype.get=function(t,e){e=this.pathPrefix+e;var i=this.clientAssets[t];return null==i||i.assets[e]},t.prototype.updateClientAssets=function(t){for(var e=!!("undefined"==typeof window||"undefined"==typeof navigator||!window.document)&&"undefined"!=typeof importScripts,i=0;i<t.toLoad.length;i++){var n=t.toLoad[i],r=t.assets[n];if(null==r){var s=this.rawAssets[n];if(null==s)continue;e?s instanceof ImageBitmap?t.assets[n]=t.textureLoader(s):t.assets[n]=s:s instanceof HTMLImageElement?t.assets[n]=t.textureLoader(s):t.assets[n]=s}}},t.prototype.isLoadingComplete=function(t){var e=this.clientAssets[t];return null==e||(this.updateClientAssets(e),e.toLoad.length==e.loaded())},t.prototype.dispose=function(){},t.prototype.hasErrors=function(){return Object.keys(this.errors).length>0},t.prototype.getErrors=function(){return this.errors},t}();t.SharedAssetManager=i}(n||(n={})),function(t){var e=function(){function e(e){if(this._updateCache=new Array,this.updateCacheReset=new Array,this.time=0,this.scaleX=1,this.scaleY=1,this.x=0,this.y=0,null==e)throw new Error("data cannot be null.");this.data=e,this.bones=new Array;for(var i=0;i<e.bones.length;i++){var n=e.bones[i],r=void 0;if(null==n.parent)r=new t.Bone(n,this,null);else{var s=this.bones[n.parent.index];r=new t.Bone(n,this,s),s.children.push(r)}this.bones.push(r)}this.slots=new Array,this.drawOrder=new Array;for(i=0;i<e.slots.length;i++){var a=e.slots[i],o=(r=this.bones[a.boneData.index],new t.Slot(a,r));this.slots.push(o),this.drawOrder.push(o)}this.ikConstraints=new Array;for(i=0;i<e.ikConstraints.length;i++){var h=e.ikConstraints[i];this.ikConstraints.push(new t.IkConstraint(h,this))}this.transformConstraints=new Array;for(i=0;i<e.transformConstraints.length;i++){var l=e.transformConstraints[i];this.transformConstraints.push(new t.TransformConstraint(l,this))}this.pathConstraints=new Array;for(i=0;i<e.pathConstraints.length;i++){var u=e.pathConstraints[i];this.pathConstraints.push(new t.PathConstraint(u,this))}this.color=new t.Color(1,1,1,1),this.updateCache()}return e.prototype.updateCache=function(){this._updateCache.length=0,this.updateCacheReset.length=0;for(var t=this.bones,e=0,i=t.length;e<i;e++){(r=t[e]).sorted=r.data.skinRequired,r.active=!r.sorted}if(null!=this.skin){var n=this.skin.bones;for(e=0,i=this.skin.bones.length;e<i;e++){var r=this.bones[n[e].index];do{r.sorted=!1,r.active=!0,r=r.parent}while(null!=r)}}var s=this.ikConstraints,a=this.transformConstraints,o=this.pathConstraints,h=s.length,l=a.length,u=o.length,c=h+l+u;t:for(e=0;e<c;e++){for(var d=0;d<h;d++){if((f=s[d]).data.order==e){this.sortIkConstraint(f);continue t}}for(d=0;d<l;d++){if((f=a[d]).data.order==e){this.sortTransformConstraint(f);continue t}}for(d=0;d<u;d++){var f;if((f=o[d]).data.order==e){this.sortPathConstraint(f);continue t}}}for(e=0,i=t.length;e<i;e++)this.sortBone(t[e])},e.prototype.sortIkConstraint=function(e){if(e.active=e.target.isActive()&&(!e.data.skinRequired||null!=this.skin&&t.Utils.contains(this.skin.constraints,e.data,!0)),e.active){var i=e.target;this.sortBone(i);var n=e.bones,r=n[0];if(this.sortBone(r),n.length>1){var s=n[n.length-1];this._updateCache.indexOf(s)>-1||this.updateCacheReset.push(s)}this._updateCache.push(e),this.sortReset(r.children),n[n.length-1].sorted=!0}},e.prototype.sortPathConstraint=function(e){if(e.active=e.target.bone.isActive()&&(!e.data.skinRequired||null!=this.skin&&t.Utils.contains(this.skin.constraints,e.data,!0)),e.active){var i=e.target,n=i.data.index,r=i.bone;null!=this.skin&&this.sortPathConstraintAttachment(this.skin,n,r),null!=this.data.defaultSkin&&this.data.defaultSkin!=this.skin&&this.sortPathConstraintAttachment(this.data.defaultSkin,n,r);for(var s=0,a=this.data.skins.length;s<a;s++)this.sortPathConstraintAttachment(this.data.skins[s],n,r);var o=i.getAttachment();o instanceof t.PathAttachment&&this.sortPathConstraintAttachmentWith(o,r);var h=e.bones,l=h.length;for(s=0;s<l;s++)this.sortBone(h[s]);this._updateCache.push(e);for(s=0;s<l;s++)this.sortReset(h[s].children);for(s=0;s<l;s++)h[s].sorted=!0}},e.prototype.sortTransformConstraint=function(e){if(e.active=e.target.isActive()&&(!e.data.skinRequired||null!=this.skin&&t.Utils.contains(this.skin.constraints,e.data,!0)),e.active){this.sortBone(e.target);var i=e.bones,n=i.length;if(e.data.local)for(var r=0;r<n;r++){var s=i[r];this.sortBone(s.parent),this._updateCache.indexOf(s)>-1||this.updateCacheReset.push(s)}else for(r=0;r<n;r++)this.sortBone(i[r]);this._updateCache.push(e);for(var a=0;a<n;a++)this.sortReset(i[a].children);for(a=0;a<n;a++)i[a].sorted=!0}},e.prototype.sortPathConstraintAttachment=function(t,e,i){var n=t.attachments[e];if(n)for(var r in n)this.sortPathConstraintAttachmentWith(n[r],i)},e.prototype.sortPathConstraintAttachmentWith=function(e,i){if(e instanceof t.PathAttachment){var n=e.bones;if(null==n)this.sortBone(i);else for(var r=this.bones,s=0;s<n.length;)for(var a=n[s++],o=s+a;s<o;s++){var h=n[s];this.sortBone(r[h])}}},e.prototype.sortBone=function(t){if(!t.sorted){var e=t.parent;null!=e&&this.sortBone(e),t.sorted=!0,this._updateCache.push(t)}},e.prototype.sortReset=function(t){for(var e=0,i=t.length;e<i;e++){var n=t[e];n.active&&(n.sorted&&this.sortReset(n.children),n.sorted=!1)}},e.prototype.updateWorldTransform=function(){for(var t=this.updateCacheReset,e=0,i=t.length;e<i;e++){var n=t[e];n.ax=n.x,n.ay=n.y,n.arotation=n.rotation,n.ascaleX=n.scaleX,n.ascaleY=n.scaleY,n.ashearX=n.shearX,n.ashearY=n.shearY,n.appliedValid=!0}var r=this._updateCache;for(e=0,i=r.length;e<i;e++)r[e].update()},e.prototype.setToSetupPose=function(){this.setBonesToSetupPose(),this.setSlotsToSetupPose()},e.prototype.setBonesToSetupPose=function(){for(var t=this.bones,e=0,i=t.length;e<i;e++)t[e].setToSetupPose();var n=this.ikConstraints;for(e=0,i=n.length;e<i;e++){(o=n[e]).mix=o.data.mix,o.softness=o.data.softness,o.bendDirection=o.data.bendDirection,o.compress=o.data.compress,o.stretch=o.data.stretch}var r=this.transformConstraints;for(e=0,i=r.length;e<i;e++){var s=(o=r[e]).data;o.rotateMix=s.rotateMix,o.translateMix=s.translateMix,o.scaleMix=s.scaleMix,o.shearMix=s.shearMix}var a=this.pathConstraints;for(e=0,i=a.length;e<i;e++){var o;s=(o=a[e]).data;o.position=s.position,o.spacing=s.spacing,o.rotateMix=s.rotateMix,o.translateMix=s.translateMix}},e.prototype.setSlotsToSetupPose=function(){var e=this.slots;t.Utils.arrayCopy(e,0,this.drawOrder,0,e.length);for(var i=0,n=e.length;i<n;i++)e[i].setToSetupPose()},e.prototype.getRootBone=function(){return 0==this.bones.length?null:this.bones[0]},e.prototype.findBone=function(t){if(null==t)throw new Error("boneName cannot be null.");for(var e=this.bones,i=0,n=e.length;i<n;i++){var r=e[i];if(r.data.name==t)return r}return null},e.prototype.findBoneIndex=function(t){if(null==t)throw new Error("boneName cannot be null.");for(var e=this.bones,i=0,n=e.length;i<n;i++)if(e[i].data.name==t)return i;return-1},e.prototype.findSlot=function(t){if(null==t)throw new Error("slotName cannot be null.");for(var e=this.slots,i=0,n=e.length;i<n;i++){var r=e[i];if(r.data.name==t)return r}return null},e.prototype.findSlotIndex=function(t){if(null==t)throw new Error("slotName cannot be null.");for(var e=this.slots,i=0,n=e.length;i<n;i++)if(e[i].data.name==t)return i;return-1},e.prototype.setSkinByName=function(t){var e=this.data.findSkin(t);if(null==e)throw new Error("Skin not found: "+t);this.setSkin(e)},e.prototype.setSkin=function(t){if(t!=this.skin){if(null!=t)if(null!=this.skin)t.attachAll(this,this.skin);else for(var e=this.slots,i=0,n=e.length;i<n;i++){var r=e[i],s=r.data.attachmentName;if(null!=s){var a=t.getAttachment(i,s);null!=a&&r.setAttachment(a)}}this.skin=t,this.updateCache()}},e.prototype.getAttachmentByName=function(t,e){return this.getAttachment(this.data.findSlotIndex(t),e)},e.prototype.getAttachment=function(t,e){if(null==e)throw new Error("attachmentName cannot be null.");if(null!=this.skin){var i=this.skin.getAttachment(t,e);if(null!=i)return i}return null!=this.data.defaultSkin?this.data.defaultSkin.getAttachment(t,e):null},e.prototype.setAttachment=function(t,e){if(null==t)throw new Error("slotName cannot be null.");for(var i=this.slots,n=0,r=i.length;n<r;n++){var s=i[n];if(s.data.name==t){var a=null;if(null!=e&&null==(a=this.getAttachment(n,e)))throw new Error("Attachment not found: "+e+", for slot: "+t);return void s.setAttachment(a)}}throw new Error("Slot not found: "+t)},e.prototype.findIkConstraint=function(t){if(null==t)throw new Error("constraintName cannot be null.");for(var e=this.ikConstraints,i=0,n=e.length;i<n;i++){var r=e[i];if(r.data.name==t)return r}return null},e.prototype.findTransformConstraint=function(t){if(null==t)throw new Error("constraintName cannot be null.");for(var e=this.transformConstraints,i=0,n=e.length;i<n;i++){var r=e[i];if(r.data.name==t)return r}return null},e.prototype.findPathConstraint=function(t){if(null==t)throw new Error("constraintName cannot be null.");for(var e=this.pathConstraints,i=0,n=e.length;i<n;i++){var r=e[i];if(r.data.name==t)return r}return null},e.prototype.getBounds=function(e,i,n){if(void 0===n&&(n=new Array(2)),null==e)throw new Error("offset cannot be null.");if(null==i)throw new Error("size cannot be null.");for(var r=this.drawOrder,s=Number.POSITIVE_INFINITY,a=Number.POSITIVE_INFINITY,o=Number.NEGATIVE_INFINITY,h=Number.NEGATIVE_INFINITY,l=0,u=r.length;l<u;l++){var c=r[l];if(c.bone.active){var d=0,f=null,p=c.getAttachment();if(p instanceof t.RegionAttachment)d=8,f=t.Utils.setArraySize(n,d,0),p.computeWorldVertices(c.bone,f,0,2);else if(p instanceof t.MeshAttachment){var v=p;d=v.worldVerticesLength,f=t.Utils.setArraySize(n,d,0),v.computeWorldVertices(c,0,d,f,0,2)}if(null!=f)for(var g=0,m=f.length;g<m;g+=2){var M=f[g],x=f[g+1];s=Math.min(s,M),a=Math.min(a,x),o=Math.max(o,M),h=Math.max(h,x)}}}e.set(s,a),i.set(o-s,h-a)},e.prototype.update=function(t){this.time+=t},e}();t.Skeleton=e}(n||(n={})),function(t){var e=function(){function e(t){this.scale=1,this.linkedMeshes=new Array,this.attachmentLoader=t}return e.prototype.readSkeletonData=function(n){var r=this.scale,s=new t.SkeletonData;s.name="";var a=new i(n);if(s.hash=a.readString(),s.version=a.readString(),"3.8.75"==s.version)throw new Error("Unsupported skeleton data, please export with a newer version of Spine.");s.x=a.readFloat(),s.y=a.readFloat(),s.width=a.readFloat(),s.height=a.readFloat();var o=a.readBoolean();o&&(s.fps=a.readFloat(),s.imagesPath=a.readString(),s.audioPath=a.readString());var h=0;h=a.readInt(!0);for(var l=0;l<h;l++)a.strings.push(a.readString());h=a.readInt(!0);for(l=0;l<h;l++){var u=a.readString(),c=0==l?null:s.bones[a.readInt(!0)];(p=new t.BoneData(l,u,c)).rotation=a.readFloat(),p.x=a.readFloat()*r,p.y=a.readFloat()*r,p.scaleX=a.readFloat(),p.scaleY=a.readFloat(),p.shearX=a.readFloat(),p.shearY=a.readFloat(),p.length=a.readFloat()*r,p.transformMode=e.TransformModeValues[a.readInt(!0)],p.skinRequired=a.readBoolean(),o&&t.Color.rgba8888ToColor(p.color,a.readInt32()),s.bones.push(p)}h=a.readInt(!0);for(l=0;l<h;l++){var d=a.readString(),f=s.bones[a.readInt(!0)],p=new t.SlotData(l,d,f);t.Color.rgba8888ToColor(p.color,a.readInt32());var v=a.readInt32();-1!=v&&t.Color.rgb888ToColor(p.darkColor=new t.Color,v),p.attachmentName=a.readStringRef(),p.blendMode=e.BlendModeValues[a.readInt(!0)],s.slots.push(p)}h=a.readInt(!0);l=0;for(var g=void 0;l<h;l++){(p=new t.IkConstraintData(a.readString())).order=a.readInt(!0),p.skinRequired=a.readBoolean(),g=a.readInt(!0);for(var m=0;m<g;m++)p.bones.push(s.bones[a.readInt(!0)]);p.target=s.bones[a.readInt(!0)],p.mix=a.readFloat(),p.softness=a.readFloat()*r,p.bendDirection=a.readByte(),p.compress=a.readBoolean(),p.stretch=a.readBoolean(),p.uniform=a.readBoolean(),s.ikConstraints.push(p)}h=a.readInt(!0);for(l=0,g=void 0;l<h;l++){(p=new t.TransformConstraintData(a.readString())).order=a.readInt(!0),p.skinRequired=a.readBoolean(),g=a.readInt(!0);for(m=0;m<g;m++)p.bones.push(s.bones[a.readInt(!0)]);p.target=s.bones[a.readInt(!0)],p.local=a.readBoolean(),p.relative=a.readBoolean(),p.offsetRotation=a.readFloat(),p.offsetX=a.readFloat()*r,p.offsetY=a.readFloat()*r,p.offsetScaleX=a.readFloat(),p.offsetScaleY=a.readFloat(),p.offsetShearY=a.readFloat(),p.rotateMix=a.readFloat(),p.translateMix=a.readFloat(),p.scaleMix=a.readFloat(),p.shearMix=a.readFloat(),s.transformConstraints.push(p)}h=a.readInt(!0);for(l=0,g=void 0;l<h;l++){(p=new t.PathConstraintData(a.readString())).order=a.readInt(!0),p.skinRequired=a.readBoolean(),g=a.readInt(!0);for(m=0;m<g;m++)p.bones.push(s.bones[a.readInt(!0)]);p.target=s.slots[a.readInt(!0)],p.positionMode=e.PositionModeValues[a.readInt(!0)],p.spacingMode=e.SpacingModeValues[a.readInt(!0)],p.rotateMode=e.RotateModeValues[a.readInt(!0)],p.offsetRotation=a.readFloat(),p.position=a.readFloat(),p.positionMode==t.PositionMode.Fixed&&(p.position*=r),p.spacing=a.readFloat(),p.spacingMode!=t.SpacingMode.Length&&p.spacingMode!=t.SpacingMode.Fixed||(p.spacing*=r),p.rotateMix=a.readFloat(),p.translateMix=a.readFloat(),s.pathConstraints.push(p)}var M=this.readSkin(a,s,!0,o);null!=M&&(s.defaultSkin=M,s.skins.push(M));l=s.skins.length;for(t.Utils.setArraySize(s.skins,h=l+a.readInt(!0));l<h;l++)s.skins[l]=this.readSkin(a,s,!1,o);h=this.linkedMeshes.length;for(l=0;l<h;l++){var x=this.linkedMeshes[l],y=null==x.skin?s.defaultSkin:s.findSkin(x.skin);if(null==y)throw new Error("Skin not found: "+x.skin);var w=y.getAttachment(x.slotIndex,x.parent);if(null==w)throw new Error("Parent mesh not found: "+x.parent);x.mesh.deformAttachment=x.inheritDeform?w:x.mesh,x.mesh.setParentMesh(w),x.mesh.updateUVs()}this.linkedMeshes.length=0,h=a.readInt(!0);for(l=0;l<h;l++){(p=new t.EventData(a.readStringRef())).intValue=a.readInt(!1),p.floatValue=a.readFloat(),p.stringValue=a.readString(),p.audioPath=a.readString(),null!=p.audioPath&&(p.volume=a.readFloat(),p.balance=a.readFloat()),s.events.push(p)}h=a.readInt(!0);for(l=0;l<h;l++)s.animations.push(this.readAnimation(a,a.readString(),s));return s},e.prototype.readSkin=function(e,i,n,r){var s=null,a=0;if(n){if(0==(a=e.readInt(!0)))return null;s=new t.Skin("default")}else{(s=new t.Skin(e.readStringRef())).bones.length=e.readInt(!0);for(var o=0,h=s.bones.length;o<h;o++)s.bones[o]=i.bones[e.readInt(!0)];for(o=0,h=e.readInt(!0);o<h;o++)s.constraints.push(i.ikConstraints[e.readInt(!0)]);for(o=0,h=e.readInt(!0);o<h;o++)s.constraints.push(i.transformConstraints[e.readInt(!0)]);for(o=0,h=e.readInt(!0);o<h;o++)s.constraints.push(i.pathConstraints[e.readInt(!0)]);a=e.readInt(!0)}for(o=0;o<a;o++)for(var l=e.readInt(!0),u=0,c=e.readInt(!0);u<c;u++){var d=e.readStringRef(),f=this.readAttachment(e,i,s,l,d,r);null!=f&&s.setAttachment(l,d,f)}return s},e.prototype.readAttachment=function(i,r,s,a,o,h){var l=this.scale,u=i.readStringRef();null==u&&(u=o);var c=i.readByte();switch(e.AttachmentTypeValues[c]){case t.AttachmentType.Region:var d=i.readStringRef(),f=i.readFloat(),p=i.readFloat(),v=i.readFloat(),g=i.readFloat(),m=i.readFloat(),M=i.readFloat(),x=i.readFloat(),y=i.readInt32();null==d&&(d=u);var w=this.attachmentLoader.newRegionAttachment(s,u,d);return null==w?null:(w.path=d,w.x=p*l,w.y=v*l,w.scaleX=g,w.scaleY=m,w.rotation=f,w.width=M*l,w.height=x*l,t.Color.rgba8888ToColor(w.color,y),w.updateOffset(),w);case t.AttachmentType.BoundingBox:var E=i.readInt(!0),b=this.readVertices(i,E),T=(y=h?i.readInt32():0,this.attachmentLoader.newBoundingBoxAttachment(s,u));return null==T?null:(T.worldVerticesLength=E<<1,T.vertices=b.vertices,T.bones=b.bones,h&&t.Color.rgba8888ToColor(T.color,y),T);case t.AttachmentType.Mesh:d=i.readStringRef(),y=i.readInt32(),E=i.readInt(!0);var A=this.readFloatArray(i,E<<1,1),S=this.readShortArray(i),R=(b=this.readVertices(i,E),i.readInt(!0)),C=null;M=0,x=0;return h&&(C=this.readShortArray(i),M=i.readFloat(),x=i.readFloat()),null==d&&(d=u),null==(I=this.attachmentLoader.newMeshAttachment(s,u,d))?null:(I.path=d,t.Color.rgba8888ToColor(I.color,y),I.bones=b.bones,I.vertices=b.vertices,I.worldVerticesLength=E<<1,I.triangles=S,I.regionUVs=A,I.updateUVs(),I.hullLength=R<<1,h&&(I.edges=C,I.width=M*l,I.height=x*l),I);case t.AttachmentType.LinkedMesh:d=i.readStringRef(),y=i.readInt32();var I,P=i.readStringRef(),L=i.readStringRef(),O=i.readBoolean();M=0,x=0;return h&&(M=i.readFloat(),x=i.readFloat()),null==d&&(d=u),null==(I=this.attachmentLoader.newMeshAttachment(s,u,d))?null:(I.path=d,t.Color.rgba8888ToColor(I.color,y),h&&(I.width=M*l,I.height=x*l),this.linkedMeshes.push(new n(I,P,a,L,O)),I);case t.AttachmentType.Path:for(var _=i.readBoolean(),k=i.readBoolean(),F=(E=i.readInt(!0),b=this.readVertices(i,E),t.Utils.newArray(E/3,0)),D=0,V=F.length;D<V;D++)F[D]=i.readFloat()*l;y=h?i.readInt32():0;return null==(d=this.attachmentLoader.newPathAttachment(s,u))?null:(d.closed=_,d.constantSpeed=k,d.worldVerticesLength=E<<1,d.vertices=b.vertices,d.bones=b.bones,d.lengths=F,h&&t.Color.rgba8888ToColor(d.color,y),d);case t.AttachmentType.Point:f=i.readFloat(),p=i.readFloat(),v=i.readFloat(),y=h?i.readInt32():0;var N=this.attachmentLoader.newPointAttachment(s,u);return null==N?null:(N.x=p*l,N.y=v*l,N.rotation=f,h&&t.Color.rgba8888ToColor(N.color,y),N);case t.AttachmentType.Clipping:var B=i.readInt(!0),X=(E=i.readInt(!0),b=this.readVertices(i,E),y=h?i.readInt32():0,this.attachmentLoader.newClippingAttachment(s,u));return null==X?null:(X.endSlot=r.slots[B],X.worldVerticesLength=E<<1,X.vertices=b.vertices,X.bones=b.bones,h&&t.Color.rgba8888ToColor(X.color,y),X)}return null},e.prototype.readVertices=function(e,i){var n=i<<1,s=new r,a=this.scale;if(!e.readBoolean())return s.vertices=this.readFloatArray(e,n,a),s;for(var o=new Array,h=new Array,l=0;l<i;l++){var u=e.readInt(!0);h.push(u);for(var c=0;c<u;c++)h.push(e.readInt(!0)),o.push(e.readFloat()*a),o.push(e.readFloat()*a),o.push(e.readFloat())}return s.vertices=t.Utils.toFloatArray(o),s.bones=h,s},e.prototype.readFloatArray=function(t,e,i){var n=new Array(e);if(1==i)for(var r=0;r<e;r++)n[r]=t.readFloat();else for(r=0;r<e;r++)n[r]=t.readFloat()*i;return n},e.prototype.readShortArray=function(t){for(var e=t.readInt(!0),i=new Array(e),n=0;n<e;n++)i[n]=t.readShort();return i},e.prototype.readAnimation=function(i,n,r){for(var s=new Array,a=this.scale,o=0,h=new t.Color,l=new t.Color,u=0,c=i.readInt(!0);u<c;u++)for(var d=i.readInt(!0),f=0,p=i.readInt(!0);f<p;f++){var v=i.readByte(),g=i.readInt(!0);switch(v){case e.SLOT_ATTACHMENT:(y=new t.AttachmentTimeline(g)).slotIndex=d;for(var m=0;m<g;m++)y.setFrame(m,i.readFloat(),i.readStringRef());s.push(y),o=Math.max(o,y.frames[g-1]);break;case e.SLOT_COLOR:(y=new t.ColorTimeline(g)).slotIndex=d;for(m=0;m<g;m++){var M=i.readFloat();t.Color.rgba8888ToColor(h,i.readInt32()),y.setFrame(m,M,h.r,h.g,h.b,h.a),m<g-1&&this.readCurve(i,m,y)}s.push(y),o=Math.max(o,y.frames[(g-1)*t.ColorTimeline.ENTRIES]);break;case e.SLOT_TWO_COLOR:(y=new t.TwoColorTimeline(g)).slotIndex=d;for(m=0;m<g;m++){M=i.readFloat();t.Color.rgba8888ToColor(h,i.readInt32()),t.Color.rgb888ToColor(l,i.readInt32()),y.setFrame(m,M,h.r,h.g,h.b,h.a,l.r,l.g,l.b),m<g-1&&this.readCurve(i,m,y)}s.push(y),o=Math.max(o,y.frames[(g-1)*t.TwoColorTimeline.ENTRIES])}}for(u=0,c=i.readInt(!0);u<c;u++){var x=i.readInt(!0);for(f=0,p=i.readInt(!0);f<p;f++){v=i.readByte(),g=i.readInt(!0);switch(v){case e.BONE_ROTATE:(y=new t.RotateTimeline(g)).boneIndex=x;for(m=0;m<g;m++)y.setFrame(m,i.readFloat(),i.readFloat()),m<g-1&&this.readCurve(i,m,y);s.push(y),o=Math.max(o,y.frames[(g-1)*t.RotateTimeline.ENTRIES]);break;case e.BONE_TRANSLATE:case e.BONE_SCALE:case e.BONE_SHEAR:var y=void 0,w=1;v==e.BONE_SCALE?y=new t.ScaleTimeline(g):v==e.BONE_SHEAR?y=new t.ShearTimeline(g):(y=new t.TranslateTimeline(g),w=a),y.boneIndex=x;for(m=0;m<g;m++)y.setFrame(m,i.readFloat(),i.readFloat()*w,i.readFloat()*w),m<g-1&&this.readCurve(i,m,y);s.push(y),o=Math.max(o,y.frames[(g-1)*t.TranslateTimeline.ENTRIES])}}}for(u=0,c=i.readInt(!0);u<c;u++){var E=i.readInt(!0);g=i.readInt(!0);(y=new t.IkConstraintTimeline(g)).ikConstraintIndex=E;for(m=0;m<g;m++)y.setFrame(m,i.readFloat(),i.readFloat(),i.readFloat()*a,i.readByte(),i.readBoolean(),i.readBoolean()),m<g-1&&this.readCurve(i,m,y);s.push(y),o=Math.max(o,y.frames[(g-1)*t.IkConstraintTimeline.ENTRIES])}for(u=0,c=i.readInt(!0);u<c;u++){E=i.readInt(!0),g=i.readInt(!0);(y=new t.TransformConstraintTimeline(g)).transformConstraintIndex=E;for(m=0;m<g;m++)y.setFrame(m,i.readFloat(),i.readFloat(),i.readFloat(),i.readFloat(),i.readFloat()),m<g-1&&this.readCurve(i,m,y);s.push(y),o=Math.max(o,y.frames[(g-1)*t.TransformConstraintTimeline.ENTRIES])}for(u=0,c=i.readInt(!0);u<c;u++){E=i.readInt(!0);var b=r.pathConstraints[E];for(f=0,p=i.readInt(!0);f<p;f++){v=i.readByte(),g=i.readInt(!0);switch(v){case e.PATH_POSITION:case e.PATH_SPACING:y=void 0,w=1;v==e.PATH_SPACING?(y=new t.PathConstraintSpacingTimeline(g),b.spacingMode!=t.SpacingMode.Length&&b.spacingMode!=t.SpacingMode.Fixed||(w=a)):(y=new t.PathConstraintPositionTimeline(g),b.positionMode==t.PositionMode.Fixed&&(w=a)),y.pathConstraintIndex=E;for(m=0;m<g;m++)y.setFrame(m,i.readFloat(),i.readFloat()*w),m<g-1&&this.readCurve(i,m,y);s.push(y),o=Math.max(o,y.frames[(g-1)*t.PathConstraintPositionTimeline.ENTRIES]);break;case e.PATH_MIX:(y=new t.PathConstraintMixTimeline(g)).pathConstraintIndex=E;for(m=0;m<g;m++)y.setFrame(m,i.readFloat(),i.readFloat(),i.readFloat()),m<g-1&&this.readCurve(i,m,y);s.push(y),o=Math.max(o,y.frames[(g-1)*t.PathConstraintMixTimeline.ENTRIES])}}}for(u=0,c=i.readInt(!0);u<c;u++){var T=r.skins[i.readInt(!0)];for(f=0,p=i.readInt(!0);f<p;f++){d=i.readInt(!0);for(var A=0,S=i.readInt(!0);A<S;A++){var R=T.getAttachment(d,i.readStringRef()),C=null!=R.bones,I=R.vertices,P=C?I.length/3*2:I.length;g=i.readInt(!0);(y=new t.DeformTimeline(g)).slotIndex=d,y.attachment=R;for(m=0;m<g;m++){M=i.readFloat();var L=void 0,O=i.readInt(!0);if(0==O)L=C?t.Utils.newFloatArray(P):I;else{L=t.Utils.newFloatArray(P);var _=i.readInt(!0);if(O+=_,1==a)for(var k=_;k<O;k++)L[k]=i.readFloat();else for(k=_;k<O;k++)L[k]=i.readFloat()*a;if(!C){k=0;for(var F=L.length;k<F;k++)L[k]+=I[k]}}y.setFrame(m,M,L),m<g-1&&this.readCurve(i,m,y)}s.push(y),o=Math.max(o,y.frames[g-1])}}}var D=i.readInt(!0);if(D>0){y=new t.DrawOrderTimeline(D);var V=r.slots.length;for(u=0;u<D;u++){M=i.readFloat();var N=i.readInt(!0),B=t.Utils.newArray(V,0);for(f=V-1;f>=0;f--)B[f]=-1;var X=t.Utils.newArray(V-N,0),U=0,Y=0;for(f=0;f<N;f++){for(d=i.readInt(!0);U!=d;)X[Y++]=U++;B[U+i.readInt(!0)]=U++}for(;U<V;)X[Y++]=U++;for(f=V-1;f>=0;f--)-1==B[f]&&(B[f]=X[--Y]);y.setFrame(u,M,B)}s.push(y),o=Math.max(o,y.frames[D-1])}var z=i.readInt(!0);if(z>0){for(y=new t.EventTimeline(z),u=0;u<z;u++){M=i.readFloat();var W=r.events[i.readInt(!0)],G=new t.Event(M,W);G.intValue=i.readInt(!1),G.floatValue=i.readFloat(),G.stringValue=i.readBoolean()?i.readString():W.stringValue,null!=G.data.audioPath&&(G.volume=i.readFloat(),G.balance=i.readFloat()),y.setFrame(u,G)}s.push(y),o=Math.max(o,y.frames[z-1])}return new t.Animation(n,s,o)},e.prototype.readCurve=function(t,i,n){switch(t.readByte()){case e.CURVE_STEPPED:n.setStepped(i);break;case e.CURVE_BEZIER:this.setCurve(n,i,t.readFloat(),t.readFloat(),t.readFloat(),t.readFloat())}},e.prototype.setCurve=function(t,e,i,n,r,s){t.setCurve(e,i,n,r,s)},e.AttachmentTypeValues=[0,1,2,3,4,5,6],e.TransformModeValues=[t.TransformMode.Normal,t.TransformMode.OnlyTranslation,t.TransformMode.NoRotationOrReflection,t.TransformMode.NoScale,t.TransformMode.NoScaleOrReflection],e.PositionModeValues=[t.PositionMode.Fixed,t.PositionMode.Percent],e.SpacingModeValues=[t.SpacingMode.Length,t.SpacingMode.Fixed,t.SpacingMode.Percent],e.RotateModeValues=[t.RotateMode.Tangent,t.RotateMode.Chain,t.RotateMode.ChainScale],e.BlendModeValues=[t.BlendMode.Normal,t.BlendMode.Additive,t.BlendMode.Multiply,t.BlendMode.Screen],e.BONE_ROTATE=0,e.BONE_TRANSLATE=1,e.BONE_SCALE=2,e.BONE_SHEAR=3,e.SLOT_ATTACHMENT=0,e.SLOT_COLOR=1,e.SLOT_TWO_COLOR=2,e.PATH_POSITION=0,e.PATH_SPACING=1,e.PATH_MIX=2,e.CURVE_LINEAR=0,e.CURVE_STEPPED=1,e.CURVE_BEZIER=2,e}();t.SkeletonBinary=e;var i=function(){function t(t,e,i,n){void 0===e&&(e=new Array),void 0===i&&(i=0),void 0===n&&(n=new DataView(t.buffer)),this.strings=e,this.index=i,this.buffer=n}return t.prototype.readByte=function(){return this.buffer.getInt8(this.index++)},t.prototype.readShort=function(){var t=this.buffer.getInt16(this.index);return this.index+=2,t},t.prototype.readInt32=function(){var t=this.buffer.getInt32(this.index);return this.index+=4,t},t.prototype.readInt=function(t){var e=this.readByte(),i=127&e;return 0!=(128&e)&&(i|=(127&(e=this.readByte()))<<7,0!=(128&e)&&(i|=(127&(e=this.readByte()))<<14,0!=(128&e)&&(i|=(127&(e=this.readByte()))<<21,0!=(128&e)&&(i|=(127&(e=this.readByte()))<<28)))),t?i:i>>>1^-(1&i)},t.prototype.readStringRef=function(){var t=this.readInt(!0);return 0==t?null:this.strings[t-1]},t.prototype.readString=function(){var t=this.readInt(!0);switch(t){case 0:return null;case 1:return""}t--;for(var e="",i=0;i<t;){var n=this.readByte();switch(n>>4){case 12:case 13:e+=String.fromCharCode((31&n)<<6|63&this.readByte()),i+=2;break;case 14:e+=String.fromCharCode((15&n)<<12|(63&this.readByte())<<6|63&this.readByte()),i+=3;break;default:e+=String.fromCharCode(n),i++}}return e},t.prototype.readFloat=function(){var t=this.buffer.getFloat32(this.index);return this.index+=4,t},t.prototype.readBoolean=function(){return 0!=this.readByte()},t}(),n=function(t,e,i,n,r){this.mesh=t,this.skin=e,this.slotIndex=i,this.parent=n,this.inheritDeform=r},r=function(t,e){void 0===t&&(t=null),void 0===e&&(e=null),this.bones=t,this.vertices=e}}(n||(n={})),function(t){var e=function(){function e(){this.minX=0,this.minY=0,this.maxX=0,this.maxY=0,this.boundingBoxes=new Array,this.polygons=new Array,this.polygonPool=new t.Pool((function(){return t.Utils.newFloatArray(16)}))}return e.prototype.update=function(e,i){if(null==e)throw new Error("skeleton cannot be null.");var n=this.boundingBoxes,r=this.polygons,s=this.polygonPool,a=e.slots,o=a.length;n.length=0,s.freeAll(r),r.length=0;for(var h=0;h<o;h++){var l=a[h];if(l.bone.active){var u=l.getAttachment();if(u instanceof t.BoundingBoxAttachment){var c=u;n.push(c);var d=s.obtain();d.length!=c.worldVerticesLength&&(d=t.Utils.newFloatArray(c.worldVerticesLength)),r.push(d),c.computeWorldVertices(l,0,c.worldVerticesLength,d,0,2)}}}i?this.aabbCompute():(this.minX=Number.POSITIVE_INFINITY,this.minY=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.maxY=Number.NEGATIVE_INFINITY)},e.prototype.aabbCompute=function(){for(var t=Number.POSITIVE_INFINITY,e=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY,n=Number.NEGATIVE_INFINITY,r=this.polygons,s=0,a=r.length;s<a;s++)for(var o=r[s],h=o,l=0,u=o.length;l<u;l+=2){var c=h[l],d=h[l+1];t=Math.min(t,c),e=Math.min(e,d),i=Math.max(i,c),n=Math.max(n,d)}this.minX=t,this.minY=e,this.maxX=i,this.maxY=n},e.prototype.aabbContainsPoint=function(t,e){return t>=this.minX&&t<=this.maxX&&e>=this.minY&&e<=this.maxY},e.prototype.aabbIntersectsSegment=function(t,e,i,n){var r=this.minX,s=this.minY,a=this.maxX,o=this.maxY;if(t<=r&&i<=r||e<=s&&n<=s||t>=a&&i>=a||e>=o&&n>=o)return!1;var h=(n-e)/(i-t),l=h*(r-t)+e;if(l>s&&l<o)return!0;if((l=h*(a-t)+e)>s&&l<o)return!0;var u=(s-e)/h+t;return u>r&&u<a||(u=(o-e)/h+t)>r&&u<a},e.prototype.aabbIntersectsSkeleton=function(t){return this.minX<t.maxX&&this.maxX>t.minX&&this.minY<t.maxY&&this.maxY>t.minY},e.prototype.containsPoint=function(t,e){for(var i=this.polygons,n=0,r=i.length;n<r;n++)if(this.containsPointPolygon(i[n],t,e))return this.boundingBoxes[n];return null},e.prototype.containsPointPolygon=function(t,e,i){for(var n=t,r=t.length,s=r-2,a=!1,o=0;o<r;o+=2){var h=n[o+1],l=n[s+1];if(h<i&&l>=i||l<i&&h>=i){var u=n[o];u+(i-h)/(l-h)*(n[s]-u)<e&&(a=!a)}s=o}return a},e.prototype.intersectsSegment=function(t,e,i,n){for(var r=this.polygons,s=0,a=r.length;s<a;s++)if(this.intersectsSegmentPolygon(r[s],t,e,i,n))return this.boundingBoxes[s];return null},e.prototype.intersectsSegmentPolygon=function(t,e,i,n,r){for(var s=t,a=t.length,o=e-n,h=i-r,l=e*r-i*n,u=s[a-2],c=s[a-1],d=0;d<a;d+=2){var f=s[d],p=s[d+1],v=u*p-c*f,g=u-f,m=c-p,M=o*m-h*g,x=(l*g-o*v)/M;if((x>=u&&x<=f||x>=f&&x<=u)&&(x>=e&&x<=n||x>=n&&x<=e)){var y=(l*m-h*v)/M;if((y>=c&&y<=p||y>=p&&y<=c)&&(y>=i&&y<=r||y>=r&&y<=i))return!0}u=f,c=p}return!1},e.prototype.getPolygon=function(t){if(null==t)throw new Error("boundingBox cannot be null.");var e=this.boundingBoxes.indexOf(t);return-1==e?null:this.polygons[e]},e.prototype.getWidth=function(){return this.maxX-this.minX},e.prototype.getHeight=function(){return this.maxY-this.minY},e}();t.SkeletonBounds=e}(n||(n={})),function(t){var e=function(){function e(){this.triangulator=new t.Triangulator,this.clippingPolygon=new Array,this.clipOutput=new Array,this.clippedVertices=new Array,this.clippedTriangles=new Array,this.scratch=new Array}return e.prototype.clipStart=function(i,n){if(null!=this.clipAttachment)return 0;this.clipAttachment=n;var r=n.worldVerticesLength,s=t.Utils.setArraySize(this.clippingPolygon,r);n.computeWorldVertices(i,0,r,s,0,2);var a=this.clippingPolygon;e.makeClockwise(a);for(var o=this.clippingPolygons=this.triangulator.decompose(a,this.triangulator.triangulate(a)),h=0,l=o.length;h<l;h++){var u=o[h];e.makeClockwise(u),u.push(u[0]),u.push(u[1])}return o.length},e.prototype.clipEndWithSlot=function(t){null!=this.clipAttachment&&this.clipAttachment.endSlot==t.data&&this.clipEnd()},e.prototype.clipEnd=function(){null!=this.clipAttachment&&(this.clipAttachment=null,this.clippingPolygons=null,this.clippedVertices.length=0,this.clippedTriangles.length=0,this.clippingPolygon.length=0)},e.prototype.isClipping=function(){return null!=this.clipAttachment},e.prototype.clipTriangles=function(e,i,n,r,s,a,o,h){var l=this.clipOutput,u=this.clippedVertices,c=this.clippedTriangles,d=this.clippingPolygons,f=this.clippingPolygons.length,p=h?12:8,v=0;u.length=0,c.length=0;t:for(var g=0;g<r;g+=3)for(var m=n[g]<<1,M=e[m],x=e[m+1],y=s[m],w=s[m+1],E=e[m=n[g+1]<<1],b=e[m+1],T=s[m],A=s[m+1],S=e[m=n[g+2]<<1],R=e[m+1],C=s[m],I=s[m+1],P=0;P<f;P++){var L=u.length;if(!this.clip(M,x,E,b,S,R,d[P],l)){(X=t.Utils.setArraySize(u,L+3*p))[L]=M,X[L+1]=x,X[L+2]=a.r,X[L+3]=a.g,X[L+4]=a.b,X[L+5]=a.a,h?(X[L+6]=y,X[L+7]=w,X[L+8]=o.r,X[L+9]=o.g,X[L+10]=o.b,X[L+11]=o.a,X[L+12]=E,X[L+13]=b,X[L+14]=a.r,X[L+15]=a.g,X[L+16]=a.b,X[L+17]=a.a,X[L+18]=T,X[L+19]=A,X[L+20]=o.r,X[L+21]=o.g,X[L+22]=o.b,X[L+23]=o.a,X[L+24]=S,X[L+25]=R,X[L+26]=a.r,X[L+27]=a.g,X[L+28]=a.b,X[L+29]=a.a,X[L+30]=C,X[L+31]=I,X[L+32]=o.r,X[L+33]=o.g,X[L+34]=o.b,X[L+35]=o.a):(X[L+6]=y,X[L+7]=w,X[L+8]=E,X[L+9]=b,X[L+10]=a.r,X[L+11]=a.g,X[L+12]=a.b,X[L+13]=a.a,X[L+14]=T,X[L+15]=A,X[L+16]=S,X[L+17]=R,X[L+18]=a.r,X[L+19]=a.g,X[L+20]=a.b,X[L+21]=a.a,X[L+22]=C,X[L+23]=I),L=c.length,(K=t.Utils.setArraySize(c,L+3))[L]=v,K[L+1]=v+1,K[L+2]=v+2,v+=3;continue t}var O=l.length;if(0!=O){for(var _=b-R,k=S-E,F=M-S,D=R-x,V=1/(_*F+k*(x-R)),N=O>>1,B=this.clipOutput,X=t.Utils.setArraySize(u,L+N*p),U=0;U<O;U+=2){var Y=B[U],z=B[U+1];X[L]=Y,X[L+1]=z,X[L+2]=a.r,X[L+3]=a.g,X[L+4]=a.b,X[L+5]=a.a;var W=Y-S,G=z-R,H=(_*W+k*G)*V,q=(D*W+F*G)*V,j=1-H-q;X[L+6]=y*H+T*q+C*j,X[L+7]=w*H+A*q+I*j,h&&(X[L+8]=o.r,X[L+9]=o.g,X[L+10]=o.b,X[L+11]=o.a),L+=p}L=c.length;var K=t.Utils.setArraySize(c,L+3*(N-2));N--;for(U=1;U<N;U++)K[L]=v,K[L+1]=v+U,K[L+2]=v+U+1,L+=3;v+=N+1}}},e.prototype.clip=function(t,e,i,n,r,s,a,o){var h=o,l=!1,u=null;a.length%4>=2?(u=o,o=this.scratch):u=this.scratch,u.length=0,u.push(t),u.push(e),u.push(i),u.push(n),u.push(r),u.push(s),u.push(t),u.push(e),o.length=0;for(var c=a,d=a.length-4,f=0;;f+=2){for(var p=c[f],v=c[f+1],g=c[f+2],m=c[f+3],M=p-g,x=v-m,y=u,w=u.length-2,E=o.length,b=0;b<w;b+=2){var T=y[b],A=y[b+1],S=y[b+2],R=y[b+3],C=M*(R-m)-x*(S-g)>0;if(M*(A-m)-x*(T-g)>0){if(C){o.push(S),o.push(R);continue}var I=(L=R-A)*(g-p)-(O=S-T)*(m-v);if(Math.abs(I)>1e-6){var P=(O*(v-A)-L*(p-T))/I;o.push(p+(g-p)*P),o.push(v+(m-v)*P)}else o.push(p),o.push(v)}else if(C){var L,O;I=(L=R-A)*(g-p)-(O=S-T)*(m-v);if(Math.abs(I)>1e-6){P=(O*(v-A)-L*(p-T))/I;o.push(p+(g-p)*P),o.push(v+(m-v)*P)}else o.push(p),o.push(v);o.push(S),o.push(R)}l=!0}if(E==o.length)return h.length=0,!0;if(o.push(o[0]),o.push(o[1]),f==d)break;var _=o;(o=u).length=0,u=_}if(h!=o){h.length=0;f=0;for(var k=o.length-2;f<k;f++)h[f]=o[f]}else h.length=h.length-2;return l},e.makeClockwise=function(t){for(var e=t,i=t.length,n=e[i-2]*e[1]-e[0]*e[i-1],r=0,s=0,a=0,o=0,h=i-3;o<h;o+=2)r=e[o],s=e[o+1],a=e[o+2],n+=r*e[o+3]-a*s;if(!(n<0)){o=0;var l=i-2;for(h=i>>1;o<h;o+=2){var u=e[o],c=e[o+1],d=l-o;e[o]=e[d],e[o+1]=e[d+1],e[d]=u,e[d+1]=c}}},e}();t.SkeletonClipping=e}(n||(n={})),function(t){var e=function(){function t(){this.bones=new Array,this.slots=new Array,this.skins=new Array,this.events=new Array,this.animations=new Array,this.ikConstraints=new Array,this.transformConstraints=new Array,this.pathConstraints=new Array,this.fps=0}return t.prototype.findBone=function(t){if(null==t)throw new Error("boneName cannot be null.");for(var e=this.bones,i=0,n=e.length;i<n;i++){var r=e[i];if(r.name==t)return r}return null},t.prototype.findBoneIndex=function(t){if(null==t)throw new Error("boneName cannot be null.");for(var e=this.bones,i=0,n=e.length;i<n;i++)if(e[i].name==t)return i;return-1},t.prototype.findSlot=function(t){if(null==t)throw new Error("slotName cannot be null.");for(var e=this.slots,i=0,n=e.length;i<n;i++){var r=e[i];if(r.name==t)return r}return null},t.prototype.findSlotIndex=function(t){if(null==t)throw new Error("slotName cannot be null.");for(var e=this.slots,i=0,n=e.length;i<n;i++)if(e[i].name==t)return i;return-1},t.prototype.findSkin=function(t){if(null==t)throw new Error("skinName cannot be null.");for(var e=this.skins,i=0,n=e.length;i<n;i++){var r=e[i];if(r.name==t)return r}return null},t.prototype.findEvent=function(t){if(null==t)throw new Error("eventDataName cannot be null.");for(var e=this.events,i=0,n=e.length;i<n;i++){var r=e[i];if(r.name==t)return r}return null},t.prototype.findAnimation=function(t){if(null==t)throw new Error("animationName cannot be null.");for(var e=this.animations,i=0,n=e.length;i<n;i++){var r=e[i];if(r.name==t)return r}return null},t.prototype.findIkConstraint=function(t){if(null==t)throw new Error("constraintName cannot be null.");for(var e=this.ikConstraints,i=0,n=e.length;i<n;i++){var r=e[i];if(r.name==t)return r}return null},t.prototype.findTransformConstraint=function(t){if(null==t)throw new Error("constraintName cannot be null.");for(var e=this.transformConstraints,i=0,n=e.length;i<n;i++){var r=e[i];if(r.name==t)return r}return null},t.prototype.findPathConstraint=function(t){if(null==t)throw new Error("constraintName cannot be null.");for(var e=this.pathConstraints,i=0,n=e.length;i<n;i++){var r=e[i];if(r.name==t)return r}return null},t.prototype.findPathConstraintIndex=function(t){if(null==t)throw new Error("pathConstraintName cannot be null.");for(var e=this.pathConstraints,i=0,n=e.length;i<n;i++)if(e[i].name==t)return i;return-1},t}();t.SkeletonData=e}(n||(n={})),function(t){var e=function(){function e(t){this.scale=1,this.linkedMeshes=new Array,this.attachmentLoader=t}return e.prototype.readSkeletonData=function(i){var n=this.scale,r=new t.SkeletonData,s="string"==typeof i?JSON.parse(i):i,a=s.skeleton;if(null!=a){if(r.hash=a.hash,r.version=a.spine,"3.8.75"==r.version)throw new Error("Unsupported skeleton data, please export with a newer version of Spine.");r.x=a.x,r.y=a.y,r.width=a.width,r.height=a.height,r.fps=a.fps,r.imagesPath=a.images}if(s.bones)for(var o=0;o<s.bones.length;o++){var h=s.bones[o],l=null,u=this.getValue(h,"parent",null);if(null!=u&&null==(l=r.findBone(u)))throw new Error("Parent bone not found: "+u);(p=new t.BoneData(r.bones.length,h.name,l)).length=this.getValue(h,"length",0)*n,p.x=this.getValue(h,"x",0)*n,p.y=this.getValue(h,"y",0)*n,p.rotation=this.getValue(h,"rotation",0),p.scaleX=this.getValue(h,"scaleX",1),p.scaleY=this.getValue(h,"scaleY",1),p.shearX=this.getValue(h,"shearX",0),p.shearY=this.getValue(h,"shearY",0),p.transformMode=e.transformModeFromString(this.getValue(h,"transform","normal")),p.skinRequired=this.getValue(h,"skin",!1),r.bones.push(p)}if(s.slots)for(o=0;o<s.slots.length;o++){var c=(S=s.slots[o]).name,d=S.bone,f=r.findBone(d);if(null==f)throw new Error("Slot bone not found: "+d);var p=new t.SlotData(r.slots.length,c,f),v=this.getValue(S,"color",null);null!=v&&p.color.setFromString(v);var g=this.getValue(S,"dark",null);null!=g&&(p.darkColor=new t.Color(1,1,1,1),p.darkColor.setFromString(g)),p.attachmentName=this.getValue(S,"attachment",null),p.blendMode=e.blendModeFromString(this.getValue(S,"blend","normal")),r.slots.push(p)}if(s.ik)for(o=0;o<s.ik.length;o++){var m=s.ik[o];(p=new t.IkConstraintData(m.name)).order=this.getValue(m,"order",0),p.skinRequired=this.getValue(m,"skin",!1);for(var M=0;M<m.bones.length;M++){d=m.bones[M];if(null==(b=r.findBone(d)))throw new Error("IK bone not found: "+d);p.bones.push(b)}var x=m.target;if(p.target=r.findBone(x),null==p.target)throw new Error("IK target bone not found: "+x);p.mix=this.getValue(m,"mix",1),p.softness=this.getValue(m,"softness",0)*n,p.bendDirection=this.getValue(m,"bendPositive",!0)?1:-1,p.compress=this.getValue(m,"compress",!1),p.stretch=this.getValue(m,"stretch",!1),p.uniform=this.getValue(m,"uniform",!1),r.ikConstraints.push(p)}if(s.transform)for(o=0;o<s.transform.length;o++){m=s.transform[o];(p=new t.TransformConstraintData(m.name)).order=this.getValue(m,"order",0),p.skinRequired=this.getValue(m,"skin",!1);for(M=0;M<m.bones.length;M++){d=m.bones[M];if(null==(b=r.findBone(d)))throw new Error("Transform constraint bone not found: "+d);p.bones.push(b)}x=m.target;if(p.target=r.findBone(x),null==p.target)throw new Error("Transform constraint target bone not found: "+x);p.local=this.getValue(m,"local",!1),p.relative=this.getValue(m,"relative",!1),p.offsetRotation=this.getValue(m,"rotation",0),p.offsetX=this.getValue(m,"x",0)*n,p.offsetY=this.getValue(m,"y",0)*n,p.offsetScaleX=this.getValue(m,"scaleX",0),p.offsetScaleY=this.getValue(m,"scaleY",0),p.offsetShearY=this.getValue(m,"shearY",0),p.rotateMix=this.getValue(m,"rotateMix",1),p.translateMix=this.getValue(m,"translateMix",1),p.scaleMix=this.getValue(m,"scaleMix",1),p.shearMix=this.getValue(m,"shearMix",1),r.transformConstraints.push(p)}if(s.path)for(o=0;o<s.path.length;o++){m=s.path[o];(p=new t.PathConstraintData(m.name)).order=this.getValue(m,"order",0),p.skinRequired=this.getValue(m,"skin",!1);for(M=0;M<m.bones.length;M++){d=m.bones[M];if(null==(b=r.findBone(d)))throw new Error("Transform constraint bone not found: "+d);p.bones.push(b)}x=m.target;if(p.target=r.findSlot(x),null==p.target)throw new Error("Path target slot not found: "+x);p.positionMode=e.positionModeFromString(this.getValue(m,"positionMode","percent")),p.spacingMode=e.spacingModeFromString(this.getValue(m,"spacingMode","length")),p.rotateMode=e.rotateModeFromString(this.getValue(m,"rotateMode","tangent")),p.offsetRotation=this.getValue(m,"rotation",0),p.position=this.getValue(m,"position",0),p.positionMode==t.PositionMode.Fixed&&(p.position*=n),p.spacing=this.getValue(m,"spacing",0),p.spacingMode!=t.SpacingMode.Length&&p.spacingMode!=t.SpacingMode.Fixed||(p.spacing*=n),p.rotateMix=this.getValue(m,"rotateMix",1),p.translateMix=this.getValue(m,"translateMix",1),r.pathConstraints.push(p)}if(s.skins)for(o=0;o<s.skins.length;o++){var y=s.skins[o],w=new t.Skin(y.name);if(y.bones)for(var E=0;E<y.bones.length;E++){var b;if(null==(b=r.findBone(y.bones[E])))throw new Error("Skin bone not found: "+y.bones[o]);w.bones.push(b)}if(y.ik)for(E=0;E<y.ik.length;E++){if(null==(T=r.findIkConstraint(y.ik[E])))throw new Error("Skin IK constraint not found: "+y.ik[o]);w.constraints.push(T)}if(y.transform)for(E=0;E<y.transform.length;E++){if(null==(T=r.findTransformConstraint(y.transform[E])))throw new Error("Skin transform constraint not found: "+y.transform[o]);w.constraints.push(T)}if(y.path)for(E=0;E<y.path.length;E++){var T;if(null==(T=r.findPathConstraint(y.path[E])))throw new Error("Skin path constraint not found: "+y.path[o]);w.constraints.push(T)}for(var c in y.attachments){var A=r.findSlot(c);if(null==A)throw new Error("Slot not found: "+c);var S=y.attachments[c];for(var R in S){var C=this.readAttachment(S[R],w,A.index,R,r);null!=C&&w.setAttachment(A.index,R,C)}}r.skins.push(w),"default"==w.name&&(r.defaultSkin=w)}o=0;for(var I=this.linkedMeshes.length;o<I;o++){var P=this.linkedMeshes[o];if(null==(w=null==P.skin?r.defaultSkin:r.findSkin(P.skin)))throw new Error("Skin not found: "+P.skin);var L=w.getAttachment(P.slotIndex,P.parent);if(null==L)throw new Error("Parent mesh not found: "+P.parent);P.mesh.deformAttachment=P.inheritDeform?L:P.mesh,P.mesh.setParentMesh(L),P.mesh.updateUVs()}if(this.linkedMeshes.length=0,s.events)for(var O in s.events){var _=s.events[O];(p=new t.EventData(O)).intValue=this.getValue(_,"int",0),p.floatValue=this.getValue(_,"float",0),p.stringValue=this.getValue(_,"string",""),p.audioPath=this.getValue(_,"audio",null),null!=p.audioPath&&(p.volume=this.getValue(_,"volume",1),p.balance=this.getValue(_,"balance",0)),r.events.push(p)}if(s.animations)for(var k in s.animations){var F=s.animations[k];this.readAnimation(F,k,r)}return r},e.prototype.readAttachment=function(e,n,r,s,a){var o=this.scale;switch(s=this.getValue(e,"name",s),this.getValue(e,"type","region")){case"region":var h=this.getValue(e,"path",s),l=this.attachmentLoader.newRegionAttachment(n,s,h);return null==l?null:(l.path=h,l.x=this.getValue(e,"x",0)*o,l.y=this.getValue(e,"y",0)*o,l.scaleX=this.getValue(e,"scaleX",1),l.scaleY=this.getValue(e,"scaleY",1),l.rotation=this.getValue(e,"rotation",0),l.width=e.width*o,l.height=e.height*o,null!=(w=this.getValue(e,"color",null))&&l.color.setFromString(w),l.updateOffset(),l);case"boundingbox":var u=this.attachmentLoader.newBoundingBoxAttachment(n,s);return null==u?null:(this.readVertices(e,u,e.vertexCount<<1),null!=(w=this.getValue(e,"color",null))&&u.color.setFromString(w),u);case"mesh":case"linkedmesh":h=this.getValue(e,"path",s);var c=this.attachmentLoader.newMeshAttachment(n,s,h);if(null==c)return null;c.path=h,null!=(w=this.getValue(e,"color",null))&&c.color.setFromString(w),c.width=this.getValue(e,"width",0)*o,c.height=this.getValue(e,"height",0)*o;var d=this.getValue(e,"parent",null);if(null!=d)return this.linkedMeshes.push(new i(c,this.getValue(e,"skin",null),r,d,this.getValue(e,"deform",!0))),c;var f=e.uvs;return this.readVertices(e,c,f.length),c.triangles=e.triangles,c.regionUVs=f,c.updateUVs(),c.edges=this.getValue(e,"edges",null),c.hullLength=2*this.getValue(e,"hull",0),c;case"path":if(null==(h=this.attachmentLoader.newPathAttachment(n,s)))return null;h.closed=this.getValue(e,"closed",!1),h.constantSpeed=this.getValue(e,"constantSpeed",!0);var p=e.vertexCount;this.readVertices(e,h,p<<1);for(var v=t.Utils.newArray(p/3,0),g=0;g<e.lengths.length;g++)v[g]=e.lengths[g]*o;return h.lengths=v,null!=(w=this.getValue(e,"color",null))&&h.color.setFromString(w),h;case"point":var m=this.attachmentLoader.newPointAttachment(n,s);return null==m?null:(m.x=this.getValue(e,"x",0)*o,m.y=this.getValue(e,"y",0)*o,m.rotation=this.getValue(e,"rotation",0),null!=(w=this.getValue(e,"color",null))&&m.color.setFromString(w),m);case"clipping":var M=this.attachmentLoader.newClippingAttachment(n,s);if(null==M)return null;var x=this.getValue(e,"end",null);if(null!=x){var y=a.findSlot(x);if(null==y)throw new Error("Clipping end slot not found: "+x);M.endSlot=y}var w;p=e.vertexCount;return this.readVertices(e,M,p<<1),null!=(w=this.getValue(e,"color",null))&&M.color.setFromString(w),M}return null},e.prototype.readVertices=function(e,i,n){var r=this.scale;i.worldVerticesLength=n;var s=e.vertices;if(n!=s.length){var a=new Array,o=new Array;for(c=0,d=s.length;c<d;){var h=s[c++];o.push(h);for(var l=c+4*h;c<l;c+=4)o.push(s[c]),a.push(s[c+1]*r),a.push(s[c+2]*r),a.push(s[c+3])}i.bones=o,i.vertices=t.Utils.toFloatArray(a)}else{var u=t.Utils.toFloatArray(s);if(1!=r)for(var c=0,d=s.length;c<d;c++)u[c]*=r;i.vertices=u}},e.prototype.readAnimation=function(e,i,n){var r=this.scale,s=new Array,a=0;if(e.slots)for(var o in e.slots){var h=e.slots[o];if(-1==(Z=n.findSlotIndex(o)))throw new Error("Slot not found: "+o);for(var l in h){var u=h[l];if("attachment"==l){(y=new t.AttachmentTimeline(u.length)).slotIndex=Z;for(var c=0,d=0;d<u.length;d++){var f=u[d];y.setFrame(c++,this.getValue(f,"time",0),f.name)}s.push(y),a=Math.max(a,y.frames[y.getFrameCount()-1])}else if("color"==l){(y=new t.ColorTimeline(u.length)).slotIndex=Z;for(c=0,d=0;d<u.length;d++){f=u[d];var p=new t.Color;p.setFromString(f.color),y.setFrame(c,this.getValue(f,"time",0),p.r,p.g,p.b,p.a),this.readCurve(f,y,c),c++}s.push(y),a=Math.max(a,y.frames[(y.getFrameCount()-1)*t.ColorTimeline.ENTRIES])}else{if("twoColor"!=l)throw new Error("Invalid timeline type for a slot: "+l+" ("+o+")");(y=new t.TwoColorTimeline(u.length)).slotIndex=Z;for(c=0,d=0;d<u.length;d++){f=u[d];var v=new t.Color,g=new t.Color;v.setFromString(f.light),g.setFromString(f.dark),y.setFrame(c,this.getValue(f,"time",0),v.r,v.g,v.b,v.a,g.r,g.g,g.b),this.readCurve(f,y,c),c++}s.push(y),a=Math.max(a,y.frames[(y.getFrameCount()-1)*t.TwoColorTimeline.ENTRIES])}}}if(e.bones)for(var m in e.bones){var M=e.bones[m],x=n.findBoneIndex(m);if(-1==x)throw new Error("Bone not found: "+m);for(var l in M){u=M[l];if("rotate"===l){(y=new t.RotateTimeline(u.length)).boneIndex=x;for(c=0,d=0;d<u.length;d++){f=u[d];y.setFrame(c,this.getValue(f,"time",0),this.getValue(f,"angle",0)),this.readCurve(f,y,c),c++}s.push(y),a=Math.max(a,y.frames[(y.getFrameCount()-1)*t.RotateTimeline.ENTRIES])}else{if("translate"!==l&&"scale"!==l&&"shear"!==l)throw new Error("Invalid timeline type for a bone: "+l+" ("+m+")");var y=null,w=1,E=0;"scale"===l?(y=new t.ScaleTimeline(u.length),E=1):"shear"===l?y=new t.ShearTimeline(u.length):(y=new t.TranslateTimeline(u.length),w=r),y.boneIndex=x;for(c=0,d=0;d<u.length;d++){f=u[d];var b=this.getValue(f,"x",E),T=this.getValue(f,"y",E);y.setFrame(c,this.getValue(f,"time",0),b*w,T*w),this.readCurve(f,y,c),c++}s.push(y),a=Math.max(a,y.frames[(y.getFrameCount()-1)*t.TranslateTimeline.ENTRIES])}}}if(e.ik)for(var A in e.ik){var S=e.ik[A],R=n.findIkConstraint(A);(y=new t.IkConstraintTimeline(S.length)).ikConstraintIndex=n.ikConstraints.indexOf(R);for(c=0,d=0;d<S.length;d++){f=S[d];y.setFrame(c,this.getValue(f,"time",0),this.getValue(f,"mix",1),this.getValue(f,"softness",0)*r,this.getValue(f,"bendPositive",!0)?1:-1,this.getValue(f,"compress",!1),this.getValue(f,"stretch",!1)),this.readCurve(f,y,c),c++}s.push(y),a=Math.max(a,y.frames[(y.getFrameCount()-1)*t.IkConstraintTimeline.ENTRIES])}if(e.transform)for(var A in e.transform){S=e.transform[A],R=n.findTransformConstraint(A);(y=new t.TransformConstraintTimeline(S.length)).transformConstraintIndex=n.transformConstraints.indexOf(R);for(c=0,d=0;d<S.length;d++){f=S[d];y.setFrame(c,this.getValue(f,"time",0),this.getValue(f,"rotateMix",1),this.getValue(f,"translateMix",1),this.getValue(f,"scaleMix",1),this.getValue(f,"shearMix",1)),this.readCurve(f,y,c),c++}s.push(y),a=Math.max(a,y.frames[(y.getFrameCount()-1)*t.TransformConstraintTimeline.ENTRIES])}if(e.path)for(var A in e.path){S=e.path[A];var C=n.findPathConstraintIndex(A);if(-1==C)throw new Error("Path constraint not found: "+A);var I=n.pathConstraints[C];for(var l in S){u=S[l];if("position"===l||"spacing"===l){y=null,w=1;"spacing"===l?(y=new t.PathConstraintSpacingTimeline(u.length),I.spacingMode!=t.SpacingMode.Length&&I.spacingMode!=t.SpacingMode.Fixed||(w=r)):(y=new t.PathConstraintPositionTimeline(u.length),I.positionMode==t.PositionMode.Fixed&&(w=r)),y.pathConstraintIndex=C;for(c=0,d=0;d<u.length;d++){f=u[d];y.setFrame(c,this.getValue(f,"time",0),this.getValue(f,l,0)*w),this.readCurve(f,y,c),c++}s.push(y),a=Math.max(a,y.frames[(y.getFrameCount()-1)*t.PathConstraintPositionTimeline.ENTRIES])}else if("mix"===l){(y=new t.PathConstraintMixTimeline(u.length)).pathConstraintIndex=C;for(c=0,d=0;d<u.length;d++){f=u[d];y.setFrame(c,this.getValue(f,"time",0),this.getValue(f,"rotateMix",1),this.getValue(f,"translateMix",1)),this.readCurve(f,y,c),c++}s.push(y),a=Math.max(a,y.frames[(y.getFrameCount()-1)*t.PathConstraintMixTimeline.ENTRIES])}}}if(e.deform)for(var P in e.deform){var L=e.deform[P],O=n.findSkin(P);if(null==O)throw new Error("Skin not found: "+P);for(var o in L){h=L[o];if(-1==(Z=n.findSlotIndex(o)))throw new Error("Slot not found: "+h.name);for(var l in h){u=h[l];var _=O.getAttachment(Z,l);if(null==_)throw new Error("Deform attachment not found: "+u.name);var k=null!=_.bones,F=_.vertices,D=k?F.length/3*2:F.length;(y=new t.DeformTimeline(u.length)).slotIndex=Z,y.attachment=_;c=0;for(var V=0;V<u.length;V++){f=u[V];var N=void 0,B=this.getValue(f,"vertices",null);if(null==B)N=k?t.Utils.newFloatArray(D):F;else{N=t.Utils.newFloatArray(D);var X=this.getValue(f,"offset",0);if(t.Utils.arrayCopy(B,0,N,X,B.length),1!=r)for(var U=(d=X)+B.length;d<U;d++)N[d]*=r;if(!k)for(d=0;d<D;d++)N[d]+=F[d]}y.setFrame(c,this.getValue(f,"time",0),N),this.readCurve(f,y,c),c++}s.push(y),a=Math.max(a,y.frames[y.getFrameCount()-1])}}}var Y=e.drawOrder;if(null==Y&&(Y=e.draworder),null!=Y){y=new t.DrawOrderTimeline(Y.length);var z=n.slots.length;for(c=0,V=0;V<Y.length;V++){var W=Y[V],G=null,H=this.getValue(W,"offsets",null);if(null!=H){G=t.Utils.newArray(z,-1);var q=t.Utils.newArray(z-H.length,0),j=0,K=0;for(d=0;d<H.length;d++){var Z,Q=H[d];if(-1==(Z=n.findSlotIndex(Q.slot)))throw new Error("Slot not found: "+Q.slot);for(;j!=Z;)q[K++]=j++;G[j+Q.offset]=j++}for(;j<z;)q[K++]=j++;for(d=z-1;d>=0;d--)-1==G[d]&&(G[d]=q[--K])}y.setFrame(c++,this.getValue(W,"time",0),G)}s.push(y),a=Math.max(a,y.frames[y.getFrameCount()-1])}if(e.events){for(y=new t.EventTimeline(e.events.length),c=0,d=0;d<e.events.length;d++){var $=e.events[d],J=n.findEvent($.name);if(null==J)throw new Error("Event not found: "+$.name);var tt=new t.Event(t.Utils.toSinglePrecision(this.getValue($,"time",0)),J);tt.intValue=this.getValue($,"int",J.intValue),tt.floatValue=this.getValue($,"float",J.floatValue),tt.stringValue=this.getValue($,"string",J.stringValue),null!=tt.data.audioPath&&(tt.volume=this.getValue($,"volume",1),tt.balance=this.getValue($,"balance",0)),y.setFrame(c++,tt)}s.push(y),a=Math.max(a,y.frames[y.getFrameCount()-1])}if(isNaN(a))throw new Error("Error while parsing animation, duration is NaN");n.animations.push(new t.Animation(i,s,a))},e.prototype.readCurve=function(t,e,i){if(t.hasOwnProperty("curve"))if("stepped"==t.curve)e.setStepped(i);else{var n=t.curve;e.setCurve(i,n,this.getValue(t,"c2",0),this.getValue(t,"c3",1),this.getValue(t,"c4",1))}},e.prototype.getValue=function(t,e,i){return void 0!==t[e]?t[e]:i},e.blendModeFromString=function(e){if("normal"==(e=e.toLowerCase()))return t.BlendMode.Normal;if("additive"==e)return t.BlendMode.Additive;if("multiply"==e)return t.BlendMode.Multiply;if("screen"==e)return t.BlendMode.Screen;throw new Error("Unknown blend mode: ".concat(e))},e.positionModeFromString=function(e){if("fixed"==(e=e.toLowerCase()))return t.PositionMode.Fixed;if("percent"==e)return t.PositionMode.Percent;throw new Error("Unknown position mode: ".concat(e))},e.spacingModeFromString=function(e){if("length"==(e=e.toLowerCase()))return t.SpacingMode.Length;if("fixed"==e)return t.SpacingMode.Fixed;if("percent"==e)return t.SpacingMode.Percent;throw new Error("Unknown position mode: ".concat(e))},e.rotateModeFromString=function(e){if("tangent"==(e=e.toLowerCase()))return t.RotateMode.Tangent;if("chain"==e)return t.RotateMode.Chain;if("chainscale"==e)return t.RotateMode.ChainScale;throw new Error("Unknown rotate mode: ".concat(e))},e.transformModeFromString=function(e){if("normal"==(e=e.toLowerCase()))return t.TransformMode.Normal;if("onlytranslation"==e)return t.TransformMode.OnlyTranslation;if("norotationorreflection"==e)return t.TransformMode.NoRotationOrReflection;if("noscale"==e)return t.TransformMode.NoScale;if("noscaleorreflection"==e)return t.TransformMode.NoScaleOrReflection;throw new Error("Unknown transform mode: ".concat(e))},e}();t.SkeletonJson=e;var i=function(t,e,i,n,r){this.mesh=t,this.skin=e,this.slotIndex=i,this.parent=n,this.inheritDeform=r}}(n||(n={})),function(t){var e=function(t,e,i){this.slotIndex=t,this.name=e,this.attachment=i};t.SkinEntry=e;var i=function(){function i(t){if(this.attachments=new Array,this.bones=Array(),this.constraints=new Array,null==t)throw new Error("name cannot be null.");this.name=t}return i.prototype.setAttachment=function(t,e,i){if(null==i)throw new Error("attachment cannot be null.");var n=this.attachments;t>=n.length&&(n.length=t+1),n[t]||(n[t]={}),n[t][e]=i},i.prototype.addSkin=function(t){for(var e=0;e<t.bones.length;e++){for(var i=t.bones[e],n=!1,r=0;r<this.bones.length;r++)if(this.bones[r]==i){n=!0;break}n||this.bones.push(i)}for(e=0;e<t.constraints.length;e++){var s=t.constraints[e];for(n=!1,r=0;r<this.constraints.length;r++)if(this.constraints[r]==s){n=!0;break}n||this.constraints.push(s)}var a=t.getAttachments();for(e=0;e<a.length;e++){var o=a[e];this.setAttachment(o.slotIndex,o.name,o.attachment)}},i.prototype.copySkin=function(e){for(var i=0;i<e.bones.length;i++){for(var n=e.bones[i],r=!1,s=0;s<this.bones.length;s++)if(this.bones[s]==n){r=!0;break}r||this.bones.push(n)}for(i=0;i<e.constraints.length;i++){var a=e.constraints[i];for(r=!1,s=0;s<this.constraints.length;s++)if(this.constraints[s]==a){r=!0;break}r||this.constraints.push(a)}var o=e.getAttachments();for(i=0;i<o.length;i++){var h=o[i];null!=h.attachment&&(h.attachment instanceof t.MeshAttachment?(h.attachment=h.attachment.newLinkedMesh(),this.setAttachment(h.slotIndex,h.name,h.attachment)):(h.attachment=h.attachment.copy(),this.setAttachment(h.slotIndex,h.name,h.attachment)))}},i.prototype.getAttachment=function(t,e){var i=this.attachments[t];return i?i[e]:null},i.prototype.removeAttachment=function(t,e){var i=this.attachments[t];i&&(i[e]=null)},i.prototype.getAttachments=function(){for(var t=new Array,i=0;i<this.attachments.length;i++){var n=this.attachments[i];if(n)for(var r in n){var s=n[r];s&&t.push(new e(i,r,s))}}return t},i.prototype.getAttachmentsForSlot=function(t,i){var n=this.attachments[t];if(n)for(var r in n){var s=n[r];s&&i.push(new e(t,r,s))}},i.prototype.clear=function(){this.attachments.length=0,this.bones.length=0,this.constraints.length=0},i.prototype.attachAll=function(t,e){for(var i=0,n=0;n<t.slots.length;n++){var r=t.slots[n],s=r.getAttachment();if(s&&i<e.attachments.length){var a=e.attachments[i];for(var o in a){if(s==a[o]){var h=this.getAttachment(i,o);null!=h&&r.setAttachment(h);break}}}i++}},i}();t.Skin=i}(n||(n={})),function(t){var e=function(){function e(e,i){if(this.deform=new Array,null==e)throw new Error("data cannot be null.");if(null==i)throw new Error("bone cannot be null.");this.data=e,this.bone=i,this.color=new t.Color,this.darkColor=null==e.darkColor?null:new t.Color,this.setToSetupPose()}return e.prototype.getSkeleton=function(){return this.bone.skeleton},e.prototype.getAttachment=function(){return this.attachment},e.prototype.setAttachment=function(t){this.attachment!=t&&(this.attachment=t,this.attachmentTime=this.bone.skeleton.time,this.deform.length=0)},e.prototype.setAttachmentTime=function(t){this.attachmentTime=this.bone.skeleton.time-t},e.prototype.getAttachmentTime=function(){return this.bone.skeleton.time-this.attachmentTime},e.prototype.setToSetupPose=function(){this.color.setFromColor(this.data.color),null!=this.darkColor&&this.darkColor.setFromColor(this.data.darkColor),null==this.data.attachmentName?this.attachment=null:(this.attachment=null,this.setAttachment(this.bone.skeleton.getAttachment(this.data.index,this.data.attachmentName)))},e}();t.Slot=e}(n||(n={})),function(t){var e=function(e,i,n){if(this.color=new t.Color(1,1,1,1),e<0)throw new Error("index must be >= 0.");if(null==i)throw new Error("name cannot be null.");if(null==n)throw new Error("boneData cannot be null.");this.index=e,this.name=i,this.boneData=n};t.SlotData=e}(n||(n={})),function(t){var e,i,n=function(){function t(t){this._image=t}return t.prototype.getImage=function(){return this._image},t.filterFromString=function(t){switch(t.toLowerCase()){case"nearest":return e.Nearest;case"linear":return e.Linear;case"mipmap":return e.MipMap;case"mipmapnearestnearest":return e.MipMapNearestNearest;case"mipmaplinearnearest":return e.MipMapLinearNearest;case"mipmapnearestlinear":return e.MipMapNearestLinear;case"mipmaplinearlinear":return e.MipMapLinearLinear;default:throw new Error("Unknown texture filter ".concat(t))}},t.wrapFromString=function(t){switch(t.toLowerCase()){case"mirroredtepeat":return i.MirroredRepeat;case"clamptoedge":return i.ClampToEdge;case"repeat":return i.Repeat;default:throw new Error("Unknown texture wrap ".concat(t))}},t}();t.Texture=n,function(t){t[t.Nearest=9728]="Nearest",t[t.Linear=9729]="Linear",t[t.MipMap=9987]="MipMap",t[t.MipMapNearestNearest=9984]="MipMapNearestNearest",t[t.MipMapLinearNearest=9985]="MipMapLinearNearest",t[t.MipMapNearestLinear=9986]="MipMapNearestLinear",t[t.MipMapLinearLinear=9987]="MipMapLinearLinear"}(e=t.TextureFilter||(t.TextureFilter={})),function(t){t[t.MirroredRepeat=33648]="MirroredRepeat",t[t.ClampToEdge=33071]="ClampToEdge",t[t.Repeat=10497]="Repeat"}(i=t.TextureWrap||(t.TextureWrap={}));var s=function(){this.u=0,this.v=0,this.u2=0,this.v2=0,this.width=0,this.height=0,this.rotate=!1,this.offsetX=0,this.offsetY=0,this.originalWidth=0,this.originalHeight=0};t.TextureRegion=s;var a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.prototype.setFilters=function(t,e){},e.prototype.setWraps=function(t,e){},e.prototype.dispose=function(){},e}(n);t.FakeTexture=a}(n||(n={})),function(t){var e=function(){function e(t,e){this.pages=new Array,this.regions=new Array,this.load(t,e)}return e.prototype.load=function(e,r){if(null==r)throw new Error("textureLoader cannot be null.");for(var a=new i(e),o=new Array(4),h=null;;){var l=a.readLine();if(null==l)break;if(0==(l=l.trim()).length)h=null;else if(h){var u=new s;u.name=l,u.page=h;var c=a.readValue();"true"==c.toLocaleLowerCase()?u.degrees=90:"false"==c.toLocaleLowerCase()?u.degrees=0:u.degrees=parseFloat(c),u.rotate=90==u.degrees,a.readTuple(o);var d=parseInt(o[0]),f=parseInt(o[1]);a.readTuple(o);var p=parseInt(o[0]),v=parseInt(o[1]);u.u=d/h.width,u.v=f/h.height,u.rotate?(u.u2=(d+v)/h.width,u.v2=(f+p)/h.height):(u.u2=(d+p)/h.width,u.v2=(f+v)/h.height),u.x=d,u.y=f,u.width=Math.abs(p),u.height=Math.abs(v),4==a.readTuple(o)&&4==a.readTuple(o)&&a.readTuple(o),u.originalWidth=parseInt(o[0]),u.originalHeight=parseInt(o[1]),a.readTuple(o),u.offsetX=parseInt(o[0]),u.offsetY=parseInt(o[1]),u.index=parseInt(a.readValue()),u.texture=h.texture,this.regions.push(u)}else{(h=new n).name=l,2==a.readTuple(o)&&(h.width=parseInt(o[0]),h.height=parseInt(o[1]),a.readTuple(o)),a.readTuple(o),h.minFilter=t.Texture.filterFromString(o[0]),h.magFilter=t.Texture.filterFromString(o[1]);var g=a.readValue();h.uWrap=t.TextureWrap.ClampToEdge,h.vWrap=t.TextureWrap.ClampToEdge,"x"==g?h.uWrap=t.TextureWrap.Repeat:"y"==g?h.vWrap=t.TextureWrap.Repeat:"xy"==g&&(h.uWrap=h.vWrap=t.TextureWrap.Repeat),h.texture=r(l),h.texture.setFilters(h.minFilter,h.magFilter),h.texture.setWraps(h.uWrap,h.vWrap),h.width=h.texture.getImage().width,h.height=h.texture.getImage().height,this.pages.push(h)}}},e.prototype.findRegion=function(t){for(var e=0;e<this.regions.length;e++)if(this.regions[e].name==t)return this.regions[e];return null},e.prototype.dispose=function(){for(var t=0;t<this.pages.length;t++)this.pages[t].texture.dispose()},e}();t.TextureAtlas=e;var i=function(){function t(t){this.index=0,this.lines=t.split(/\r\n|\r|\n/)}return t.prototype.readLine=function(){return this.index>=this.lines.length?null:this.lines[this.index++]},t.prototype.readValue=function(){var t=this.readLine(),e=t.indexOf(":");if(-1==e)throw new Error("Invalid line: "+t);return t.substring(e+1).trim()},t.prototype.readTuple=function(t){var e=this.readLine(),i=e.indexOf(":");if(-1==i)throw new Error("Invalid line: "+e);for(var n=0,r=i+1;n<3;n++){var s=e.indexOf(",",r);if(-1==s)break;t[n]=e.substr(r,s-r).trim(),r=s+1}return t[n]=e.substring(r).trim(),n+1},t}(),n=function(){};t.TextureAtlasPage=n;var s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e}(t.TextureRegion);t.TextureAtlasRegion=s}(n||(n={})),function(t){var e=function(){function e(e,i){if(this.rotateMix=0,this.translateMix=0,this.scaleMix=0,this.shearMix=0,this.temp=new t.Vector2,this.active=!1,null==e)throw new Error("data cannot be null.");if(null==i)throw new Error("skeleton cannot be null.");this.data=e,this.rotateMix=e.rotateMix,this.translateMix=e.translateMix,this.scaleMix=e.scaleMix,this.shearMix=e.shearMix,this.bones=new Array;for(var n=0;n<e.bones.length;n++)this.bones.push(i.findBone(e.bones[n].name));this.target=i.findBone(e.target.name)}return e.prototype.isActive=function(){return this.active},e.prototype.apply=function(){this.update()},e.prototype.update=function(){this.data.local?this.data.relative?this.applyRelativeLocal():this.applyAbsoluteLocal():this.data.relative?this.applyRelativeWorld():this.applyAbsoluteWorld()},e.prototype.applyAbsoluteWorld=function(){for(var e=this.rotateMix,i=this.translateMix,n=this.scaleMix,r=this.shearMix,s=this.target,a=s.a,o=s.b,h=s.c,l=s.d,u=a*l-o*h>0?t.MathUtils.degRad:-t.MathUtils.degRad,c=this.data.offsetRotation*u,d=this.data.offsetShearY*u,f=this.bones,p=0,v=f.length;p<v;p++){var g=f[p],m=!1;if(0!=e){var M=g.a,x=g.b,y=g.c,w=g.d;(R=Math.atan2(h,a)-Math.atan2(y,M)+c)>t.MathUtils.PI?R-=t.MathUtils.PI2:R<-t.MathUtils.PI&&(R+=t.MathUtils.PI2),R*=e;var E=Math.cos(R),b=Math.sin(R);g.a=E*M-b*y,g.b=E*x-b*w,g.c=b*M+E*y,g.d=b*x+E*w,m=!0}if(0!=i){var T=this.temp;s.localToWorld(T.set(this.data.offsetX,this.data.offsetY)),g.worldX+=(T.x-g.worldX)*i,g.worldY+=(T.y-g.worldY)*i,m=!0}if(n>0){var A=Math.sqrt(g.a*g.a+g.c*g.c),S=Math.sqrt(a*a+h*h);A>1e-5&&(A=(A+(S-A+this.data.offsetScaleX)*n)/A),g.a*=A,g.c*=A,A=Math.sqrt(g.b*g.b+g.d*g.d),S=Math.sqrt(o*o+l*l),A>1e-5&&(A=(A+(S-A+this.data.offsetScaleY)*n)/A),g.b*=A,g.d*=A,m=!0}if(r>0){x=g.b,w=g.d;var R,C=Math.atan2(w,x);(R=Math.atan2(l,o)-Math.atan2(h,a)-(C-Math.atan2(g.c,g.a)))>t.MathUtils.PI?R-=t.MathUtils.PI2:R<-t.MathUtils.PI&&(R+=t.MathUtils.PI2),R=C+(R+d)*r;A=Math.sqrt(x*x+w*w);g.b=Math.cos(R)*A,g.d=Math.sin(R)*A,m=!0}m&&(g.appliedValid=!1)}},e.prototype.applyRelativeWorld=function(){for(var e=this.rotateMix,i=this.translateMix,n=this.scaleMix,r=this.shearMix,s=this.target,a=s.a,o=s.b,h=s.c,l=s.d,u=a*l-o*h>0?t.MathUtils.degRad:-t.MathUtils.degRad,c=this.data.offsetRotation*u,d=this.data.offsetShearY*u,f=this.bones,p=0,v=f.length;p<v;p++){var g=f[p],m=!1;if(0!=e){var M=g.a,x=g.b,y=g.c,w=g.d;(S=Math.atan2(h,a)+c)>t.MathUtils.PI?S-=t.MathUtils.PI2:S<-t.MathUtils.PI&&(S+=t.MathUtils.PI2),S*=e;var E=Math.cos(S),b=Math.sin(S);g.a=E*M-b*y,g.b=E*x-b*w,g.c=b*M+E*y,g.d=b*x+E*w,m=!0}if(0!=i){var T=this.temp;s.localToWorld(T.set(this.data.offsetX,this.data.offsetY)),g.worldX+=T.x*i,g.worldY+=T.y*i,m=!0}if(n>0){var A=(Math.sqrt(a*a+h*h)-1+this.data.offsetScaleX)*n+1;g.a*=A,g.c*=A,A=(Math.sqrt(o*o+l*l)-1+this.data.offsetScaleY)*n+1,g.b*=A,g.d*=A,m=!0}if(r>0){var S;(S=Math.atan2(l,o)-Math.atan2(h,a))>t.MathUtils.PI?S-=t.MathUtils.PI2:S<-t.MathUtils.PI&&(S+=t.MathUtils.PI2);x=g.b,w=g.d;S=Math.atan2(w,x)+(S-t.MathUtils.PI/2+d)*r;A=Math.sqrt(x*x+w*w);g.b=Math.cos(S)*A,g.d=Math.sin(S)*A,m=!0}m&&(g.appliedValid=!1)}},e.prototype.applyAbsoluteLocal=function(){var t=this.rotateMix,e=this.translateMix,i=this.scaleMix,n=this.shearMix,r=this.target;r.appliedValid||r.updateAppliedTransform();for(var s=this.bones,a=0,o=s.length;a<o;a++){var h=s[a];h.appliedValid||h.updateAppliedTransform();var l=h.arotation;if(0!=t){var u=r.arotation-l+this.data.offsetRotation;l+=(u-=360*(16384-(16384.499999999996-u/360|0)))*t}var c=h.ax,d=h.ay;0!=e&&(c+=(r.ax-c+this.data.offsetX)*e,d+=(r.ay-d+this.data.offsetY)*e);var f=h.ascaleX,p=h.ascaleY;0!=i&&(f>1e-5&&(f=(f+(r.ascaleX-f+this.data.offsetScaleX)*i)/f),p>1e-5&&(p=(p+(r.ascaleY-p+this.data.offsetScaleY)*i)/p));var v=h.ashearY;if(0!=n){u=r.ashearY-v+this.data.offsetShearY;u-=360*(16384-(16384.499999999996-u/360|0)),h.shearY+=u*n}h.updateWorldTransformWith(c,d,l,f,p,h.ashearX,v)}},e.prototype.applyRelativeLocal=function(){var t=this.rotateMix,e=this.translateMix,i=this.scaleMix,n=this.shearMix,r=this.target;r.appliedValid||r.updateAppliedTransform();for(var s=this.bones,a=0,o=s.length;a<o;a++){var h=s[a];h.appliedValid||h.updateAppliedTransform();var l=h.arotation;0!=t&&(l+=(r.arotation+this.data.offsetRotation)*t);var u=h.ax,c=h.ay;0!=e&&(u+=(r.ax+this.data.offsetX)*e,c+=(r.ay+this.data.offsetY)*e);var d=h.ascaleX,f=h.ascaleY;0!=i&&(d>1e-5&&(d*=(r.ascaleX-1+this.data.offsetScaleX)*i+1),f>1e-5&&(f*=(r.ascaleY-1+this.data.offsetScaleY)*i+1));var p=h.ashearY;0!=n&&(p+=(r.ashearY+this.data.offsetShearY)*n),h.updateWorldTransformWith(u,c,l,d,f,h.ashearX,p)}},e}();t.TransformConstraint=e}(n||(n={})),function(t){var e=function(t){function e(e){var i=t.call(this,e,0,!1)||this;return i.bones=new Array,i.rotateMix=0,i.translateMix=0,i.scaleMix=0,i.shearMix=0,i.offsetRotation=0,i.offsetX=0,i.offsetY=0,i.offsetScaleX=0,i.offsetScaleY=0,i.offsetShearY=0,i.relative=!1,i.local=!1,i}return r(e,t),e}(t.ConstraintData);t.TransformConstraintData=e}(n||(n={})),function(t){var e=function(){function e(){this.convexPolygons=new Array,this.convexPolygonsIndices=new Array,this.indicesArray=new Array,this.isConcaveArray=new Array,this.triangles=new Array,this.polygonPool=new t.Pool((function(){return new Array})),this.polygonIndicesPool=new t.Pool((function(){return new Array}))}return e.prototype.triangulate=function(t){var i=t,n=t.length>>1,r=this.indicesArray;r.length=0;for(var s=0;s<n;s++)r[s]=s;var a=this.isConcaveArray;a.length=0;s=0;for(var o=n;s<o;++s)a[s]=e.isConcave(s,n,i,r);var h=this.triangles;for(h.length=0;n>3;){for(var l=n-1,u=(s=0,1);;){t:if(!a[s]){for(var c=r[l]<<1,d=r[s]<<1,f=r[u]<<1,p=i[c],v=i[c+1],g=i[d],m=i[d+1],M=i[f],x=i[f+1],y=(u+1)%n;y!=l;y=(y+1)%n)if(a[y]){var w=r[y]<<1,E=i[w],b=i[w+1];if(e.positiveArea(M,x,p,v,E,b)&&e.positiveArea(p,v,g,m,E,b)&&e.positiveArea(g,m,M,x,E,b))break t}break}if(0==u){do{if(!a[s])break;s--}while(s>0);break}l=s,s=u,u=(u+1)%n}h.push(r[(n+s-1)%n]),h.push(r[s]),h.push(r[(s+1)%n]),r.splice(s,1),a.splice(s,1);var T=(--n+s-1)%n,A=s==n?0:s;a[T]=e.isConcave(T,n,i,r),a[A]=e.isConcave(A,n,i,r)}return 3==n&&(h.push(r[2]),h.push(r[0]),h.push(r[1])),h},e.prototype.decompose=function(t,i){var n=t,r=this.convexPolygons;this.polygonPool.freeAll(r),r.length=0;var s=this.convexPolygonsIndices;this.polygonIndicesPool.freeAll(s),s.length=0;var a=this.polygonIndicesPool.obtain();a.length=0;var o=this.polygonPool.obtain();o.length=0;for(var h=-1,l=0,u=0,c=i.length;u<c;u+=3){var d=i[u]<<1,f=i[u+1]<<1,p=i[u+2]<<1,v=n[d],g=n[d+1],m=n[f],M=n[f+1],x=n[p],y=n[p+1],w=!1;if(h==d){var E=o.length-4,b=e.winding(o[E],o[E+1],o[E+2],o[E+3],x,y),T=e.winding(x,y,o[0],o[1],o[2],o[3]);b==l&&T==l&&(o.push(x),o.push(y),a.push(p),w=!0)}w||(o.length>0?(r.push(o),s.push(a)):(this.polygonPool.free(o),this.polygonIndicesPool.free(a)),(o=this.polygonPool.obtain()).length=0,o.push(v),o.push(g),o.push(m),o.push(M),o.push(x),o.push(y),(a=this.polygonIndicesPool.obtain()).length=0,a.push(d),a.push(f),a.push(p),l=e.winding(v,g,m,M,x,y),h=d)}o.length>0&&(r.push(o),s.push(a));for(u=0,c=r.length;u<c;u++)if(0!=(a=s[u]).length)for(var A=a[0],S=a[a.length-1],R=(o=r[u])[E=o.length-4],C=o[E+1],I=o[E+2],P=o[E+3],L=o[0],O=o[1],_=o[2],k=o[3],F=e.winding(R,C,I,P,L,O),D=0;D<c;D++)if(D!=u){var V=s[D];if(3==V.length){var N=V[0],B=V[1],X=V[2],U=r[D];x=U[U.length-2],y=U[U.length-1];if(N==A&&B==S){b=e.winding(R,C,I,P,x,y),T=e.winding(x,y,L,O,_,k);b==F&&T==F&&(U.length=0,V.length=0,o.push(x),o.push(y),a.push(X),R=I,C=P,I=x,P=y,D=0)}}}for(u=r.length-1;u>=0;u--)0==(o=r[u]).length&&(r.splice(u,1),this.polygonPool.free(o),a=s[u],s.splice(u,1),this.polygonIndicesPool.free(a));return r},e.isConcave=function(t,e,i,n){var r=n[(e+t-1)%e]<<1,s=n[t]<<1,a=n[(t+1)%e]<<1;return!this.positiveArea(i[r],i[r+1],i[s],i[s+1],i[a],i[a+1])},e.positiveArea=function(t,e,i,n,r,s){return t*(s-n)+i*(e-s)+r*(n-e)>=0},e.winding=function(t,e,i,n,r,s){var a=i-t,o=n-e;return r*o-s*a+a*e-t*o>=0?1:-1},e}();t.Triangulator=e}(n||(n={})),function(t){var e=function(){function t(){this.array=new Array}return t.prototype.add=function(t){var e=this.contains(t);return this.array[0|t]=0|t,!e},t.prototype.contains=function(t){return null!=this.array[0|t]},t.prototype.remove=function(t){this.array[0|t]=void 0},t.prototype.clear=function(){this.array.length=0},t}();t.IntSet=e;var i=function(){function t(t,e,i,n){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=0),void 0===n&&(n=0),this.r=t,this.g=e,this.b=i,this.a=n}return t.prototype.set=function(t,e,i,n){return this.r=t,this.g=e,this.b=i,this.a=n,this.clamp(),this},t.prototype.setFromColor=function(t){return this.r=t.r,this.g=t.g,this.b=t.b,this.a=t.a,this},t.prototype.setFromString=function(t){return t="#"==t.charAt(0)?t.substr(1):t,this.r=parseInt(t.substr(0,2),16)/255,this.g=parseInt(t.substr(2,2),16)/255,this.b=parseInt(t.substr(4,2),16)/255,this.a=(8!=t.length?255:parseInt(t.substr(6,2),16))/255,this},t.prototype.add=function(t,e,i,n){return this.r+=t,this.g+=e,this.b+=i,this.a+=n,this.clamp(),this},t.prototype.clamp=function(){return this.r<0?this.r=0:this.r>1&&(this.r=1),this.g<0?this.g=0:this.g>1&&(this.g=1),this.b<0?this.b=0:this.b>1&&(this.b=1),this.a<0?this.a=0:this.a>1&&(this.a=1),this},t.rgba8888ToColor=function(t,e){t.r=((4278190080&e)>>>24)/255,t.g=((16711680&e)>>>16)/255,t.b=((65280&e)>>>8)/255,t.a=(255&e)/255},t.rgb888ToColor=function(t,e){t.r=((16711680&e)>>>16)/255,t.g=((65280&e)>>>8)/255,t.b=(255&e)/255},t.WHITE=new t(1,1,1,1),t.RED=new t(1,0,0,1),t.GREEN=new t(0,1,0,1),t.BLUE=new t(0,0,1,1),t.MAGENTA=new t(1,0,1,1),t}();t.Color=i;var n=function(){function t(){}return t.clamp=function(t,e,i){return t<e?e:t>i?i:t},t.cosDeg=function(e){return Math.cos(e*t.degRad)},t.sinDeg=function(e){return Math.sin(e*t.degRad)},t.signum=function(t){return t>0?1:t<0?-1:0},t.toInt=function(t){return t>0?Math.floor(t):Math.ceil(t)},t.cbrt=function(t){var e=Math.pow(Math.abs(t),1/3);return t<0?-e:e},t.randomTriangular=function(e,i){return t.randomTriangularWith(e,i,.5*(e+i))},t.randomTriangularWith=function(t,e,i){var n=Math.random(),r=e-t;return n<=(i-t)/r?t+Math.sqrt(n*r*(i-t)):e-Math.sqrt((1-n)*r*(e-i))},t.PI=3.1415927,t.PI2=2*t.PI,t.radiansToDegrees=180/t.PI,t.radDeg=t.radiansToDegrees,t.degreesToRadians=t.PI/180,t.degRad=t.degreesToRadians,t}();t.MathUtils=n;var s=function(){function t(){}return t.prototype.apply=function(t,e,i){return t+(e-t)*this.applyInternal(i)},t}();t.Interpolation=s;var a=function(t){function e(e){var i=t.call(this)||this;return i.power=2,i.power=e,i}return r(e,t),e.prototype.applyInternal=function(t){return t<=.5?Math.pow(2*t,this.power)/2:Math.pow(2*(t-1),this.power)/(this.power%2==0?-2:2)+1},e}(s);t.Pow=a;var o=function(t){function e(e){return t.call(this,e)||this}return r(e,t),e.prototype.applyInternal=function(t){return Math.pow(t-1,this.power)*(this.power%2==0?-1:1)+1},e}(a);t.PowOut=o;var h=function(){function t(){}return t.arrayCopy=function(t,e,i,n,r){for(var s=e,a=n;s<e+r;s++,a++)i[a]=t[s]},t.setArraySize=function(t,e,i){void 0===i&&(i=0);var n=t.length;if(n==e)return t;if(t.length=e,n<e)for(var r=n;r<e;r++)t[r]=i;return t},t.ensureArrayCapacity=function(e,i,n){return void 0===n&&(n=0),e.length>=i?e:t.setArraySize(e,i,n)},t.newArray=function(t,e){for(var i=new Array(t),n=0;n<t;n++)i[n]=e;return i},t.newFloatArray=function(e){if(t.SUPPORTS_TYPED_ARRAYS)return new Float32Array(e);for(var i=new Array(e),n=0;n<i.length;n++)i[n]=0;return i},t.newShortArray=function(e){if(t.SUPPORTS_TYPED_ARRAYS)return new Int16Array(e);for(var i=new Array(e),n=0;n<i.length;n++)i[n]=0;return i},t.toFloatArray=function(e){return t.SUPPORTS_TYPED_ARRAYS?new Float32Array(e):e},t.toSinglePrecision=function(e){return t.SUPPORTS_TYPED_ARRAYS?Math.fround(e):e},t.webkit602BugfixHelper=function(t,e){},t.contains=function(t,e,i){void 0===i&&(i=!0);for(var n=0;n<t.length;n++)if(t[n]==e)return!0;return!1},t.SUPPORTS_TYPED_ARRAYS="undefined"!=typeof Float32Array,t}();t.Utils=h;var l=function(){function t(){}return t.logBones=function(t){for(var e=0;e<t.bones.length;e++){var i=t.bones[e];console.log(i.data.name+", "+i.a+", "+i.b+", "+i.c+", "+i.d+", "+i.worldX+", "+i.worldY)}},t}();t.DebugUtils=l;var u=function(){function t(t){this.items=new Array,this.instantiator=t}return t.prototype.obtain=function(){return this.items.length>0?this.items.pop():this.instantiator()},t.prototype.free=function(t){t.reset&&t.reset(),this.items.push(t)},t.prototype.freeAll=function(t){for(var e=0;e<t.length;e++)this.free(t[e])},t.prototype.clear=function(){this.items.length=0},t}();t.Pool=u;var c=function(){function t(t,e){void 0===t&&(t=0),void 0===e&&(e=0),this.x=t,this.y=e}return t.prototype.set=function(t,e){return this.x=t,this.y=e,this},t.prototype.length=function(){var t=this.x,e=this.y;return Math.sqrt(t*t+e*e)},t.prototype.normalize=function(){var t=this.length();return 0!=t&&(this.x/=t,this.y/=t),this},t}();t.Vector2=c;var d=function(){function t(){this.maxDelta=.064,this.framesPerSecond=0,this.delta=0,this.totalTime=0,this.lastTime=Date.now()/1e3,this.frameCount=0,this.frameTime=0}return t.prototype.update=function(){var t=Date.now()/1e3;this.delta=t-this.lastTime,this.frameTime+=this.delta,this.totalTime+=this.delta,this.delta>this.maxDelta&&(this.delta=this.maxDelta),this.lastTime=t,this.frameCount++,this.frameTime>1&&(this.framesPerSecond=this.frameCount/this.frameTime,this.frameTime=0,this.frameCount=0)},t}();t.TimeKeeper=d;var f=function(){function t(t){void 0===t&&(t=32),this.addedValues=0,this.lastValue=0,this.mean=0,this.dirty=!0,this.values=new Array(t)}return t.prototype.hasEnoughData=function(){return this.addedValues>=this.values.length},t.prototype.addValue=function(t){this.addedValues<this.values.length&&this.addedValues++,this.values[this.lastValue++]=t,this.lastValue>this.values.length-1&&(this.lastValue=0),this.dirty=!0},t.prototype.getMean=function(){if(this.hasEnoughData()){if(this.dirty){for(var t=0,e=0;e<this.values.length;e++)t+=this.values[e];this.mean=t/this.values.length,this.dirty=!1}return this.mean}return 0},t}();t.WindowedMean=f}(n||(n={})),Math.fround||(Math.fround=(i=new Float32Array(1),function(t){return i[0]=t,i[0]})),function(t){var e=function(t){if(null==t)throw new Error("name cannot be null.");this.name=t};t.Attachment=e;var i=function(e){function i(t){var n=e.call(this,t)||this;return n.id=(65535&i.nextID++)<<11,n.worldVerticesLength=0,n.deformAttachment=n,n}return r(i,e),i.prototype.computeWorldVertices=function(t,e,i,n,r,s){i=r+(i>>1)*s;var a=t.bone.skeleton,o=t.deform,h=this.vertices,l=this.bones;if(null!=l){for(var u=0,c=0,d=0;d<e;d+=2){u+=(g=l[u])+1,c+=g}var f=a.bones;if(0==o.length)for(C=r,T=3*c;C<i;C+=s){var p=0,v=0,g=l[u++];for(g+=u;u<g;u++,T+=3){y=f[l[u]],I=h[T],P=h[T+1];var m=h[T+2];p+=(I*y.a+P*y.b+y.worldX)*m,v+=(I*y.c+P*y.d+y.worldY)*m}n[C]=p,n[C+1]=v}else for(var M=o,x=(C=r,T=3*c,c<<1);C<i;C+=s){p=0,v=0,g=l[u++];for(g+=u;u<g;u++,T+=3,x+=2){y=f[l[u]],I=h[T]+M[x],P=h[T+1]+M[x+1],m=h[T+2];p+=(I*y.a+P*y.b+y.worldX)*m,v+=(I*y.c+P*y.d+y.worldY)*m}n[C]=p,n[C+1]=v}}else{o.length>0&&(h=o);for(var y,w=(y=t.bone).worldX,E=y.worldY,b=y.a,T=y.b,A=y.c,S=y.d,R=e,C=r;C<i;R+=2,C+=s){var I=h[R],P=h[R+1];n[C]=I*b+P*T+w,n[C+1]=I*A+P*S+E}}},i.prototype.copyTo=function(e){null!=this.bones?(e.bones=new Array(this.bones.length),t.Utils.arrayCopy(this.bones,0,e.bones,0,this.bones.length)):e.bones=null,null!=this.vertices?(e.vertices=t.Utils.newFloatArray(this.vertices.length),t.Utils.arrayCopy(this.vertices,0,e.vertices,0,this.vertices.length)):e.vertices=null,e.worldVerticesLength=this.worldVerticesLength,e.deformAttachment=this.deformAttachment},i.nextID=0,i}(e);t.VertexAttachment=i}(n||(n={})),function(t){!function(t){t[t.Region=0]="Region",t[t.BoundingBox=1]="BoundingBox",t[t.Mesh=2]="Mesh",t[t.LinkedMesh=3]="LinkedMesh",t[t.Path=4]="Path",t[t.Point=5]="Point",t[t.Clipping=6]="Clipping"}(t.AttachmentType||(t.AttachmentType={}))}(n||(n={})),function(t){var e=function(e){function i(i){var n=e.call(this,i)||this;return n.color=new t.Color(1,1,1,1),n}return r(i,e),i.prototype.copy=function(){var t=new i(this.name);return this.copyTo(t),t.color.setFromColor(this.color),t},i}(t.VertexAttachment);t.BoundingBoxAttachment=e}(n||(n={})),function(t){var e=function(e){function i(i){var n=e.call(this,i)||this;return n.color=new t.Color(.2275,.2275,.8078,1),n}return r(i,e),i.prototype.copy=function(){var t=new i(this.name);return this.copyTo(t),t.endSlot=this.endSlot,t.color.setFromColor(this.color),t},i}(t.VertexAttachment);t.ClippingAttachment=e}(n||(n={})),function(t){var e=function(e){function i(i){var n=e.call(this,i)||this;return n.color=new t.Color(1,1,1,1),n.tempColor=new t.Color(0,0,0,0),n}return r(i,e),i.prototype.updateUVs=function(){var e=this.regionUVs;null!=this.uvs&&this.uvs.length==e.length||(this.uvs=t.Utils.newFloatArray(e.length));var i=this.uvs,n=this.uvs.length,r=this.region.u,s=this.region.v,a=0,o=0;if(this.region instanceof t.TextureAtlasRegion){var h=this.region,l=h.texture.getImage().width,u=h.texture.getImage().height;switch(h.degrees){case 90:r-=(h.originalHeight-h.offsetY-h.height)/l,s-=(h.originalWidth-h.offsetX-h.width)/u,a=h.originalHeight/l,o=h.originalWidth/u;for(var c=0;c<n;c+=2)i[c]=r+e[c+1]*a,i[c+1]=s+(1-e[c])*o;return;case 180:r-=(h.originalWidth-h.offsetX-h.width)/l,s-=h.offsetY/u,a=h.originalWidth/l,o=h.originalHeight/u;for(c=0;c<n;c+=2)i[c]=r+(1-e[c])*a,i[c+1]=s+(1-e[c+1])*o;return;case 270:r-=h.offsetY/l,s-=h.offsetX/u,a=h.originalHeight/l,o=h.originalWidth/u;for(c=0;c<n;c+=2)i[c]=r+(1-e[c+1])*a,i[c+1]=s+e[c]*o;return}r-=h.offsetX/l,s-=(h.originalHeight-h.offsetY-h.height)/u,a=h.originalWidth/l,o=h.originalHeight/u}else null==this.region?(r=s=0,a=o=1):(a=this.region.u2-r,o=this.region.v2-s);for(c=0;c<n;c+=2)i[c]=r+e[c]*a,i[c+1]=s+e[c+1]*o},i.prototype.getParentMesh=function(){return this.parentMesh},i.prototype.setParentMesh=function(t){this.parentMesh=t,null!=t&&(this.bones=t.bones,this.vertices=t.vertices,this.worldVerticesLength=t.worldVerticesLength,this.regionUVs=t.regionUVs,this.triangles=t.triangles,this.hullLength=t.hullLength,this.worldVerticesLength=t.worldVerticesLength)},i.prototype.copy=function(){if(null!=this.parentMesh)return this.newLinkedMesh();var e=new i(this.name);return e.region=this.region,e.path=this.path,e.color.setFromColor(this.color),this.copyTo(e),e.regionUVs=new Array(this.regionUVs.length),t.Utils.arrayCopy(this.regionUVs,0,e.regionUVs,0,this.regionUVs.length),e.uvs=new Array(this.uvs.length),t.Utils.arrayCopy(this.uvs,0,e.uvs,0,this.uvs.length),e.triangles=new Array(this.triangles.length),t.Utils.arrayCopy(this.triangles,0,e.triangles,0,this.triangles.length),e.hullLength=this.hullLength,null!=this.edges&&(e.edges=new Array(this.edges.length),t.Utils.arrayCopy(this.edges,0,e.edges,0,this.edges.length)),e.width=this.width,e.height=this.height,e},i.prototype.newLinkedMesh=function(){var t=new i(this.name);return t.region=this.region,t.path=this.path,t.color.setFromColor(this.color),t.deformAttachment=this.deformAttachment,t.setParentMesh(null!=this.parentMesh?this.parentMesh:this),t.updateUVs(),t},i}(t.VertexAttachment);t.MeshAttachment=e}(n||(n={})),function(t){var e=function(e){function i(i){var n=e.call(this,i)||this;return n.closed=!1,n.constantSpeed=!1,n.color=new t.Color(1,1,1,1),n}return r(i,e),i.prototype.copy=function(){var e=new i(this.name);return this.copyTo(e),e.lengths=new Array(this.lengths.length),t.Utils.arrayCopy(this.lengths,0,e.lengths,0,this.lengths.length),e.closed=closed,e.constantSpeed=this.constantSpeed,e.color.setFromColor(this.color),e},i}(t.VertexAttachment);t.PathAttachment=e}(n||(n={})),function(t){var e=function(e){function i(i){var n=e.call(this,i)||this;return n.color=new t.Color(.38,.94,0,1),n}return r(i,e),i.prototype.computeWorldPosition=function(t,e){return e.x=this.x*t.a+this.y*t.b+t.worldX,e.y=this.x*t.c+this.y*t.d+t.worldY,e},i.prototype.computeWorldRotation=function(e){var i=t.MathUtils.cosDeg(this.rotation),n=t.MathUtils.sinDeg(this.rotation),r=i*e.a+n*e.b,s=i*e.c+n*e.d;return Math.atan2(s,r)*t.MathUtils.radDeg},i.prototype.copy=function(){var t=new i(this.name);return t.x=this.x,t.y=this.y,t.rotation=this.rotation,t.color.setFromColor(this.color),t},i}(t.VertexAttachment);t.PointAttachment=e}(n||(n={})),function(t){var e=function(e){function i(i){var n=e.call(this,i)||this;return n.x=0,n.y=0,n.scaleX=1,n.scaleY=1,n.rotation=0,n.width=0,n.height=0,n.color=new t.Color(1,1,1,1),n.offset=t.Utils.newFloatArray(8),n.uvs=t.Utils.newFloatArray(8),n.tempColor=new t.Color(1,1,1,1),n}return r(i,e),i.prototype.updateOffset=function(){var t=this.width/this.region.originalWidth*this.scaleX,e=this.height/this.region.originalHeight*this.scaleY,n=-this.width/2*this.scaleX+this.region.offsetX*t,r=-this.height/2*this.scaleY+this.region.offsetY*e,s=n+this.region.width*t,a=r+this.region.height*e,o=this.rotation*Math.PI/180,h=Math.cos(o),l=Math.sin(o),u=n*h+this.x,c=n*l,d=r*h+this.y,f=r*l,p=s*h+this.x,v=s*l,g=a*h+this.y,m=a*l,M=this.offset;M[i.OX1]=u-f,M[i.OY1]=d+c,M[i.OX2]=u-m,M[i.OY2]=g+c,M[i.OX3]=p-m,M[i.OY3]=g+v,M[i.OX4]=p-f,M[i.OY4]=d+v},i.prototype.setRegion=function(t){this.region=t;var e=this.uvs;t.rotate?(e[2]=t.u,e[3]=t.v2,e[4]=t.u,e[5]=t.v,e[6]=t.u2,e[7]=t.v,e[0]=t.u2,e[1]=t.v2):(e[0]=t.u,e[1]=t.v2,e[2]=t.u,e[3]=t.v,e[4]=t.u2,e[5]=t.v,e[6]=t.u2,e[7]=t.v2)},i.prototype.computeWorldVertices=function(t,e,n,r){var s=this.offset,a=t.worldX,o=t.worldY,h=t.a,l=t.b,u=t.c,c=t.d,d=0,f=0;d=s[i.OX1],f=s[i.OY1],e[n]=d*h+f*l+a,e[n+1]=d*u+f*c+o,n+=r,d=s[i.OX2],f=s[i.OY2],e[n]=d*h+f*l+a,e[n+1]=d*u+f*c+o,n+=r,d=s[i.OX3],f=s[i.OY3],e[n]=d*h+f*l+a,e[n+1]=d*u+f*c+o,n+=r,d=s[i.OX4],f=s[i.OY4],e[n]=d*h+f*l+a,e[n+1]=d*u+f*c+o},i.prototype.copy=function(){var e=new i(this.name);return e.region=this.region,e.rendererObject=this.rendererObject,e.path=this.path,e.x=this.x,e.y=this.y,e.scaleX=this.scaleX,e.scaleY=this.scaleY,e.rotation=this.rotation,e.width=this.width,e.height=this.height,t.Utils.arrayCopy(this.uvs,0,e.uvs,0,8),t.Utils.arrayCopy(this.offset,0,e.offset,0,8),e.color.setFromColor(this.color),e},i.OX1=0,i.OY1=1,i.OX2=2,i.OY2=3,i.OX3=4,i.OY3=5,i.OX4=6,i.OY4=7,i.X1=0,i.Y1=1,i.C1R=2,i.C1G=3,i.C1B=4,i.C1A=5,i.U1=6,i.V1=7,i.X2=8,i.Y2=9,i.C2R=10,i.C2G=11,i.C2B=12,i.C2A=13,i.U2=14,i.V2=15,i.X3=16,i.Y3=17,i.C3R=18,i.C3G=19,i.C3B=20,i.C3A=21,i.U3=22,i.V3=23,i.X4=24,i.Y4=25,i.C4R=26,i.C4G=27,i.C4B=28,i.C4A=29,i.U4=30,i.V4=31,i}(t.Attachment);t.RegionAttachment=e}(n||(n={})),function(t){var e=function(){function e(t,e){this.jitterX=0,this.jitterY=0,this.jitterX=t,this.jitterY=e}return e.prototype.begin=function(t){},e.prototype.transform=function(e,i,n,r){e.x+=t.MathUtils.randomTriangular(-this.jitterX,this.jitterY),e.y+=t.MathUtils.randomTriangular(-this.jitterX,this.jitterY)},e.prototype.end=function(){},e}();t.JitterEffect=e}(n||(n={})),function(t){var e=function(){function e(t){this.centerX=0,this.centerY=0,this.radius=0,this.angle=0,this.worldX=0,this.worldY=0,this.radius=t}return e.prototype.begin=function(t){this.worldX=t.x+this.centerX,this.worldY=t.y+this.centerY},e.prototype.transform=function(i,n,r,s){var a=this.angle*t.MathUtils.degreesToRadians,o=i.x-this.worldX,h=i.y-this.worldY,l=Math.sqrt(o*o+h*h);if(l<this.radius){var u=e.interpolation.apply(0,a,(this.radius-l)/this.radius),c=Math.cos(u),d=Math.sin(u);i.x=c*o-d*h+this.worldX,i.y=d*o+c*h+this.worldY}},e.prototype.end=function(){},e.interpolation=new t.PowOut(2),e}();t.SwirlEffect=e}(n||(n={})),function(t){!function(e){var i=function(e){function i(i,n){return void 0===n&&(n=""),e.call(this,(function(e){return new t.webgl.GLTexture(i,e)}),n)||this}return r(i,e),i}(t.AssetManager);e.AssetManager=i}(t.webgl||(t.webgl={}))}(n||(n={})),function(t){!function(t){var e=function(){function e(e,i){this.position=new t.Vector3(0,0,0),this.direction=new t.Vector3(0,0,-1),this.up=new t.Vector3(0,1,0),this.near=0,this.far=100,this.zoom=1,this.viewportWidth=0,this.viewportHeight=0,this.projectionView=new t.Matrix4,this.inverseProjectionView=new t.Matrix4,this.projection=new t.Matrix4,this.view=new t.Matrix4,this.tmp=new t.Vector3,this.viewportWidth=e,this.viewportHeight=i,this.update()}return e.prototype.update=function(){var t=this.projection,e=this.view,i=this.projectionView,n=this.inverseProjectionView,r=this.zoom,s=this.viewportWidth,a=this.viewportHeight;t.ortho(r*(-s/2),r*(s/2),r*(-a/2),r*(a/2),this.near,this.far),e.lookAt(this.position,this.direction,this.up),i.set(t.values),i.multiply(e),n.set(i.values).invert()},e.prototype.screenToWorld=function(t,e,i){var n=t.x,r=i-t.y-1,s=this.tmp;return s.x=2*n/e-1,s.y=2*r/i-1,s.z=2*t.z-1,s.project(this.inverseProjectionView),t.set(s.x,s.y,s.z),t},e.prototype.setViewport=function(t,e){this.viewportWidth=t,this.viewportHeight=e},e}();t.OrthoCamera=e}(t.webgl||(t.webgl={}))}(n||(n={})),function(t){!function(e){var i=function(i){function n(t,n,r){void 0===r&&(r=!1);var s=i.call(this,n)||this;return s.texture=null,s.boundUnit=0,s.useMipMaps=!1,s.context=t instanceof e.ManagedWebGLRenderingContext?t:new e.ManagedWebGLRenderingContext(t),s.useMipMaps=r,s.restore(),s.context.addRestorable(s),s}return r(n,i),n.prototype.setFilters=function(t,e){var i=this.context.gl;this.bind(),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_MIN_FILTER,t),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_MAG_FILTER,n.validateMagFilter(e))},n.validateMagFilter=function(e){switch(e){case t.TextureFilter.MipMap:case t.TextureFilter.MipMapLinearLinear:case t.TextureFilter.MipMapLinearNearest:case t.TextureFilter.MipMapNearestLinear:case t.TextureFilter.MipMapNearestNearest:return t.TextureFilter.Linear;default:return e}},n.prototype.setWraps=function(t,e){var i=this.context.gl;this.bind(),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_WRAP_S,t),i.texParameteri(i.TEXTURE_2D,i.TEXTURE_WRAP_T,e)},n.prototype.update=function(t){var e=this.context.gl;this.texture||(this.texture=this.context.gl.createTexture()),this.bind(),n.DISABLE_UNPACK_PREMULTIPLIED_ALPHA_WEBGL&&e.pixelStorei(e.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),e.texImage2D(e.TEXTURE_2D,0,e.RGBA,e.RGBA,e.UNSIGNED_BYTE,this._image),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,t?e.LINEAR_MIPMAP_LINEAR:e.LINEAR),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE),t&&e.generateMipmap(e.TEXTURE_2D)},n.prototype.restore=function(){this.texture=null,this.update(this.useMipMaps)},n.prototype.bind=function(t){void 0===t&&(t=0);var e=this.context.gl;this.boundUnit=t,e.activeTexture(e.TEXTURE0+t),e.bindTexture(e.TEXTURE_2D,this.texture)},n.prototype.unbind=function(){var t=this.context.gl;t.activeTexture(t.TEXTURE0+this.boundUnit),t.bindTexture(t.TEXTURE_2D,null)},n.prototype.dispose=function(){this.context.removeRestorable(this),this.context.gl.deleteTexture(this.texture)},n.DISABLE_UNPACK_PREMULTIPLIED_ALPHA_WEBGL=!1,n}(t.Texture);e.GLTexture=i}(t.webgl||(t.webgl={}))}(n||(n={})),function(t){!function(t){t.M00=0,t.M01=4,t.M02=8,t.M03=12,t.M10=1,t.M11=5,t.M12=9,t.M13=13,t.M20=2,t.M21=6,t.M22=10,t.M23=14,t.M30=3,t.M31=7,t.M32=11,t.M33=15;var e=function(){function e(){this.temp=new Float32Array(16),this.values=new Float32Array(16);var e=this.values;e[t.M00]=1,e[t.M11]=1,e[t.M22]=1,e[t.M33]=1}return e.prototype.set=function(t){return this.values.set(t),this},e.prototype.transpose=function(){var e=this.temp,i=this.values;return e[t.M00]=i[t.M00],e[t.M01]=i[t.M10],e[t.M02]=i[t.M20],e[t.M03]=i[t.M30],e[t.M10]=i[t.M01],e[t.M11]=i[t.M11],e[t.M12]=i[t.M21],e[t.M13]=i[t.M31],e[t.M20]=i[t.M02],e[t.M21]=i[t.M12],e[t.M22]=i[t.M22],e[t.M23]=i[t.M32],e[t.M30]=i[t.M03],e[t.M31]=i[t.M13],e[t.M32]=i[t.M23],e[t.M33]=i[t.M33],this.set(e)},e.prototype.identity=function(){var e=this.values;return e[t.M00]=1,e[t.M01]=0,e[t.M02]=0,e[t.M03]=0,e[t.M10]=0,e[t.M11]=1,e[t.M12]=0,e[t.M13]=0,e[t.M20]=0,e[t.M21]=0,e[t.M22]=1,e[t.M23]=0,e[t.M30]=0,e[t.M31]=0,e[t.M32]=0,e[t.M33]=1,this},e.prototype.invert=function(){var e=this.values,i=this.temp,n=e[t.M30]*e[t.M21]*e[t.M12]*e[t.M03]-e[t.M20]*e[t.M31]*e[t.M12]*e[t.M03]-e[t.M30]*e[t.M11]*e[t.M22]*e[t.M03]+e[t.M10]*e[t.M31]*e[t.M22]*e[t.M03]+e[t.M20]*e[t.M11]*e[t.M32]*e[t.M03]-e[t.M10]*e[t.M21]*e[t.M32]*e[t.M03]-e[t.M30]*e[t.M21]*e[t.M02]*e[t.M13]+e[t.M20]*e[t.M31]*e[t.M02]*e[t.M13]+e[t.M30]*e[t.M01]*e[t.M22]*e[t.M13]-e[t.M00]*e[t.M31]*e[t.M22]*e[t.M13]-e[t.M20]*e[t.M01]*e[t.M32]*e[t.M13]+e[t.M00]*e[t.M21]*e[t.M32]*e[t.M13]+e[t.M30]*e[t.M11]*e[t.M02]*e[t.M23]-e[t.M10]*e[t.M31]*e[t.M02]*e[t.M23]-e[t.M30]*e[t.M01]*e[t.M12]*e[t.M23]+e[t.M00]*e[t.M31]*e[t.M12]*e[t.M23]+e[t.M10]*e[t.M01]*e[t.M32]*e[t.M23]-e[t.M00]*e[t.M11]*e[t.M32]*e[t.M23]-e[t.M20]*e[t.M11]*e[t.M02]*e[t.M33]+e[t.M10]*e[t.M21]*e[t.M02]*e[t.M33]+e[t.M20]*e[t.M01]*e[t.M12]*e[t.M33]-e[t.M00]*e[t.M21]*e[t.M12]*e[t.M33]-e[t.M10]*e[t.M01]*e[t.M22]*e[t.M33]+e[t.M00]*e[t.M11]*e[t.M22]*e[t.M33];if(0==n)throw new Error("non-invertible matrix");var r=1/n;return i[t.M00]=e[t.M12]*e[t.M23]*e[t.M31]-e[t.M13]*e[t.M22]*e[t.M31]+e[t.M13]*e[t.M21]*e[t.M32]-e[t.M11]*e[t.M23]*e[t.M32]-e[t.M12]*e[t.M21]*e[t.M33]+e[t.M11]*e[t.M22]*e[t.M33],i[t.M01]=e[t.M03]*e[t.M22]*e[t.M31]-e[t.M02]*e[t.M23]*e[t.M31]-e[t.M03]*e[t.M21]*e[t.M32]+e[t.M01]*e[t.M23]*e[t.M32]+e[t.M02]*e[t.M21]*e[t.M33]-e[t.M01]*e[t.M22]*e[t.M33],i[t.M02]=e[t.M02]*e[t.M13]*e[t.M31]-e[t.M03]*e[t.M12]*e[t.M31]+e[t.M03]*e[t.M11]*e[t.M32]-e[t.M01]*e[t.M13]*e[t.M32]-e[t.M02]*e[t.M11]*e[t.M33]+e[t.M01]*e[t.M12]*e[t.M33],i[t.M03]=e[t.M03]*e[t.M12]*e[t.M21]-e[t.M02]*e[t.M13]*e[t.M21]-e[t.M03]*e[t.M11]*e[t.M22]+e[t.M01]*e[t.M13]*e[t.M22]+e[t.M02]*e[t.M11]*e[t.M23]-e[t.M01]*e[t.M12]*e[t.M23],i[t.M10]=e[t.M13]*e[t.M22]*e[t.M30]-e[t.M12]*e[t.M23]*e[t.M30]-e[t.M13]*e[t.M20]*e[t.M32]+e[t.M10]*e[t.M23]*e[t.M32]+e[t.M12]*e[t.M20]*e[t.M33]-e[t.M10]*e[t.M22]*e[t.M33],i[t.M11]=e[t.M02]*e[t.M23]*e[t.M30]-e[t.M03]*e[t.M22]*e[t.M30]+e[t.M03]*e[t.M20]*e[t.M32]-e[t.M00]*e[t.M23]*e[t.M32]-e[t.M02]*e[t.M20]*e[t.M33]+e[t.M00]*e[t.M22]*e[t.M33],i[t.M12]=e[t.M03]*e[t.M12]*e[t.M30]-e[t.M02]*e[t.M13]*e[t.M30]-e[t.M03]*e[t.M10]*e[t.M32]+e[t.M00]*e[t.M13]*e[t.M32]+e[t.M02]*e[t.M10]*e[t.M33]-e[t.M00]*e[t.M12]*e[t.M33],i[t.M13]=e[t.M02]*e[t.M13]*e[t.M20]-e[t.M03]*e[t.M12]*e[t.M20]+e[t.M03]*e[t.M10]*e[t.M22]-e[t.M00]*e[t.M13]*e[t.M22]-e[t.M02]*e[t.M10]*e[t.M23]+e[t.M00]*e[t.M12]*e[t.M23],i[t.M20]=e[t.M11]*e[t.M23]*e[t.M30]-e[t.M13]*e[t.M21]*e[t.M30]+e[t.M13]*e[t.M20]*e[t.M31]-e[t.M10]*e[t.M23]*e[t.M31]-e[t.M11]*e[t.M20]*e[t.M33]+e[t.M10]*e[t.M21]*e[t.M33],i[t.M21]=e[t.M03]*e[t.M21]*e[t.M30]-e[t.M01]*e[t.M23]*e[t.M30]-e[t.M03]*e[t.M20]*e[t.M31]+e[t.M00]*e[t.M23]*e[t.M31]+e[t.M01]*e[t.M20]*e[t.M33]-e[t.M00]*e[t.M21]*e[t.M33],i[t.M22]=e[t.M01]*e[t.M13]*e[t.M30]-e[t.M03]*e[t.M11]*e[t.M30]+e[t.M03]*e[t.M10]*e[t.M31]-e[t.M00]*e[t.M13]*e[t.M31]-e[t.M01]*e[t.M10]*e[t.M33]+e[t.M00]*e[t.M11]*e[t.M33],i[t.M23]=e[t.M03]*e[t.M11]*e[t.M20]-e[t.M01]*e[t.M13]*e[t.M20]-e[t.M03]*e[t.M10]*e[t.M21]+e[t.M00]*e[t.M13]*e[t.M21]+e[t.M01]*e[t.M10]*e[t.M23]-e[t.M00]*e[t.M11]*e[t.M23],i[t.M30]=e[t.M12]*e[t.M21]*e[t.M30]-e[t.M11]*e[t.M22]*e[t.M30]-e[t.M12]*e[t.M20]*e[t.M31]+e[t.M10]*e[t.M22]*e[t.M31]+e[t.M11]*e[t.M20]*e[t.M32]-e[t.M10]*e[t.M21]*e[t.M32],i[t.M31]=e[t.M01]*e[t.M22]*e[t.M30]-e[t.M02]*e[t.M21]*e[t.M30]+e[t.M02]*e[t.M20]*e[t.M31]-e[t.M00]*e[t.M22]*e[t.M31]-e[t.M01]*e[t.M20]*e[t.M32]+e[t.M00]*e[t.M21]*e[t.M32],i[t.M32]=e[t.M02]*e[t.M11]*e[t.M30]-e[t.M01]*e[t.M12]*e[t.M30]-e[t.M02]*e[t.M10]*e[t.M31]+e[t.M00]*e[t.M12]*e[t.M31]+e[t.M01]*e[t.M10]*e[t.M32]-e[t.M00]*e[t.M11]*e[t.M32],i[t.M33]=e[t.M01]*e[t.M12]*e[t.M20]-e[t.M02]*e[t.M11]*e[t.M20]+e[t.M02]*e[t.M10]*e[t.M21]-e[t.M00]*e[t.M12]*e[t.M21]-e[t.M01]*e[t.M10]*e[t.M22]+e[t.M00]*e[t.M11]*e[t.M22],e[t.M00]=i[t.M00]*r,e[t.M01]=i[t.M01]*r,e[t.M02]=i[t.M02]*r,e[t.M03]=i[t.M03]*r,e[t.M10]=i[t.M10]*r,e[t.M11]=i[t.M11]*r,e[t.M12]=i[t.M12]*r,e[t.M13]=i[t.M13]*r,e[t.M20]=i[t.M20]*r,e[t.M21]=i[t.M21]*r,e[t.M22]=i[t.M22]*r,e[t.M23]=i[t.M23]*r,e[t.M30]=i[t.M30]*r,e[t.M31]=i[t.M31]*r,e[t.M32]=i[t.M32]*r,e[t.M33]=i[t.M33]*r,this},e.prototype.determinant=function(){var e=this.values;return e[t.M30]*e[t.M21]*e[t.M12]*e[t.M03]-e[t.M20]*e[t.M31]*e[t.M12]*e[t.M03]-e[t.M30]*e[t.M11]*e[t.M22]*e[t.M03]+e[t.M10]*e[t.M31]*e[t.M22]*e[t.M03]+e[t.M20]*e[t.M11]*e[t.M32]*e[t.M03]-e[t.M10]*e[t.M21]*e[t.M32]*e[t.M03]-e[t.M30]*e[t.M21]*e[t.M02]*e[t.M13]+e[t.M20]*e[t.M31]*e[t.M02]*e[t.M13]+e[t.M30]*e[t.M01]*e[t.M22]*e[t.M13]-e[t.M00]*e[t.M31]*e[t.M22]*e[t.M13]-e[t.M20]*e[t.M01]*e[t.M32]*e[t.M13]+e[t.M00]*e[t.M21]*e[t.M32]*e[t.M13]+e[t.M30]*e[t.M11]*e[t.M02]*e[t.M23]-e[t.M10]*e[t.M31]*e[t.M02]*e[t.M23]-e[t.M30]*e[t.M01]*e[t.M12]*e[t.M23]+e[t.M00]*e[t.M31]*e[t.M12]*e[t.M23]+e[t.M10]*e[t.M01]*e[t.M32]*e[t.M23]-e[t.M00]*e[t.M11]*e[t.M32]*e[t.M23]-e[t.M20]*e[t.M11]*e[t.M02]*e[t.M33]+e[t.M10]*e[t.M21]*e[t.M02]*e[t.M33]+e[t.M20]*e[t.M01]*e[t.M12]*e[t.M33]-e[t.M00]*e[t.M21]*e[t.M12]*e[t.M33]-e[t.M10]*e[t.M01]*e[t.M22]*e[t.M33]+e[t.M00]*e[t.M11]*e[t.M22]*e[t.M33]},e.prototype.translate=function(e,i,n){var r=this.values;return r[t.M03]+=e,r[t.M13]+=i,r[t.M23]+=n,this},e.prototype.copy=function(){return(new e).set(this.values)},e.prototype.projection=function(e,i,n,r){this.identity();var s=1/Math.tan(n*(Math.PI/180)/2),a=(i+e)/(e-i),o=2*i*e/(e-i),h=this.values;return h[t.M00]=s/r,h[t.M10]=0,h[t.M20]=0,h[t.M30]=0,h[t.M01]=0,h[t.M11]=s,h[t.M21]=0,h[t.M31]=0,h[t.M02]=0,h[t.M12]=0,h[t.M22]=a,h[t.M32]=-1,h[t.M03]=0,h[t.M13]=0,h[t.M23]=o,h[t.M33]=0,this},e.prototype.ortho2d=function(t,e,i,n){return this.ortho(t,t+i,e,e+n,0,1)},e.prototype.ortho=function(e,i,n,r,s,a){this.identity();var o=2/(i-e),h=2/(r-n),l=-2/(a-s),u=-(i+e)/(i-e),c=-(r+n)/(r-n),d=-(a+s)/(a-s),f=this.values;return f[t.M00]=o,f[t.M10]=0,f[t.M20]=0,f[t.M30]=0,f[t.M01]=0,f[t.M11]=h,f[t.M21]=0,f[t.M31]=0,f[t.M02]=0,f[t.M12]=0,f[t.M22]=l,f[t.M32]=0,f[t.M03]=u,f[t.M13]=c,f[t.M23]=d,f[t.M33]=1,this},e.prototype.multiply=function(e){var i=this.temp,n=this.values,r=e.values;return i[t.M00]=n[t.M00]*r[t.M00]+n[t.M01]*r[t.M10]+n[t.M02]*r[t.M20]+n[t.M03]*r[t.M30],i[t.M01]=n[t.M00]*r[t.M01]+n[t.M01]*r[t.M11]+n[t.M02]*r[t.M21]+n[t.M03]*r[t.M31],i[t.M02]=n[t.M00]*r[t.M02]+n[t.M01]*r[t.M12]+n[t.M02]*r[t.M22]+n[t.M03]*r[t.M32],i[t.M03]=n[t.M00]*r[t.M03]+n[t.M01]*r[t.M13]+n[t.M02]*r[t.M23]+n[t.M03]*r[t.M33],i[t.M10]=n[t.M10]*r[t.M00]+n[t.M11]*r[t.M10]+n[t.M12]*r[t.M20]+n[t.M13]*r[t.M30],i[t.M11]=n[t.M10]*r[t.M01]+n[t.M11]*r[t.M11]+n[t.M12]*r[t.M21]+n[t.M13]*r[t.M31],i[t.M12]=n[t.M10]*r[t.M02]+n[t.M11]*r[t.M12]+n[t.M12]*r[t.M22]+n[t.M13]*r[t.M32],i[t.M13]=n[t.M10]*r[t.M03]+n[t.M11]*r[t.M13]+n[t.M12]*r[t.M23]+n[t.M13]*r[t.M33],i[t.M20]=n[t.M20]*r[t.M00]+n[t.M21]*r[t.M10]+n[t.M22]*r[t.M20]+n[t.M23]*r[t.M30],i[t.M21]=n[t.M20]*r[t.M01]+n[t.M21]*r[t.M11]+n[t.M22]*r[t.M21]+n[t.M23]*r[t.M31],i[t.M22]=n[t.M20]*r[t.M02]+n[t.M21]*r[t.M12]+n[t.M22]*r[t.M22]+n[t.M23]*r[t.M32],i[t.M23]=n[t.M20]*r[t.M03]+n[t.M21]*r[t.M13]+n[t.M22]*r[t.M23]+n[t.M23]*r[t.M33],i[t.M30]=n[t.M30]*r[t.M00]+n[t.M31]*r[t.M10]+n[t.M32]*r[t.M20]+n[t.M33]*r[t.M30],i[t.M31]=n[t.M30]*r[t.M01]+n[t.M31]*r[t.M11]+n[t.M32]*r[t.M21]+n[t.M33]*r[t.M31],i[t.M32]=n[t.M30]*r[t.M02]+n[t.M31]*r[t.M12]+n[t.M32]*r[t.M22]+n[t.M33]*r[t.M32],i[t.M33]=n[t.M30]*r[t.M03]+n[t.M31]*r[t.M13]+n[t.M32]*r[t.M23]+n[t.M33]*r[t.M33],this.set(this.temp)},e.prototype.multiplyLeft=function(e){var i=this.temp,n=this.values,r=e.values;return i[t.M00]=r[t.M00]*n[t.M00]+r[t.M01]*n[t.M10]+r[t.M02]*n[t.M20]+r[t.M03]*n[t.M30],i[t.M01]=r[t.M00]*n[t.M01]+r[t.M01]*n[t.M11]+r[t.M02]*n[t.M21]+r[t.M03]*n[t.M31],i[t.M02]=r[t.M00]*n[t.M02]+r[t.M01]*n[t.M12]+r[t.M02]*n[t.M22]+r[t.M03]*n[t.M32],i[t.M03]=r[t.M00]*n[t.M03]+r[t.M01]*n[t.M13]+r[t.M02]*n[t.M23]+r[t.M03]*n[t.M33],i[t.M10]=r[t.M10]*n[t.M00]+r[t.M11]*n[t.M10]+r[t.M12]*n[t.M20]+r[t.M13]*n[t.M30],i[t.M11]=r[t.M10]*n[t.M01]+r[t.M11]*n[t.M11]+r[t.M12]*n[t.M21]+r[t.M13]*n[t.M31],i[t.M12]=r[t.M10]*n[t.M02]+r[t.M11]*n[t.M12]+r[t.M12]*n[t.M22]+r[t.M13]*n[t.M32],i[t.M13]=r[t.M10]*n[t.M03]+r[t.M11]*n[t.M13]+r[t.M12]*n[t.M23]+r[t.M13]*n[t.M33],i[t.M20]=r[t.M20]*n[t.M00]+r[t.M21]*n[t.M10]+r[t.M22]*n[t.M20]+r[t.M23]*n[t.M30],i[t.M21]=r[t.M20]*n[t.M01]+r[t.M21]*n[t.M11]+r[t.M22]*n[t.M21]+r[t.M23]*n[t.M31],i[t.M22]=r[t.M20]*n[t.M02]+r[t.M21]*n[t.M12]+r[t.M22]*n[t.M22]+r[t.M23]*n[t.M32],i[t.M23]=r[t.M20]*n[t.M03]+r[t.M21]*n[t.M13]+r[t.M22]*n[t.M23]+r[t.M23]*n[t.M33],i[t.M30]=r[t.M30]*n[t.M00]+r[t.M31]*n[t.M10]+r[t.M32]*n[t.M20]+r[t.M33]*n[t.M30],i[t.M31]=r[t.M30]*n[t.M01]+r[t.M31]*n[t.M11]+r[t.M32]*n[t.M21]+r[t.M33]*n[t.M31],i[t.M32]=r[t.M30]*n[t.M02]+r[t.M31]*n[t.M12]+r[t.M32]*n[t.M22]+r[t.M33]*n[t.M32],i[t.M33]=r[t.M30]*n[t.M03]+r[t.M31]*n[t.M13]+r[t.M32]*n[t.M23]+r[t.M33]*n[t.M33],this.set(this.temp)},e.prototype.lookAt=function(i,n,r){e.initTemps();var s=e.xAxis,a=e.yAxis,o=e.zAxis;o.setFrom(n).normalize(),s.setFrom(n).normalize(),s.cross(r).normalize(),a.setFrom(s).cross(o).normalize(),this.identity();var h=this.values;return h[t.M00]=s.x,h[t.M01]=s.y,h[t.M02]=s.z,h[t.M10]=a.x,h[t.M11]=a.y,h[t.M12]=a.z,h[t.M20]=-o.x,h[t.M21]=-o.y,h[t.M22]=-o.z,e.tmpMatrix.identity(),e.tmpMatrix.values[t.M03]=-i.x,e.tmpMatrix.values[t.M13]=-i.y,e.tmpMatrix.values[t.M23]=-i.z,this.multiply(e.tmpMatrix),this},e.initTemps=function(){null===e.xAxis&&(e.xAxis=new t.Vector3),null===e.yAxis&&(e.yAxis=new t.Vector3),null===e.zAxis&&(e.zAxis=new t.Vector3)},e.xAxis=null,e.yAxis=null,e.zAxis=null,e.tmpMatrix=new e,e}();t.Matrix4=e}(t.webgl||(t.webgl={}))}(n||(n={})),function(t){!function(t){var e=function(){function e(e,i,n,r){this.attributes=i,this.verticesLength=0,this.dirtyVertices=!1,this.indicesLength=0,this.dirtyIndices=!1,this.elementsPerVertex=0,this.context=e instanceof t.ManagedWebGLRenderingContext?e:new t.ManagedWebGLRenderingContext(e),this.elementsPerVertex=0;for(var s=0;s<i.length;s++)this.elementsPerVertex+=i[s].numElements;this.vertices=new Float32Array(n*this.elementsPerVertex),this.indices=new Uint16Array(r),this.context.addRestorable(this)}return e.prototype.getAttributes=function(){return this.attributes},e.prototype.maxVertices=function(){return this.vertices.length/this.elementsPerVertex},e.prototype.numVertices=function(){return this.verticesLength/this.elementsPerVertex},e.prototype.setVerticesLength=function(t){this.dirtyVertices=!0,this.verticesLength=t},e.prototype.getVertices=function(){return this.vertices},e.prototype.maxIndices=function(){return this.indices.length},e.prototype.numIndices=function(){return this.indicesLength},e.prototype.setIndicesLength=function(t){this.dirtyIndices=!0,this.indicesLength=t},e.prototype.getIndices=function(){return this.indices},e.prototype.getVertexSizeInFloats=function(){for(var t=0,e=0;e<this.attributes.length;e++){t+=this.attributes[e].numElements}return t},e.prototype.setVertices=function(t){if(this.dirtyVertices=!0,t.length>this.vertices.length)throw Error("Mesh can't store more than "+this.maxVertices()+" vertices");this.vertices.set(t,0),this.verticesLength=t.length},e.prototype.setIndices=function(t){if(this.dirtyIndices=!0,t.length>this.indices.length)throw Error("Mesh can't store more than "+this.maxIndices()+" indices");this.indices.set(t,0),this.indicesLength=t.length},e.prototype.draw=function(t,e){this.drawWithOffset(t,e,0,this.indicesLength>0?this.indicesLength:this.verticesLength/this.elementsPerVertex)},e.prototype.drawWithOffset=function(t,e,i,n){var r=this.context.gl;(this.dirtyVertices||this.dirtyIndices)&&this.update(),this.bind(t),this.indicesLength>0?r.drawElements(e,n,r.UNSIGNED_SHORT,2*i):r.drawArrays(e,i,n),this.unbind(t)},e.prototype.bind=function(t){var e=this.context.gl;e.bindBuffer(e.ARRAY_BUFFER,this.verticesBuffer);for(var i=0,n=0;n<this.attributes.length;n++){var r=this.attributes[n],s=t.getAttributeLocation(r.name);e.enableVertexAttribArray(s),e.vertexAttribPointer(s,r.numElements,e.FLOAT,!1,4*this.elementsPerVertex,4*i),i+=r.numElements}this.indicesLength>0&&e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,this.indicesBuffer)},e.prototype.unbind=function(t){for(var e=this.context.gl,i=0;i<this.attributes.length;i++){var n=this.attributes[i],r=t.getAttributeLocation(n.name);e.disableVertexAttribArray(r)}e.bindBuffer(e.ARRAY_BUFFER,null),this.indicesLength>0&&e.bindBuffer(e.ELEMENT_ARRAY_BUFFER,null)},e.prototype.update=function(){var t=this.context.gl;this.dirtyVertices&&(this.verticesBuffer||(this.verticesBuffer=t.createBuffer()),t.bindBuffer(t.ARRAY_BUFFER,this.verticesBuffer),t.bufferData(t.ARRAY_BUFFER,this.vertices.subarray(0,this.verticesLength),t.DYNAMIC_DRAW),this.dirtyVertices=!1),this.dirtyIndices&&(this.indicesBuffer||(this.indicesBuffer=t.createBuffer()),t.bindBuffer(t.ELEMENT_ARRAY_BUFFER,this.indicesBuffer),t.bufferData(t.ELEMENT_ARRAY_BUFFER,this.indices.subarray(0,this.indicesLength),t.DYNAMIC_DRAW),this.dirtyIndices=!1)},e.prototype.restore=function(){this.verticesBuffer=null,this.indicesBuffer=null,this.update()},e.prototype.dispose=function(){this.context.removeRestorable(this);var t=this.context.gl;t.deleteBuffer(this.verticesBuffer),t.deleteBuffer(this.indicesBuffer)},e}();t.Mesh=e;var i=function(t,e,i){this.name=t,this.type=e,this.numElements=i};t.VertexAttribute=i;var n=function(e){function i(){return e.call(this,t.Shader.POSITION,h.Float,2)||this}return r(i,e),i}(i);t.Position2Attribute=n;var s=function(e){function i(){return e.call(this,t.Shader.POSITION,h.Float,3)||this}return r(i,e),i}(i);t.Position3Attribute=s;var a=function(e){function i(i){return void 0===i&&(i=0),e.call(this,t.Shader.TEXCOORDS+(0==i?"":i),h.Float,2)||this}return r(i,e),i}(i);t.TexCoordAttribute=a;var o=function(e){function i(){return e.call(this,t.Shader.COLOR,h.Float,4)||this}return r(i,e),i}(i);t.ColorAttribute=o;var h,l=function(e){function i(){return e.call(this,t.Shader.COLOR2,h.Float,4)||this}return r(i,e),i}(i);t.Color2Attribute=l,function(t){t[t.Float=0]="Float"}(h=t.VertexAttributeType||(t.VertexAttributeType={}))}(t.webgl||(t.webgl={}))}(n||(n={})),function(t){!function(t){var e=function(){function e(e,i,n){if(void 0===i&&(i=!0),void 0===n&&(n=10920),this.isDrawing=!1,this.shader=null,this.lastTexture=null,this.verticesLength=0,this.indicesLength=0,n>10920)throw new Error("Can't have more than 10920 triangles per batch: "+n);this.context=e instanceof t.ManagedWebGLRenderingContext?e:new t.ManagedWebGLRenderingContext(e);var r=i?[new t.Position2Attribute,new t.ColorAttribute,new t.TexCoordAttribute,new t.Color2Attribute]:[new t.Position2Attribute,new t.ColorAttribute,new t.TexCoordAttribute];this.mesh=new t.Mesh(e,r,n,3*n),this.srcBlend=this.context.gl.SRC_ALPHA,this.dstBlend=this.context.gl.ONE_MINUS_SRC_ALPHA}return e.prototype.begin=function(t){var e=this.context.gl;if(this.isDrawing)throw new Error("PolygonBatch is already drawing. Call PolygonBatch.end() before calling PolygonBatch.begin()");this.drawCalls=0,this.shader=t,this.lastTexture=null,this.isDrawing=!0,e.enable(e.BLEND),e.blendFunc(this.srcBlend,this.dstBlend)},e.prototype.setBlendMode=function(t,e){var i=this.context.gl;this.srcBlend=t,this.dstBlend=e,this.isDrawing&&(this.flush(),i.blendFunc(this.srcBlend,this.dstBlend))},e.prototype.draw=function(t,e,i){t!=this.lastTexture?(this.flush(),this.lastTexture=t):(this.verticesLength+e.length>this.mesh.getVertices().length||this.indicesLength+i.length>this.mesh.getIndices().length)&&this.flush();var n=this.mesh.numVertices();this.mesh.getVertices().set(e,this.verticesLength),this.verticesLength+=e.length,this.mesh.setVerticesLength(this.verticesLength);for(var r=this.mesh.getIndices(),s=this.indicesLength,a=0;a<i.length;s++,a++)r[s]=i[a]+n;this.indicesLength+=i.length,this.mesh.setIndicesLength(this.indicesLength)},e.prototype.flush=function(){var t=this.context.gl;0!=this.verticesLength&&(this.lastTexture.bind(),this.mesh.draw(this.shader,t.TRIANGLES),this.verticesLength=0,this.indicesLength=0,this.mesh.setVerticesLength(0),this.mesh.setIndicesLength(0),this.drawCalls++)},e.prototype.end=function(){var t=this.context.gl;if(!this.isDrawing)throw new Error("PolygonBatch is not drawing. Call PolygonBatch.begin() before calling PolygonBatch.end()");(this.verticesLength>0||this.indicesLength>0)&&this.flush(),this.shader=null,this.lastTexture=null,this.isDrawing=!1,t.disable(t.BLEND)},e.prototype.getDrawCalls=function(){return this.drawCalls},e.prototype.dispose=function(){this.mesh.dispose()},e}();t.PolygonBatcher=e}(t.webgl||(t.webgl={}))}(n||(n={})),function(t){!function(e){var i,n=function(){function n(i,n,r){void 0===r&&(r=!0),this.twoColorTint=!1,this.activeRenderer=null,this.QUAD=[0,0,1,1,1,1,0,0,0,0,1,1,1,1,0,0,0,0,1,1,1,1,0,0,0,0,1,1,1,1,0,0],this.QUAD_TRIANGLES=[0,1,2,2,3,0],this.WHITE=new t.Color(1,1,1,1),this.canvas=i,this.context=n instanceof e.ManagedWebGLRenderingContext?n:new e.ManagedWebGLRenderingContext(n),this.twoColorTint=r,this.camera=new e.OrthoCamera(i.width,i.height),this.batcherShader=r?e.Shader.newTwoColoredTextured(this.context):e.Shader.newColoredTextured(this.context),this.batcher=new e.PolygonBatcher(this.context,r),this.shapesShader=e.Shader.newColored(this.context),this.shapes=new e.ShapeRenderer(this.context),this.skeletonRenderer=new e.SkeletonRenderer(this.context,r),this.skeletonDebugRenderer=new e.SkeletonDebugRenderer(this.context)}return n.prototype.begin=function(){this.camera.update(),this.enableRenderer(this.batcher)},n.prototype.drawSkeleton=function(t,e,i,n){void 0===e&&(e=!1),void 0===i&&(i=-1),void 0===n&&(n=-1),this.enableRenderer(this.batcher),this.skeletonRenderer.premultipliedAlpha=e,this.skeletonRenderer.draw(this.batcher,t,i,n)},n.prototype.drawSkeletonDebug=function(t,e,i){void 0===e&&(e=!1),void 0===i&&(i=null),this.enableRenderer(this.shapes),this.skeletonDebugRenderer.premultipliedAlpha=e,this.skeletonDebugRenderer.draw(this.shapes,t,i)},n.prototype.drawTexture=function(t,e,i,n,r,s){void 0===s&&(s=null),this.enableRenderer(this.batcher),null===s&&(s=this.WHITE);var a=this.QUAD,o=0;a[o++]=e,a[o++]=i,a[o++]=s.r,a[o++]=s.g,a[o++]=s.b,a[o++]=s.a,a[o++]=0,a[o++]=1,this.twoColorTint&&(a[o++]=0,a[o++]=0,a[o++]=0,a[o++]=0),a[o++]=e+n,a[o++]=i,a[o++]=s.r,a[o++]=s.g,a[o++]=s.b,a[o++]=s.a,a[o++]=1,a[o++]=1,this.twoColorTint&&(a[o++]=0,a[o++]=0,a[o++]=0,a[o++]=0),a[o++]=e+n,a[o++]=i+r,a[o++]=s.r,a[o++]=s.g,a[o++]=s.b,a[o++]=s.a,a[o++]=1,a[o++]=0,this.twoColorTint&&(a[o++]=0,a[o++]=0,a[o++]=0,a[o++]=0),a[o++]=e,a[o++]=i+r,a[o++]=s.r,a[o++]=s.g,a[o++]=s.b,a[o++]=s.a,a[o++]=0,a[o++]=0,this.twoColorTint&&(a[o++]=0,a[o++]=0,a[o++]=0,a[o++]=0),this.batcher.draw(t,a,this.QUAD_TRIANGLES)},n.prototype.drawTextureUV=function(t,e,i,n,r,s,a,o,h,l){void 0===l&&(l=null),this.enableRenderer(this.batcher),null===l&&(l=this.WHITE);var u=this.QUAD,c=0;u[c++]=e,u[c++]=i,u[c++]=l.r,u[c++]=l.g,u[c++]=l.b,u[c++]=l.a,u[c++]=s,u[c++]=a,this.twoColorTint&&(u[c++]=0,u[c++]=0,u[c++]=0,u[c++]=0),u[c++]=e+n,u[c++]=i,u[c++]=l.r,u[c++]=l.g,u[c++]=l.b,u[c++]=l.a,u[c++]=o,u[c++]=a,this.twoColorTint&&(u[c++]=0,u[c++]=0,u[c++]=0,u[c++]=0),u[c++]=e+n,u[c++]=i+r,u[c++]=l.r,u[c++]=l.g,u[c++]=l.b,u[c++]=l.a,u[c++]=o,u[c++]=h,this.twoColorTint&&(u[c++]=0,u[c++]=0,u[c++]=0,u[c++]=0),u[c++]=e,u[c++]=i+r,u[c++]=l.r,u[c++]=l.g,u[c++]=l.b,u[c++]=l.a,u[c++]=s,u[c++]=h,this.twoColorTint&&(u[c++]=0,u[c++]=0,u[c++]=0,u[c++]=0),this.batcher.draw(t,u,this.QUAD_TRIANGLES)},n.prototype.drawTextureRotated=function(e,i,n,r,s,a,o,h,l,u){void 0===l&&(l=null),void 0===u&&(u=!1),this.enableRenderer(this.batcher),null===l&&(l=this.WHITE);var c=this.QUAD,d=i+a,f=n+o,p=-a,v=-o,g=r-a,m=s-o,M=p,x=v,y=p,w=m,E=g,b=m,T=g,A=v,S=0,R=0,C=0,I=0,P=0,L=0,O=0,_=0;if(0!=h){var k=t.MathUtils.cosDeg(h),F=t.MathUtils.sinDeg(h);C=(P=k*E-F*b)+((S=k*M-F*x)-(O=k*y-F*w)),I=(L=F*E+k*b)+((R=F*M+k*x)-(_=F*y+k*w))}else S=M,R=x,O=y,_=w,P=E,L=b,C=T,I=A;S+=d,R+=f,C+=d,I+=f,P+=d,L+=f,O+=d,_+=f;var D=0;c[D++]=S,c[D++]=R,c[D++]=l.r,c[D++]=l.g,c[D++]=l.b,c[D++]=l.a,c[D++]=0,c[D++]=1,this.twoColorTint&&(c[D++]=0,c[D++]=0,c[D++]=0,c[D++]=0),c[D++]=C,c[D++]=I,c[D++]=l.r,c[D++]=l.g,c[D++]=l.b,c[D++]=l.a,c[D++]=1,c[D++]=1,this.twoColorTint&&(c[D++]=0,c[D++]=0,c[D++]=0,c[D++]=0),c[D++]=P,c[D++]=L,c[D++]=l.r,c[D++]=l.g,c[D++]=l.b,c[D++]=l.a,c[D++]=1,c[D++]=0,this.twoColorTint&&(c[D++]=0,c[D++]=0,c[D++]=0,c[D++]=0),c[D++]=O,c[D++]=_,c[D++]=l.r,c[D++]=l.g,c[D++]=l.b,c[D++]=l.a,c[D++]=0,c[D++]=0,this.twoColorTint&&(c[D++]=0,c[D++]=0,c[D++]=0,c[D++]=0),this.batcher.draw(e,c,this.QUAD_TRIANGLES)},n.prototype.drawRegion=function(t,e,i,n,r,s,a){void 0===s&&(s=null),void 0===a&&(a=!1),this.enableRenderer(this.batcher),null===s&&(s=this.WHITE);var o=this.QUAD,h=0;o[h++]=e,o[h++]=i,o[h++]=s.r,o[h++]=s.g,o[h++]=s.b,o[h++]=s.a,o[h++]=t.u,o[h++]=t.v2,this.twoColorTint&&(o[h++]=0,o[h++]=0,o[h++]=0,o[h++]=0),o[h++]=e+n,o[h++]=i,o[h++]=s.r,o[h++]=s.g,o[h++]=s.b,o[h++]=s.a,o[h++]=t.u2,o[h++]=t.v2,this.twoColorTint&&(o[h++]=0,o[h++]=0,o[h++]=0,o[h++]=0),o[h++]=e+n,o[h++]=i+r,o[h++]=s.r,o[h++]=s.g,o[h++]=s.b,o[h++]=s.a,o[h++]=t.u2,o[h++]=t.v,this.twoColorTint&&(o[h++]=0,o[h++]=0,o[h++]=0,o[h++]=0),o[h++]=e,o[h++]=i+r,o[h++]=s.r,o[h++]=s.g,o[h++]=s.b,o[h++]=s.a,o[h++]=t.u,o[h++]=t.v,this.twoColorTint&&(o[h++]=0,o[h++]=0,o[h++]=0,o[h++]=0),this.batcher.draw(t.texture,o,this.QUAD_TRIANGLES)},n.prototype.line=function(t,e,i,n,r,s){void 0===r&&(r=null),void 0===s&&(s=null),this.enableRenderer(this.shapes),this.shapes.line(t,e,i,n,r)},n.prototype.triangle=function(t,e,i,n,r,s,a,o,h,l){void 0===o&&(o=null),void 0===h&&(h=null),void 0===l&&(l=null),this.enableRenderer(this.shapes),this.shapes.triangle(t,e,i,n,r,s,a,o,h,l)},n.prototype.quad=function(t,e,i,n,r,s,a,o,h,l,u,c,d){void 0===l&&(l=null),void 0===u&&(u=null),void 0===c&&(c=null),void 0===d&&(d=null),this.enableRenderer(this.shapes),this.shapes.quad(t,e,i,n,r,s,a,o,h,l,u,c,d)},n.prototype.rect=function(t,e,i,n,r,s){void 0===s&&(s=null),this.enableRenderer(this.shapes),this.shapes.rect(t,e,i,n,r,s)},n.prototype.rectLine=function(t,e,i,n,r,s,a){void 0===a&&(a=null),this.enableRenderer(this.shapes),this.shapes.rectLine(t,e,i,n,r,s,a)},n.prototype.polygon=function(t,e,i,n){void 0===n&&(n=null),this.enableRenderer(this.shapes),this.shapes.polygon(t,e,i,n)},n.prototype.circle=function(t,e,i,n,r,s){void 0===r&&(r=null),void 0===s&&(s=0),this.enableRenderer(this.shapes),this.shapes.circle(t,e,i,n,r,s)},n.prototype.curve=function(t,e,i,n,r,s,a,o,h,l){void 0===l&&(l=null),this.enableRenderer(this.shapes),this.shapes.curve(t,e,i,n,r,s,a,o,h,l)},n.prototype.end=function(){this.activeRenderer===this.batcher?this.batcher.end():this.activeRenderer===this.shapes&&this.shapes.end(),this.activeRenderer=null},n.prototype.resize=function(t){var e=this.canvas,n=e.clientWidth,r=e.clientHeight;if(e.width==n&&e.height==r||(e.width=n,e.height=r),this.context.gl.viewport(0,0,e.width,e.height),t===i.Stretch);else if(t===i.Expand)this.camera.setViewport(n,r);else if(t===i.Fit){var s=e.width,a=e.height,o=this.camera.viewportWidth,h=this.camera.viewportHeight,l=h/o<a/s?o/s:h/a;this.camera.viewportWidth=s*l,this.camera.viewportHeight=a*l}this.camera.update()},n.prototype.enableRenderer=function(t){this.activeRenderer!==t&&(this.end(),t instanceof e.PolygonBatcher?(this.batcherShader.bind(),this.batcherShader.setUniform4x4f(e.Shader.MVP_MATRIX,this.camera.projectionView.values),this.batcherShader.setUniformi("u_texture",0),this.batcher.begin(this.batcherShader),this.activeRenderer=this.batcher):t instanceof e.ShapeRenderer?(this.shapesShader.bind(),this.shapesShader.setUniform4x4f(e.Shader.MVP_MATRIX,this.camera.projectionView.values),this.shapes.begin(this.shapesShader),this.activeRenderer=this.shapes):this.activeRenderer=this.skeletonDebugRenderer)},n.prototype.dispose=function(){this.batcher.dispose(),this.batcherShader.dispose(),this.shapes.dispose(),this.shapesShader.dispose(),this.skeletonDebugRenderer.dispose()},n}();e.SceneRenderer=n,function(t){t[t.Stretch=0]="Stretch",t[t.Expand=1]="Expand",t[t.Fit=2]="Fit"}(i=e.ResizeMode||(e.ResizeMode={}))}(t.webgl||(t.webgl={}))}(n||(n={})),function(t){!function(t){var e=function(){function e(e,i,n){this.vertexShader=i,this.fragmentShader=n,this.vs=null,this.fs=null,this.program=null,this.tmp2x2=new Float32Array(4),this.tmp3x3=new Float32Array(9),this.tmp4x4=new Float32Array(16),this.vsSource=i,this.fsSource=n,this.context=e instanceof t.ManagedWebGLRenderingContext?e:new t.ManagedWebGLRenderingContext(e),this.context.addRestorable(this),this.compile()}return e.prototype.getProgram=function(){return this.program},e.prototype.getVertexShader=function(){return this.vertexShader},e.prototype.getFragmentShader=function(){return this.fragmentShader},e.prototype.getVertexShaderSource=function(){return this.vsSource},e.prototype.getFragmentSource=function(){return this.fsSource},e.prototype.compile=function(){var t=this.context.gl;try{this.vs=this.compileShader(t.VERTEX_SHADER,this.vertexShader),this.fs=this.compileShader(t.FRAGMENT_SHADER,this.fragmentShader),this.program=this.compileProgram(this.vs,this.fs)}catch(t){throw this.dispose(),t}},e.prototype.compileShader=function(t,e){var i=this.context.gl,n=i.createShader(t);if(i.shaderSource(n,e),i.compileShader(n),!i.getShaderParameter(n,i.COMPILE_STATUS)){var r="Couldn't compile shader: "+i.getShaderInfoLog(n);if(i.deleteShader(n),!i.isContextLost())throw new Error(r)}return n},e.prototype.compileProgram=function(t,e){var i=this.context.gl,n=i.createProgram();if(i.attachShader(n,t),i.attachShader(n,e),i.linkProgram(n),!i.getProgramParameter(n,i.LINK_STATUS)){var r="Couldn't compile shader program: "+i.getProgramInfoLog(n);if(i.deleteProgram(n),!i.isContextLost())throw new Error(r)}return n},e.prototype.restore=function(){this.compile()},e.prototype.bind=function(){this.context.gl.useProgram(this.program)},e.prototype.unbind=function(){this.context.gl.useProgram(null)},e.prototype.setUniformi=function(t,e){this.context.gl.uniform1i(this.getUniformLocation(t),e)},e.prototype.setUniformf=function(t,e){this.context.gl.uniform1f(this.getUniformLocation(t),e)},e.prototype.setUniform2f=function(t,e,i){this.context.gl.uniform2f(this.getUniformLocation(t),e,i)},e.prototype.setUniform3f=function(t,e,i,n){this.context.gl.uniform3f(this.getUniformLocation(t),e,i,n)},e.prototype.setUniform4f=function(t,e,i,n,r){this.context.gl.uniform4f(this.getUniformLocation(t),e,i,n,r)},e.prototype.setUniform2x2f=function(t,e){var i=this.context.gl;this.tmp2x2.set(e),i.uniformMatrix2fv(this.getUniformLocation(t),!1,this.tmp2x2)},e.prototype.setUniform3x3f=function(t,e){var i=this.context.gl;this.tmp3x3.set(e),i.uniformMatrix3fv(this.getUniformLocation(t),!1,this.tmp3x3)},e.prototype.setUniform4x4f=function(t,e){var i=this.context.gl;this.tmp4x4.set(e),i.uniformMatrix4fv(this.getUniformLocation(t),!1,this.tmp4x4)},e.prototype.getUniformLocation=function(t){var e=this.context.gl,i=e.getUniformLocation(this.program,t);if(!i&&!e.isContextLost())throw new Error("Couldn't find location for uniform ".concat(t));return i},e.prototype.getAttributeLocation=function(t){var e=this.context.gl,i=e.getAttribLocation(this.program,t);if(-1==i&&!e.isContextLost())throw new Error("Couldn't find location for attribute ".concat(t));return i},e.prototype.dispose=function(){this.context.removeRestorable(this);var t=this.context.gl;this.vs&&(t.deleteShader(this.vs),this.vs=null),this.fs&&(t.deleteShader(this.fs),this.fs=null),this.program&&(t.deleteProgram(this.program),this.program=null)},e.newColoredTextured=function(t){return new e(t,"\n\t\t\t\tattribute vec4 ".concat(e.POSITION,";\n\t\t\t\tattribute vec4 ").concat(e.COLOR,";\n\t\t\t\tattribute vec2 ").concat(e.TEXCOORDS,";\n\t\t\t\tuniform mat4 ").concat(e.MVP_MATRIX,";\n\t\t\t\tvarying vec4 v_color;\n\t\t\t\tvarying vec2 v_texCoords;\n\n\t\t\t\tvoid main () {\n\t\t\t\t\tv_color = ").concat(e.COLOR,";\n\t\t\t\t\tv_texCoords = ").concat(e.TEXCOORDS,";\n\t\t\t\t\tgl_Position = ").concat(e.MVP_MATRIX," * ").concat(e.POSITION,";\n\t\t\t\t}\n\t\t\t"),"\n\t\t\t\t#ifdef GL_ES\n\t\t\t\t\t#define LOWP lowp\n\t\t\t\t\tprecision mediump float;\n\t\t\t\t#else\n\t\t\t\t\t#define LOWP\n\t\t\t\t#endif\n\t\t\t\tvarying LOWP vec4 v_color;\n\t\t\t\tvarying vec2 v_texCoords;\n\t\t\t\tuniform sampler2D u_texture;\n\n\t\t\t\tvoid main () {\n\t\t\t\t\tgl_FragColor = v_color * texture2D(u_texture, v_texCoords);\n\t\t\t\t}\n\t\t\t")},e.newTwoColoredTextured=function(t){return new e(t,"\n\t\t\t\tattribute vec4 ".concat(e.POSITION,";\n\t\t\t\tattribute vec4 ").concat(e.COLOR,";\n\t\t\t\tattribute vec4 ").concat(e.COLOR2,";\n\t\t\t\tattribute vec2 ").concat(e.TEXCOORDS,";\n\t\t\t\tuniform mat4 ").concat(e.MVP_MATRIX,";\n\t\t\t\tvarying vec4 v_light;\n\t\t\t\tvarying vec4 v_dark;\n\t\t\t\tvarying vec2 v_texCoords;\n\n\t\t\t\tvoid main () {\n\t\t\t\t\tv_light = ").concat(e.COLOR,";\n\t\t\t\t\tv_dark = ").concat(e.COLOR2,";\n\t\t\t\t\tv_texCoords = ").concat(e.TEXCOORDS,";\n\t\t\t\t\tgl_Position = ").concat(e.MVP_MATRIX," * ").concat(e.POSITION,";\n\t\t\t\t}\n\t\t\t"),"\n\t\t\t\t#ifdef GL_ES\n\t\t\t\t\t#define LOWP lowp\n\t\t\t\t\tprecision mediump float;\n\t\t\t\t#else\n\t\t\t\t\t#define LOWP\n\t\t\t\t#endif\n\t\t\t\tvarying LOWP vec4 v_light;\n\t\t\t\tvarying LOWP vec4 v_dark;\n\t\t\t\tvarying vec2 v_texCoords;\n\t\t\t\tuniform sampler2D u_texture;\n\n\t\t\t\tvoid main () {\n\t\t\t\t\tvec4 texColor = texture2D(u_texture, v_texCoords);\n\t\t\t\t\tgl_FragColor.a = texColor.a * v_light.a;\n\t\t\t\t\tgl_FragColor.rgb = ((texColor.a - 1.0) * v_dark.a + 1.0 - texColor.rgb) * v_dark.rgb + texColor.rgb * v_light.rgb;\n\t\t\t\t}\n\t\t\t")},e.newColored=function(t){return new e(t,"\n\t\t\t\tattribute vec4 ".concat(e.POSITION,";\n\t\t\t\tattribute vec4 ").concat(e.COLOR,";\n\t\t\t\tuniform mat4 ").concat(e.MVP_MATRIX,";\n\t\t\t\tvarying vec4 v_color;\n\n\t\t\t\tvoid main () {\n\t\t\t\t\tv_color = ").concat(e.COLOR,";\n\t\t\t\t\tgl_Position = ").concat(e.MVP_MATRIX," * ").concat(e.POSITION,";\n\t\t\t\t}\n\t\t\t"),"\n\t\t\t\t#ifdef GL_ES\n\t\t\t\t\t#define LOWP lowp\n\t\t\t\t\tprecision mediump float;\n\t\t\t\t#else\n\t\t\t\t\t#define LOWP\n\t\t\t\t#endif\n\t\t\t\tvarying LOWP vec4 v_color;\n\n\t\t\t\tvoid main () {\n\t\t\t\t\tgl_FragColor = v_color;\n\t\t\t\t}\n\t\t\t")},e.MVP_MATRIX="u_projTrans",e.POSITION="a_position",e.COLOR="a_color",e.COLOR2="a_color2",e.TEXCOORDS="a_texCoords",e.SAMPLER="u_texture",e}();t.Shader=e}(t.webgl||(t.webgl={}))}(n||(n={})),function(t){!function(e){var i,n=function(){function n(n,r){if(void 0===r&&(r=10920),this.isDrawing=!1,this.shapeType=i.Filled,this.color=new t.Color(1,1,1,1),this.vertexIndex=0,this.tmp=new t.Vector2,r>10920)throw new Error("Can't have more than 10920 triangles per batch: "+r);this.context=n instanceof e.ManagedWebGLRenderingContext?n:new e.ManagedWebGLRenderingContext(n),this.mesh=new e.Mesh(n,[new e.Position2Attribute,new e.ColorAttribute],r,0),this.srcBlend=this.context.gl.SRC_ALPHA,this.dstBlend=this.context.gl.ONE_MINUS_SRC_ALPHA}return n.prototype.begin=function(t){if(this.isDrawing)throw new Error("ShapeRenderer.begin() has already been called");this.shader=t,this.vertexIndex=0,this.isDrawing=!0;var e=this.context.gl;e.enable(e.BLEND),e.blendFunc(this.srcBlend,this.dstBlend)},n.prototype.setBlendMode=function(t,e){var i=this.context.gl;this.srcBlend=t,this.dstBlend=e,this.isDrawing&&(this.flush(),i.blendFunc(this.srcBlend,this.dstBlend))},n.prototype.setColor=function(t){this.color.setFromColor(t)},n.prototype.setColorWith=function(t,e,i,n){this.color.set(t,e,i,n)},n.prototype.point=function(t,e,n){void 0===n&&(n=null),this.check(i.Point,1),null===n&&(n=this.color),this.vertex(t,e,n)},n.prototype.line=function(t,e,n,r,s){void 0===s&&(s=null),this.check(i.Line,2);this.mesh.getVertices(),this.vertexIndex;null===s&&(s=this.color),this.vertex(t,e,s),this.vertex(n,r,s)},n.prototype.triangle=function(t,e,n,r,s,a,o,h,l,u){void 0===h&&(h=null),void 0===l&&(l=null),void 0===u&&(u=null),this.check(t?i.Filled:i.Line,3);this.mesh.getVertices(),this.vertexIndex;null===h&&(h=this.color),null===l&&(l=this.color),null===u&&(u=this.color),t?(this.vertex(e,n,h),this.vertex(r,s,l),this.vertex(a,o,u)):(this.vertex(e,n,h),this.vertex(r,s,l),this.vertex(r,s,h),this.vertex(a,o,l),this.vertex(a,o,h),this.vertex(e,n,l))},n.prototype.quad=function(t,e,n,r,s,a,o,h,l,u,c,d,f){void 0===u&&(u=null),void 0===c&&(c=null),void 0===d&&(d=null),void 0===f&&(f=null),this.check(t?i.Filled:i.Line,3);this.mesh.getVertices(),this.vertexIndex;null===u&&(u=this.color),null===c&&(c=this.color),null===d&&(d=this.color),null===f&&(f=this.color),t?(this.vertex(e,n,u),this.vertex(r,s,c),this.vertex(a,o,d),this.vertex(a,o,d),this.vertex(h,l,f),this.vertex(e,n,u)):(this.vertex(e,n,u),this.vertex(r,s,c),this.vertex(r,s,c),this.vertex(a,o,d),this.vertex(a,o,d),this.vertex(h,l,f),this.vertex(h,l,f),this.vertex(e,n,u))},n.prototype.rect=function(t,e,i,n,r,s){void 0===s&&(s=null),this.quad(t,e,i,e+n,i,e+n,i+r,e,i+r,s,s,s,s)},n.prototype.rectLine=function(t,e,n,r,s,a,o){void 0===o&&(o=null),this.check(t?i.Filled:i.Line,8),null===o&&(o=this.color);var h=this.tmp.set(s-n,e-r);h.normalize(),a*=.5;var l=h.x*a,u=h.y*a;t?(this.vertex(e+l,n+u,o),this.vertex(e-l,n-u,o),this.vertex(r+l,s+u,o),this.vertex(r-l,s-u,o),this.vertex(r+l,s+u,o),this.vertex(e-l,n-u,o)):(this.vertex(e+l,n+u,o),this.vertex(e-l,n-u,o),this.vertex(r+l,s+u,o),this.vertex(r-l,s-u,o),this.vertex(r+l,s+u,o),this.vertex(e+l,n+u,o),this.vertex(r-l,s-u,o),this.vertex(e-l,n-u,o))},n.prototype.x=function(t,e,i){this.line(t-i,e-i,t+i,e+i),this.line(t-i,e+i,t+i,e-i)},n.prototype.polygon=function(t,e,n,r){if(void 0===r&&(r=null),n<3)throw new Error("Polygon must contain at least 3 vertices");this.check(i.Line,2*n),null===r&&(r=this.color);this.mesh.getVertices(),this.vertexIndex;n<<=1;for(var s=t[e<<=1],a=t[e+1],o=e+n,h=e,l=e+n-2;h<l;h+=2){var u=t[h],c=t[h+1],d=0,f=0;h+2>=o?(d=s,f=a):(d=t[h+2],f=t[h+3]),this.vertex(u,c,r),this.vertex(d,f,r)}},n.prototype.circle=function(e,n,r,s,a,o){if(void 0===a&&(a=null),void 0===o&&(o=0),0===o&&(o=Math.max(1,6*t.MathUtils.cbrt(s)|0)),o<=0)throw new Error("segments must be > 0.");null===a&&(a=this.color);var h=2*t.MathUtils.PI/o,l=Math.cos(h),u=Math.sin(h),c=s,d=0;if(e){this.check(i.Filled,3*o+3),o--;for(p=0;p<o;p++){this.vertex(n,r,a),this.vertex(n+c,r+d,a);var f=c;c=l*c-u*d,d=u*f+l*d,this.vertex(n+c,r+d,a)}this.vertex(n,r,a),this.vertex(n+c,r+d,a)}else{this.check(i.Line,2*o+2);for(var p=0;p<o;p++){this.vertex(n+c,r+d,a);var v=c;c=l*c-u*d,d=u*v+l*d,this.vertex(n+c,r+d,a)}this.vertex(n+c,r+d,a)}c=s,d=0,this.vertex(n+c,r+d,a)},n.prototype.curve=function(t,e,n,r,s,a,o,h,l,u){void 0===u&&(u=null),this.check(i.Line,2*l+2),null===u&&(u=this.color);for(var c=1/l,d=c*c,f=c*c*c,p=3*c,v=3*d,g=6*d,m=6*f,M=t-2*n+s,x=e-2*r+a,y=3*(n-s)-t+o,w=3*(r-a)-e+h,E=t,b=e,T=(n-t)*p+M*v+y*f,A=(r-e)*p+x*v+w*f,S=M*g+y*m,R=x*g+w*m,C=y*m,I=w*m;l-- >0;)this.vertex(E,b,u),E+=T,b+=A,T+=S,A+=R,S+=C,R+=I,this.vertex(E,b,u);this.vertex(E,b,u),this.vertex(o,h,u)},n.prototype.vertex=function(t,e,i){var n=this.vertexIndex,r=this.mesh.getVertices();r[n++]=t,r[n++]=e,r[n++]=i.r,r[n++]=i.g,r[n++]=i.b,r[n++]=i.a,this.vertexIndex=n},n.prototype.end=function(){if(!this.isDrawing)throw new Error("ShapeRenderer.begin() has not been called");this.flush(),this.context.gl.disable(this.context.gl.BLEND),this.isDrawing=!1},n.prototype.flush=function(){0!=this.vertexIndex&&(this.mesh.setVerticesLength(this.vertexIndex),this.mesh.draw(this.shader,this.shapeType),this.vertexIndex=0)},n.prototype.check=function(t,e){if(!this.isDrawing)throw new Error("ShapeRenderer.begin() has not been called");if(this.shapeType==t){if(!(this.mesh.maxVertices()-this.mesh.numVertices()<e))return;this.flush()}else this.flush(),this.shapeType=t},n.prototype.dispose=function(){this.mesh.dispose()},n}();e.ShapeRenderer=n,function(t){t[t.Point=0]="Point",t[t.Line=1]="Line",t[t.Filled=4]="Filled"}(i=e.ShapeType||(e.ShapeType={}))}(t.webgl||(t.webgl={}))}(n||(n={})),function(t){!function(e){var i=function(){function i(i){this.boneLineColor=new t.Color(1,0,0,1),this.boneOriginColor=new t.Color(0,1,0,1),this.attachmentLineColor=new t.Color(0,0,1,.5),this.triangleLineColor=new t.Color(1,.64,0,.5),this.pathColor=(new t.Color).setFromString("FF7F00"),this.clipColor=new t.Color(.8,0,0,2),this.aabbColor=new t.Color(0,1,0,.5),this.drawBones=!0,this.drawRegionAttachments=!0,this.drawBoundingBoxes=!0,this.drawMeshHull=!0,this.drawMeshTriangles=!0,this.drawPaths=!0,this.drawSkeletonXY=!1,this.drawClipping=!0,this.premultipliedAlpha=!1,this.scale=1,this.boneWidth=2,this.bounds=new t.SkeletonBounds,this.temp=new Array,this.vertices=t.Utils.newFloatArray(2048),this.context=i instanceof e.ManagedWebGLRenderingContext?i:new e.ManagedWebGLRenderingContext(i)}return i.prototype.draw=function(e,n,r){void 0===r&&(r=null);var s=n.x,a=n.y,o=this.context.gl,h=this.premultipliedAlpha?o.ONE:o.SRC_ALPHA;e.setBlendMode(h,o.ONE_MINUS_SRC_ALPHA);var l=n.bones;if(this.drawBones){e.setColor(this.boneLineColor);for(var u=0,c=l.length;u<c;u++){var d=l[u];if(!(r&&r.indexOf(d.data.name)>-1)&&null!=d.parent){var f=s+d.data.length*d.a+d.worldX,p=a+d.data.length*d.c+d.worldY;e.rectLine(!0,s+d.worldX,a+d.worldY,f,p,this.boneWidth*this.scale)}}this.drawSkeletonXY&&e.x(s,a,4*this.scale)}if(this.drawRegionAttachments){e.setColor(this.attachmentLineColor);for(u=0,c=(Y=n.slots).length;u<c;u++){if((W=(z=Y[u]).getAttachment())instanceof t.RegionAttachment){var v=W,g=this.vertices;v.computeWorldVertices(z.bone,g,0,2),e.line(g[0],g[1],g[2],g[3]),e.line(g[2],g[3],g[4],g[5]),e.line(g[4],g[5],g[6],g[7]),e.line(g[6],g[7],g[0],g[1])}}}if(this.drawMeshHull||this.drawMeshTriangles)for(u=0,c=(Y=n.slots).length;u<c;u++){if((z=Y[u]).bone.active)if((W=z.getAttachment())instanceof t.MeshAttachment){var m=W;g=this.vertices;m.computeWorldVertices(z,0,m.worldVerticesLength,g,0,2);var M=m.triangles,x=m.hullLength;if(this.drawMeshTriangles){e.setColor(this.triangleLineColor);for(var y=0,w=M.length;y<w;y+=3){var E=2*M[y],b=2*M[y+1],T=2*M[y+2];e.triangle(!1,g[E],g[E+1],g[b],g[b+1],g[T],g[T+1])}}if(this.drawMeshHull&&x>0){e.setColor(this.attachmentLineColor);var A=g[(x=2*(x>>1))-2],S=g[x-1];for(y=0,w=x;y<w;y+=2){f=g[y],p=g[y+1];e.line(f,p,A,S),A=f,S=p}}}}if(this.drawBoundingBoxes){var R=this.bounds;R.update(n,!0),e.setColor(this.aabbColor),e.rect(!1,R.minX,R.minY,R.getWidth(),R.getHeight());var C=R.polygons,I=R.boundingBoxes;for(u=0,c=C.length;u<c;u++){var P=C[u];e.setColor(I[u].color),e.polygon(P,0,P.length)}}if(this.drawPaths)for(u=0,c=(Y=n.slots).length;u<c;u++){if((z=Y[u]).bone.active)if((W=z.getAttachment())instanceof t.PathAttachment){var L=W,O=(w=L.worldVerticesLength,this.temp=t.Utils.setArraySize(this.temp,w,0));L.computeWorldVertices(z,0,w,O,0,2);var _=this.pathColor,k=O[2],F=O[3],D=0,V=0;if(L.closed){e.setColor(_);var N=O[0],B=O[1],X=O[w-2],U=O[w-1];D=O[w-4],V=O[w-3],e.curve(k,F,N,B,X,U,D,V,32),e.setColor(i.LIGHT_GRAY),e.line(k,F,N,B),e.line(D,V,X,U)}w-=4;for(y=4;y<w;y+=6){N=O[y],B=O[y+1],X=O[y+2],U=O[y+3];D=O[y+4],V=O[y+5],e.setColor(_),e.curve(k,F,N,B,X,U,D,V,32),e.setColor(i.LIGHT_GRAY),e.line(k,F,N,B),e.line(D,V,X,U),k=D,F=V}}}if(this.drawBones){e.setColor(this.boneOriginColor);for(u=0,c=l.length;u<c;u++){d=l[u];r&&r.indexOf(d.data.name)>-1||e.circle(!0,s+d.worldX,a+d.worldY,3*this.scale,i.GREEN,8)}}if(this.drawClipping){var Y=n.slots;e.setColor(this.clipColor);for(u=0,c=Y.length;u<c;u++){var z,W;if((z=Y[u]).bone.active)if((W=z.getAttachment())instanceof t.ClippingAttachment){var G=W;w=G.worldVerticesLength,O=this.temp=t.Utils.setArraySize(this.temp,w,0);G.computeWorldVertices(z,0,w,O,0,2);for(var H=0,q=O.length;H<q;H+=2){f=O[H],p=O[H+1],D=O[(H+2)%O.length],V=O[(H+3)%O.length];e.line(f,p,D,V)}}}}},i.prototype.dispose=function(){},i.LIGHT_GRAY=new t.Color(192/255,192/255,192/255,1),i.GREEN=new t.Color(0,1,0,1),i}();e.SkeletonDebugRenderer=i}(t.webgl||(t.webgl={}))}(n||(n={})),function(t){!function(e){var i=function(t,e,i){this.vertices=t,this.numVertices=e,this.numFloats=i},n=function(){function n(e,n){void 0===n&&(n=!0),this.premultipliedAlpha=!1,this.vertexEffect=null,this.tempColor=new t.Color,this.tempColor2=new t.Color,this.vertexSize=8,this.twoColorTint=!1,this.renderable=new i(null,0,0),this.clipper=new t.SkeletonClipping,this.temp=new t.Vector2,this.temp2=new t.Vector2,this.temp3=new t.Color,this.temp4=new t.Color,this.twoColorTint=n,n&&(this.vertexSize+=4),this.vertices=t.Utils.newFloatArray(1024*this.vertexSize)}return n.prototype.draw=function(i,r,s,a){void 0===s&&(s=-1),void 0===a&&(a=-1);var o=this.clipper,h=this.premultipliedAlpha,l=this.twoColorTint,u=null,c=this.temp,d=this.temp2,f=this.temp3,p=this.temp4,v=this.renderable,g=null,m=null,M=r.drawOrder,x=null,y=r.color,w=l?12:8,E=!1;-1==s&&(E=!0);for(var b=0,T=M.length;b<T;b++){var A=o.isClipping()?2:w,S=M[b];if(S.bone.active)if(s>=0&&s==S.data.index&&(E=!0),E){a>=0&&a==S.data.index&&(E=!1);var R=S.getAttachment(),C=null;if(R instanceof t.RegionAttachment){var I=R;v.vertices=this.vertices,v.numVertices=4,v.numFloats=A<<2,I.computeWorldVertices(S.bone,v.vertices,0,A),m=n.QUAD_TRIANGLES,g=I.uvs,C=I.region.renderObject.texture,x=I.color}else{if(!(R instanceof t.MeshAttachment)){if(R instanceof t.ClippingAttachment){var P=R;o.clipStart(S,P);continue}o.clipEndWithSlot(S);continue}var L=R;v.vertices=this.vertices,v.numVertices=L.worldVerticesLength>>1,v.numFloats=v.numVertices*A,v.numFloats>v.vertices.length&&(v.vertices=this.vertices=t.Utils.newFloatArray(v.numFloats)),L.computeWorldVertices(S,0,L.worldVerticesLength,v.vertices,0,A),m=L.triangles,C=L.region.renderObject.texture,g=L.uvs,x=L.color}if(null!=C){var O=S.color,_=this.tempColor;_.r=y.r*O.r*x.r,_.g=y.g*O.g*x.g,_.b=y.b*O.b*x.b,_.a=y.a*O.a*x.a,h&&(_.r*=_.a,_.g*=_.a,_.b*=_.a);var k=this.tempColor2;null==S.darkColor?k.set(0,0,0,1):(h?(k.r=S.darkColor.r*_.a,k.g=S.darkColor.g*_.a,k.b=S.darkColor.b*_.a):k.setFromColor(S.darkColor),k.a=h?1:0);var F=S.data.blendMode;if(F!=u&&(u=F,i.setBlendMode(e.WebGLBlendModeConverter.getSourceGLBlendMode(u,h),e.WebGLBlendModeConverter.getDestGLBlendMode(u))),o.isClipping()){o.clipTriangles(v.vertices,v.numFloats,m,m.length,g,_,k,l);var D=new Float32Array(o.clippedVertices),V=o.clippedTriangles;if(null!=this.vertexEffect){var N=this.vertexEffect,B=D;if(l){U=0;for(var X=D.length;U<X;U+=w)c.x=B[U],c.y=B[U+1],f.set(B[U+2],B[U+3],B[U+4],B[U+5]),d.x=B[U+6],d.y=B[U+7],p.set(B[U+8],B[U+9],B[U+10],B[U+11]),N.transform(c,d,f,p),B[U]=c.x,B[U+1]=c.y,B[U+2]=f.r,B[U+3]=f.g,B[U+4]=f.b,B[U+5]=f.a,B[U+6]=d.x,B[U+7]=d.y,B[U+8]=p.r,B[U+9]=p.g,B[U+10]=p.b,B[U+11]=p.a}else for(var U=0,Y=D.length;U<Y;U+=w)c.x=B[U],c.y=B[U+1],f.set(B[U+2],B[U+3],B[U+4],B[U+5]),d.x=B[U+6],d.y=B[U+7],p.set(0,0,0,0),N.transform(c,d,f,p),B[U]=c.x,B[U+1]=c.y,B[U+2]=f.r,B[U+3]=f.g,B[U+4]=f.b,B[U+5]=f.a,B[U+6]=d.x,B[U+7]=d.y}i.draw(C,D,V)}else{B=v.vertices;if(null!=this.vertexEffect){N=this.vertexEffect;if(l){U=0,W=0;for(var z=v.numFloats;U<z;U+=w,W+=2)c.x=B[U],c.y=B[U+1],d.x=g[W],d.y=g[W+1],f.setFromColor(_),p.setFromColor(k),N.transform(c,d,f,p),B[U]=c.x,B[U+1]=c.y,B[U+2]=f.r,B[U+3]=f.g,B[U+4]=f.b,B[U+5]=f.a,B[U+6]=d.x,B[U+7]=d.y,B[U+8]=p.r,B[U+9]=p.g,B[U+10]=p.b,B[U+11]=p.a}else for(var U=0,W=0,G=v.numFloats;U<G;U+=w,W+=2)c.x=B[U],c.y=B[U+1],d.x=g[W],d.y=g[W+1],f.setFromColor(_),p.set(0,0,0,0),N.transform(c,d,f,p),B[U]=c.x,B[U+1]=c.y,B[U+2]=f.r,B[U+3]=f.g,B[U+4]=f.b,B[U+5]=f.a,B[U+6]=d.x,B[U+7]=d.y}else if(l){U=2,W=0;for(var H=v.numFloats;U<H;U+=w,W+=2)B[U]=_.r,B[U+1]=_.g,B[U+2]=_.b,B[U+3]=_.a,B[U+4]=g[W],B[U+5]=g[W+1],B[U+6]=k.r,B[U+7]=k.g,B[U+8]=k.b,B[U+9]=k.a}else{U=2;for(var W=0,q=v.numFloats;U<q;U+=w,W+=2)B[U]=_.r,B[U+1]=_.g,B[U+2]=_.b,B[U+3]=_.a,B[U+4]=g[W],B[U+5]=g[W+1]}var j=v.vertices.subarray(0,v.numFloats);i.draw(C,j,m)}}o.clipEndWithSlot(S)}else o.clipEndWithSlot(S);else o.clipEndWithSlot(S)}o.clipEnd()},n.QUAD_TRIANGLES=[0,1,2,2,3,0],n}();e.SkeletonRenderer=n}(t.webgl||(t.webgl={}))}(n||(n={})),function(t){!function(t){var e=function(){function e(t,e,i){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=0),this.x=0,this.y=0,this.z=0,this.x=t,this.y=e,this.z=i}return e.prototype.setFrom=function(t){return this.x=t.x,this.y=t.y,this.z=t.z,this},e.prototype.set=function(t,e,i){return this.x=t,this.y=e,this.z=i,this},e.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this.z+=t.z,this},e.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this.z-=t.z,this},e.prototype.scale=function(t){return this.x*=t,this.y*=t,this.z*=t,this},e.prototype.normalize=function(){var t=this.length();return 0==t||(t=1/t,this.x*=t,this.y*=t,this.z*=t),this},e.prototype.cross=function(t){return this.set(this.y*t.z-this.z*t.y,this.z*t.x-this.x*t.z,this.x*t.y-this.y*t.x)},e.prototype.multiply=function(e){var i=e.values;return this.set(this.x*i[t.M00]+this.y*i[t.M01]+this.z*i[t.M02]+i[t.M03],this.x*i[t.M10]+this.y*i[t.M11]+this.z*i[t.M12]+i[t.M13],this.x*i[t.M20]+this.y*i[t.M21]+this.z*i[t.M22]+i[t.M23])},e.prototype.project=function(e){var i=e.values,n=1/(this.x*i[t.M30]+this.y*i[t.M31]+this.z*i[t.M32]+i[t.M33]);return this.set((this.x*i[t.M00]+this.y*i[t.M01]+this.z*i[t.M02]+i[t.M03])*n,(this.x*i[t.M10]+this.y*i[t.M11]+this.z*i[t.M12]+i[t.M13])*n,(this.x*i[t.M20]+this.y*i[t.M21]+this.z*i[t.M22]+i[t.M23])*n)},e.prototype.dot=function(t){return this.x*t.x+this.y*t.y+this.z*t.z},e.prototype.length=function(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)},e.prototype.distance=function(t){var e=t.x-this.x,i=t.y-this.y,n=t.z-this.z;return Math.sqrt(e*e+i*i+n*n)},e}();t.Vector3=e}(t.webgl||(t.webgl={}))}(n||(n={})),function(t){!function(e){var i=function(){function t(t,e){void 0===e&&(e={alpha:"true"}),this.restorables=new Array,t instanceof WebGLRenderingContext||t instanceof WebGL2RenderingContext?(this.gl=t,this.canvas=this.gl.canvas):this.setupCanvas(t,e)}return t.prototype.setupCanvas=function(t,e){var i=this;this.gl=t.getContext("webgl2",e)||t.getContext("webgl",e),this.canvas=t,t.addEventListener("webglcontextlost",(function(t){t&&t.preventDefault()})),t.addEventListener("webglcontextrestored",(function(t){for(var e=0,n=i.restorables.length;e<n;e++)i.restorables[e].restore()}))},t.prototype.addRestorable=function(t){this.restorables.push(t)},t.prototype.removeRestorable=function(t){var e=this.restorables.indexOf(t);e>-1&&this.restorables.splice(e,1)},t}();e.ManagedWebGLRenderingContext=i;var n=function(){function e(){}return e.getDestGLBlendMode=function(i){switch(i){case t.BlendMode.Normal:return e.ONE_MINUS_SRC_ALPHA;case t.BlendMode.Additive:return e.ONE;case t.BlendMode.Multiply:case t.BlendMode.Screen:return e.ONE_MINUS_SRC_ALPHA;default:throw new Error("Unknown blend mode: "+i)}},e.getSourceGLBlendMode=function(i,n){switch(void 0===n&&(n=!1),i){case t.BlendMode.Normal:case t.BlendMode.Additive:return n?e.ONE:e.SRC_ALPHA;case t.BlendMode.Multiply:return e.DST_COLOR;case t.BlendMode.Screen:return e.ONE;default:throw new Error("Unknown blend mode: "+i)}},e.ZERO=0,e.ONE=1,e.SRC_COLOR=768,e.ONE_MINUS_SRC_COLOR=769,e.SRC_ALPHA=770,e.ONE_MINUS_SRC_ALPHA=771,e.DST_ALPHA=772,e.ONE_MINUS_DST_ALPHA=773,e.DST_COLOR=774,e}();e.WebGLBlendModeConverter=n}(t.webgl||(t.webgl={}))}(n||(n={})),t.exports=n}).call(window)},3524:(t,e,i)=>{var n=i(7473),r=i(4597),s=i(6732),a=i(2482),o=i(704),h=i(3137),l=i(1192),u=new n({Extends:h,initialize:function(t,e,i,n,s,u,c){var d,f,p,v=[],g=t.cacheManager.custom.spine;if(a(e)){var m=e;for(e=r(m,"key"),f=new o(t,{key:e,url:r(m,"jsonURL"),extension:r(m,"jsonExtension","json"),xhrSettings:r(m,"jsonXhrSettings")}),n=r(m,"atlasURL"),s=r(m,"preMultipliedAlpha"),Array.isArray(n)||(n=[n]),d=0;d<n.length;d++)(p=new l(t,{key:e+"!"+d,url:n[d],extension:r(m,"atlasExtension","atlas"),xhrSettings:r(m,"atlasXhrSettings")})).cache=g,v.push(p)}else for(f=new o(t,e,i,u),Array.isArray(n)||(n=[n]),d=0;d<n.length;d++)(p=new l(t,e+"!"+d,n[d],c)).cache=g,v.push(p);v.unshift(f),h.call(this,t,"spine",e,v),this.config.preMultipliedAlpha=s},onFileComplete:function(t){if(-1!==this.files.indexOf(t)&&(this.pending--,"text"===t.type)){for(var e=t.data.split("\n"),i=[],n=0;n<e.length;n++){var a=e[n];""===a.trim()&&n<e.length-1&&(a=e[n+1],i.push(a))}var o=this.config,h=this.loader,l=h.baseURL,u=h.path,c=h.prefix,d=r(o,"baseURL",this.baseURL),f=r(o,"path",t.src.match(/^.*\//))[0],p=r(o,"prefix",this.prefix),v=r(o,"textureXhrSettings");h.setBaseURL(d),h.setPath(f),h.setPrefix(p);for(var g=0;g<i.length;g++){var m=i[g],M=this.key+":"+m,x=new s(h,M,m,v);h.keyExists(x)||(this.addToMultiFile(x),h.addFile(x))}h.setBaseURL(l),h.setPath(u),h.setPrefix(c)}},addToCache:function(){if(this.isReadyToProcess()){var t;this.files[0].addToCache();for(var e="",i="",n=!!this.config.preMultipliedAlpha,r=this.loader.textureManager,s=1;s<this.files.length;s++){var a=this.files[s];if("text"===a.type)e=a.key.replace(/![\d]$/,""),t=a.cache,i=i.concat(a.data);else{var o=a.key.trim(),h=o.indexOf("!"),l=o.substr(h+1);r.exists(l)||r.addImage(l,a.data)}a.pendingDestroy()}t.add(e,{preMultipliedAlpha:n,data:i,prefix:this.prefix}),this.complete=!0}}});t.exports=u},4513:(t,e,i)=>{var n=i(2494),r=i(7473),s=i(5851),a=i(3527),o=i(5722),h=i(6937),l=i(3524),u=i(8332),c=i(5782),d=i(1984),f=new r({Extends:o,initialize:function(t,e,i){o.call(this,t,e,i);var r=e.game;this.isWebGL=2===r.config.renderType,this.cache=r.cache.addCustom("spine"),this.spineTextures=r.cache.addCustom("spineTextures"),this.json=r.cache.json,this.textures=r.textures,this.drawDebug=!1,this.gl,this.renderer,this.sceneRenderer,this.skeletonRenderer,this.skeletonDebugRenderer,this.plugin=h,this.temp1,this.temp2,this.isWebGL?(this.runtime=h.webgl,this.renderer=r.renderer,this.gl=r.renderer.gl,this.getAtlas=this.getAtlasWebGL):(this.runtime=h.canvas,this.renderer=r.renderer,this.getAtlas=this.getAtlasCanvas),this.renderer||(this.renderer={width:r.scale.width,height:r.scale.height,preRender:d,postRender:d,render:d,destroy:d});var a=this.isWebGL;e.registerFileType("spine",this.spineFileCallback,t),e.registerGameObject("spine",(function(t,e,n,r,s){a&&this.scene.sys.renderer.pipelines.clear();var o=this.scene.sys[i],h=new u(this.scene,o,t,e,n,r,s);return this.displayList.add(h),this.updateList.add(h),a&&this.scene.sys.renderer.pipelines.rebind(),h}),(function(t,e){void 0===t&&(t={}),a&&this.scene.sys.renderer.pipelines.clear();var r=s(t,"key",null),o=s(t,"animationName",null),h=s(t,"loop",!1),l=this.scene.sys[i],c=new u(this.scene,l,0,0,r,o,h);void 0!==e&&(t.add=e),n(this.scene,c,t);var d=s(t,"skinName",!1);d&&c.setSkinByName(d);var f=s(t,"slotName",!1),p=s(t,"attachmentName",null);return f&&c.setAttachment(f,p),a&&this.scene.sys.renderer.pipelines.rebind(),c.refresh()})),e.registerGameObject("spineContainer",(function(t,e,n){var r=this.scene.sys[i],s=new c(this.scene,r,t,e,n);return this.displayList.add(s),s}),(function(t,e){void 0===t&&(t={});var r=s(t,"x",0),a=s(t,"y",0),o=s(t,"children",null),h=this.scene.sys[i],l=new c(this.scene,h,r,a,o);return void 0!==e&&(t.add=e),n(this.scene,l,t),l}))},boot:function(){this.isWebGL?(this.bootWebGL(),this.onResize(),this.game.scale.on(a,this.onResize,this)):this.bootCanvas();var t=this.systems.events;t.once("shutdown",this.shutdown,this),t.once("destroy",this.destroy,this),this.game.events.once("destroy",this.gameDestroy,this)},bootCanvas:function(){this.skeletonRenderer=new h.canvas.SkeletonRenderer(this.scene.sys.context)},bootWebGL:function(){var t=function(t,e){if(t!==this.srcBlend||e!==this.dstBlend){var i=this.context.gl;this.srcBlend=t,this.dstBlend=e,this.isDrawing&&(this.flush(),i.blendFunc(this.srcBlend,this.dstBlend))}},e=this.renderer.spineSceneRenderer;e||((e=new h.webgl.SceneRenderer(this.renderer.canvas,this.gl,!0)).batcher.setBlendMode=t,e.shapes.setBlendMode=t,this.renderer.spineSceneRenderer=e),this.sceneRenderer=e,this.skeletonRenderer=e.skeletonRenderer,this.skeletonDebugRenderer=e.skeletonDebugRenderer,this.temp1=new h.webgl.Vector3(0,0,0),this.temp2=new h.webgl.Vector3(0,0,0)},getAtlasCanvas:function(t){var e=this.cache.get(t);if(e){var i,n=this.spineTextures;if(n.has(t))i=n.get(t);else{var r=this.textures;i=new h.TextureAtlas(e.data,(function(i){return new h.canvas.CanvasTexture(r.get(e.prefix+t+":"+i).getSourceImage())}))}return i}console.warn("No atlas data for: "+t)},getAtlasWebGL:function(t){var e=this.cache.get(t);if(e){var i,n=this.spineTextures;if(n.has(t))i=n.get(t);else{var r=this.textures,s=this.sceneRenderer.context.gl;s.pixelStorei(s.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),i=new h.TextureAtlas(e.data,(function(i){return new h.webgl.GLTexture(s,r.get(e.prefix+t+":"+i).getSourceImage(),!1)}))}return i}console.warn("No atlas data for: "+t)},spineFileCallback:function(t,e,i,n,r,s,a){var o;if(a=a||{},Array.isArray(t))for(var h=0;h<t.length;h++)(o=new l(this,t[h])).prefix=o.prefix||a.prefix||"",this.addFile(o.files);else(o=new l(this,t,e,i,n,r,s)).prefix=o.prefix||a.prefix||"",this.addFile(o.files);return this},worldToLocal:function(t,e,i,n){var r=this.temp1,s=this.temp2,a=this.sceneRenderer.camera;r.set(t+i.x,e-i.y,0);var o=a.viewportWidth,l=a.viewportHeight;return a.screenToWorld(r,o,l),n&&null!==n.parent?(n.parent.worldToLocal(s.set(r.x-i.x,r.y-i.y,0)),new h.Vector2(s.x,s.y)):n?new h.Vector2(r.x-i.x,r.y-i.y):new h.Vector2(r.x,r.y)},getVector2:function(t,e){return new h.Vector2(t,e)},getVector3:function(t,e,i){return new h.webgl.Vector3(t,e,i)},setDebugBones:function(t){return void 0===t&&(t=!0),this.skeletonDebugRenderer.drawBones=t,this},setDebugRegionAttachments:function(t){return void 0===t&&(t=!0),this.skeletonDebugRenderer.drawRegionAttachments=t,this},setDebugBoundingBoxes:function(t){return void 0===t&&(t=!0),this.skeletonDebugRenderer.drawBoundingBoxes=t,this},setDebugMeshHull:function(t){return void 0===t&&(t=!0),this.skeletonDebugRenderer.drawMeshHull=t,this},setDebugMeshTriangles:function(t){return void 0===t&&(t=!0),this.skeletonDebugRenderer.drawMeshTriangles=t,this},setDebugPaths:function(t){return void 0===t&&(t=!0),this.skeletonDebugRenderer.drawPaths=t,this},setDebugSkeletonXY:function(t){return void 0===t&&(t=!0),this.skeletonDebugRenderer.drawSkeletonXY=t,this},setDebugClipping:function(t){return void 0===t&&(t=!0),this.skeletonDebugRenderer.drawClipping=t,this},setEffect:function(t){return this.sceneRenderer.skeletonRenderer.vertexEffect=t,this},createSkeleton:function(t,e){var i=t,n=t,r=-1!==t.indexOf(".");if(r){var a=t.split(".");i=a.shift(),n=a.join(".")}var o=this.cache.get(i),l=this.getAtlas(i);if(!l)return null;this.spineTextures.has(i)||this.spineTextures.add(i,l);var u,c=o.preMultipliedAlpha,d=new h.AtlasAttachmentLoader(l),f=new h.SkeletonJson(d);if(e)u=e;else{var p=this.json.get(i);u=r?s(p,n):p}if(u){var v=f.readSkeletonData(u);return{skeletonData:v,skeleton:new h.Skeleton(v),preMultipliedAlpha:c}}return null},createAnimationState:function(t){var e=new h.AnimationStateData(t.data);return{stateData:e,state:new h.AnimationState(e)}},getBounds:function(t){var e=new h.Vector2,i=new h.Vector2;return t.getBounds(e,i,[]),{offset:e,size:i}},onResize:function(){var t=this.renderer,e=this.sceneRenderer,i=t.width,n=t.height;e.camera.position.x=i/2,e.camera.position.y=n/2,e.camera.setViewport(i,n)},shutdown:function(){this.systems.events.off("shutdown",this.shutdown,this),this.isWebGL&&this.game.scale.off(a,this.onResize,this)},destroy:function(){this.shutdown(),this.game=null,this.scene=null,this.systems=null,this.cache=null,this.spineTextures=null,this.json=null,this.textures=null,this.skeletonRenderer=null,this.gl=null},gameDestroy:function(){this.pluginManager.removeGameObject("spine",!0,!0),this.pluginManager.removeGameObject("spineContainer",!0,!0),this.pluginManager=null;var t=this.renderer.spineSceneRenderer;t&&t.dispose(),this.renderer.spineSceneRenderer=null,this.sceneRenderer=null}});f.SpineGameObject=u,f.SpineContainer=c,t.exports=f},5782:(t,e,i)=>{var n=i(7473),r=i(7361),s=i(7738),a=new n({Extends:r,Mixins:[s],initialize:function(t,e,i,n,s){r.call(this,t,i,n,s),this.type="Spine",this.plugin=e},preDestroy:function(){this.removeAll(!!this.exclusive),this.localTransform.destroy(),this.tempTransformMatrix.destroy(),this.list=[],this._displayList=null,this.plugin=null}});t.exports=a},7738:(t,e,i)=>{var n=i(1984),r=i(1984);n=i(434),t.exports={renderWebGL:n,renderCanvas:r}},434:t=>{t.exports=function(t,e,i,n){var r=e.plugin.sceneRenderer,s=e.list;if(0!==s.length){i.addToRenderList(e);var a=e.localTransform;n?(a.loadIdentity(),a.multiply(n),a.translate(e.x,e.y),a.rotate(e.rotation),a.scale(e.scaleX,e.scaleY)):a.applyITRS(e.x,e.y,e.rotation,e.scaleX,e.scaleY),t.newType&&(t.pipelines.clear(),r.begin());var o=t.nextTypeMatch;t.nextTypeMatch=!0,t.newType=!1;for(var h=0;h<s.length;h++){var l=s[h];if(l.willRender(i,e)){var u=l.mask;u&&(r.end(),t.pipelines.rebind(),u.preRenderWebGL(t,l,i),t.pipelines.clear(),r.begin()),l.renderWebGL(t,l,i,a,e),u&&(r.end(),t.pipelines.rebind(),u.postRenderWebGL(t,i),t.pipelines.clear(),r.begin())}}t.nextTypeMatch=o,o&&!e.mask||(r.end(),t.pipelines.rebind())}else r.batcher.isDrawing&&t.finalType&&(r.end(),t.pipelines.rebind())}},6576:t=>{t.exports="complete"},8621:t=>{t.exports="dispose"},8944:t=>{t.exports="end"},7494:t=>{t.exports="event"},1908:t=>{t.exports="interrupted"},5591:t=>{t.exports="start"},5146:(t,e,i)=>{t.exports={COMPLETE:i(6576),DISPOSE:i(8621),END:i(8944),EVENT:i(7494),INTERRUPTED:i(1908),START:i(5591)}},8332:(t,e,i)=>{var n=i(6412),r=i(2915),s=i(7473),a=i(1991),o=i(3131),h=i(9660),l=i(4627),u=i(3212),c=i(8414),d=i(3426),f=i(7149),p=i(2273),v=i(4208),g=i(5146),m=i(2762),M=new s({Extends:p,Mixins:[a,o,h,l,u,c,m],initialize:function(t,e,i,n,r,s,a){p.call(this,t,"Spine"),this.plugin=e,this.skeleton=null,this.skeletonData=null,this.state=null,this.stateData=null,this.root=null,this.bounds=null,this.drawDebug=!1,this.timeScale=1,this.displayOriginX=0,this.displayOriginY=0,this.preMultipliedAlpha=!1,this.blendMode=-1,this.setPosition(i,n),r&&this.setSkeleton(r,s,a)},willRender:function(t,e){var i=!this.skeleton||!(15!==this.renderFlags||0!==this.cameraFilter&&this.cameraFilter&t.id);if(!e&&!i&&this.parentContainer){var n=this.plugin,r=n.sceneRenderer;n.gl&&r.batcher.isDrawing&&(r.end(),n.renderer.pipelines.rebind())}return i},setAlpha:function(t,e){if(void 0===t&&(t=1),e){var i=this.findSlot(e);i&&(i.color.a=r(t,0,1))}else this.alpha=t;return this},alpha:{get:function(){return this.skeleton.color.a},set:function(t){var e=r(t,0,1);this.skeleton&&(this.skeleton.color.a=e),0===e?this.renderFlags&=-3:this.renderFlags|=2}},red:{get:function(){return this.skeleton.color.r},set:function(t){var e=r(t,0,1);this.skeleton&&(this.skeleton.color.r=e)}},green:{get:function(){return this.skeleton.color.g},set:function(t){var e=r(t,0,1);this.skeleton&&(this.skeleton.color.g=e)}},blue:{get:function(){return this.skeleton.color.b},set:function(t){var e=r(t,0,1);this.skeleton&&(this.skeleton.color.b=e)}},setColor:function(t,e){void 0===t&&(t=16777215);var i=(t>>16&255)/255,n=(t>>8&255)/255,r=(255&t)/255,s=t>16777215?(t>>>24)/255:null,a=this.skeleton;if(e){var o=this.findSlot(e);o&&(a=o)}return a.color.r=i,a.color.g=n,a.color.b=r,null!==s&&(a.color.a=s),this},setSkeletonFromJSON:function(t,e,i,n){return this.setSkeleton(t,i,n,e)},setSkeleton:function(t,e,i,n){this.state&&(this.state.clearListeners(),this.state.clearListenerNotifications());var r=this.plugin.createSkeleton(t,n);this.skeletonData=r.skeletonData,this.preMultipliedAlpha=r.preMultipliedAlpha;var s=r.skeleton;return s.setSkin(),s.setToSetupPose(),this.skeleton=s,r=this.plugin.createAnimationState(s),this.state&&(this.state.clearListeners(),this.state.clearListenerNotifications()),this.state=r.state,this.stateData=r.stateData,this.state.addListener({event:this.onEvent.bind(this),complete:this.onComplete.bind(this),start:this.onStart.bind(this),end:this.onEnd.bind(this),dispose:this.onDispose.bind(this),interrupted:this.onInterrupted.bind(this)}),e&&this.setAnimation(0,e,i),this.root=this.getRootBone(),this.root&&(this.root.rotation=v(d(this.rotation))+90),this.state.apply(s),s.updateCache(),this.updateSize()},onComplete:function(t){this.emit(g.COMPLETE,t)},onDispose:function(t){this.emit(g.DISPOSE,t)},onEnd:function(t){this.emit(g.END,t)},onEvent:function(t,e){this.emit(g.EVENT,t,e)},onInterrupted:function(t){this.emit(g.INTERRUPTED,t)},onStart:function(t){this.emit(g.START,t)},refresh:function(){return this.root&&(this.root.rotation=v(d(this.rotation))+90),this.updateSize(),this.skeleton.updateCache(),this},setSize:function(t,e,i,n){var r=this.skeleton;return void 0===t&&(t=r.data.width),void 0===e&&(e=r.data.height),void 0===i&&(i=0),void 0===n&&(n=0),this.width=t,this.height=e,this.displayOriginX=r.x-i,this.displayOriginY=r.y-n,this},setOffset:function(t,e){var i=this.skeleton;return void 0===t&&(t=0),void 0===e&&(e=0),this.displayOriginX=i.x-t,this.displayOriginY=i.y-e,this},updateSize:function(){var t=this.skeleton,e=this.plugin.renderer.height,i=this.scaleX,n=this.scaleY;t.x=this.x,t.y=e-this.y,t.scaleX=1,t.scaleY=1,t.updateWorldTransform();var r=this.getBounds();return this.width=r.size.x,this.height=r.size.y,this.displayOriginX=this.x-r.offset.x,this.displayOriginY=this.y-(e-(this.height+r.offset.y)),t.scaleX=i,t.scaleY=n,t.updateWorldTransform(),this},scaleX:{get:function(){return this._scaleX},set:function(t){this._scaleX=t,this.refresh()}},scaleY:{get:function(){return this._scaleY},set:function(t){this._scaleY=t,this.refresh()}},getBoneList:function(){var t=[],e=this.skeletonData;if(e)for(var i=0;i<e.bones.length;i++)t.push(e.bones[i].name);return t},getSkinList:function(){var t=[],e=this.skeletonData;if(e)for(var i=0;i<e.skins.length;i++)t.push(e.skins[i].name);return t},getSlotList:function(){for(var t=[],e=this.skeleton,i=0;i<e.slots.length;i++)t.push(e.slots[i].data.name);return t},getAnimationList:function(){var t=[],e=this.skeletonData;if(e)for(var i=0;i<e.animations.length;i++)t.push(e.animations[i].name);return t},getCurrentAnimation:function(t){void 0===t&&(t=0);var e=this.state.getCurrent(t);if(e)return e.animation},play:function(t,e,i){return this.setAnimation(0,t,e,i),this},setAnimation:function(t,e,i,n){if(void 0===i&&(i=!1),void 0===n&&(n=!1),n&&this.state){var r=this.state.getCurrent(t);if(r&&r.animation.name===e&&!r.isComplete())return}if(this.findAnimation(e))return this.state.setAnimation(t,e,i)},addAnimation:function(t,e,i,n){return void 0===i&&(i=!1),void 0===n&&(n=0),this.state.addAnimation(t,e,i,n)},setEmptyAnimation:function(t,e){return this.state.setEmptyAnimation(t,e)},clearTrack:function(t){return this.state.clearTrack(t),this},clearTracks:function(){return this.state.clearTracks(),this},setSkinByName:function(t){var e=this.skeleton;return e.setSkinByName(t),e.setSlotsToSetupPose(),this.state.apply(e),this},setSkin:function(t){var e=this.skeleton;return e.setSkin(t),e.setSlotsToSetupPose(),this.state.apply(e),this},setMix:function(t,e,i){return this.stateData.setMix(t,e,i),this},getAttachment:function(t,e){return this.skeleton.getAttachment(t,e)},getAttachmentByName:function(t,e){return this.skeleton.getAttachmentByName(t,e)},setAttachment:function(t,e){if(Array.isArray(t)&&Array.isArray(e)&&t.length===e.length)for(var i=0;i<t.length;i++)this.skeleton.setAttachment(t[i],e[i]);else this.skeleton.setAttachment(t,e);return this},setToSetupPose:function(){return this.skeleton.setToSetupPose(),this},setSlotsToSetupPose:function(){return this.skeleton.setSlotsToSetupPose(),this},setBonesToSetupPose:function(){return this.skeleton.setBonesToSetupPose(),this},getRootBone:function(){return this.skeleton.getRootBone()},angleBoneToXY:function(t,e,i,s,a,o){void 0===s&&(s=0),void 0===a&&(a=0),void 0===o&&(o=360);var h=this.plugin.renderer.height,l=d(n(t.worldX,h-t.worldY,e,i)+f(s));return t.rotation=r(v(l),a,o),this},findBone:function(t){return this.skeleton.findBone(t)},findBoneIndex:function(t){return this.skeleton.findBoneIndex(t)},findSlot:function(t){return this.skeleton.findSlot(t)},findSlotIndex:function(t){return this.skeleton.findSlotIndex(t)},findSkin:function(t){return this.skeletonData.findSkin(t)},findEvent:function(t){return this.skeletonData.findEvent(t)},findAnimation:function(t){return this.skeletonData.findAnimation(t)},findIkConstraint:function(t){return this.skeletonData.findIkConstraint(t)},findTransformConstraint:function(t){return this.skeletonData.findTransformConstraint(t)},findPathConstraint:function(t){return this.skeletonData.findPathConstraint(t)},findPathConstraintIndex:function(t){return this.skeletonData.findPathConstraintIndex(t)},getBounds:function(){return this.plugin.getBounds(this.skeleton)},preUpdate:function(t,e){var i=this.skeleton;this.state.update(e/1e3*this.timeScale),this.state.apply(i)},preDestroy:function(){this.state&&(this.state.clearListeners(),this.state.clearListenerNotifications()),this.plugin=null,this.skeleton=null,this.skeletonData=null,this.state=null,this.stateData=null}});t.exports=M},2762:(t,e,i)=>{var n=i(1984),r=i(1984),s=i(1984);n=i(4290),s=i(780),t.exports={renderWebGL:n,renderCanvas:r,renderDirect:s}},780:(t,e,i)=>{var n=i(2915),r=i(3426),s=i(2208),a=i(4208),o=i(8445);t.exports=function(t,e,i,h,l){var u=e.plugin,c=e.skeleton,d=u.sceneRenderer;t.pipelines.clear(),d.begin();var f=e.scrollFactorX,p=e.scrollFactorY,v=c.color.a;l&&(e.scrollFactorX=l.scrollFactorX,e.scrollFactorY=l.scrollFactorY,c.color.a=n(v*l.alpha,0,1)),i.addToRenderList(e);var g=s(e,i,h).calc,m=t.height;if(c.x=g.tx,c.y=m-g.ty,c.scaleX=g.scaleX,c.scaleY=g.scaleY,e.scaleX<0?(c.scaleX*=-1,e.root.rotation=o(a(g.rotationNormalized)-180,0,360)):e.root.rotation=o(a(r(g.rotationNormalized))+90,0,360),e.scaleY<0&&(c.scaleY*=-1,e.scaleX<0?e.root.rotation-=2*a(g.rotationNormalized):e.root.rotation+=2*a(g.rotationNormalized)),c.updateWorldTransform(),d.drawSkeleton(c,e.preMultipliedAlpha),l&&(e.scrollFactorX=f,e.scrollFactorY=p,c.color.a=v),u.drawDebug||e.drawDebug){var M=c.x,x=c.y;c.x=0,c.y=0,d.drawSkeletonDebug(c,e.preMultipliedAlpha),c.x=M,c.y=x}d.end(),t.pipelines.rebind()}},4290:(t,e,i)=>{var n=i(2915),r=i(3426),s=i(2208),a=i(4208),o=i(8445);t.exports=function(t,e,i,h,l){var u=e.plugin,c=e.skeleton,d=u.sceneRenderer;t.newType&&(t.pipelines.clear(),d.begin());var f=e.scrollFactorX,p=e.scrollFactorY,v=c.color.a;l&&(e.scrollFactorX=l.scrollFactorX,e.scrollFactorY=l.scrollFactorY,c.color.a=n(v*l.alpha,0,1)),i.addToRenderList(e);var g=s(e,i,h).calc,m=t.height;if(c.x=g.tx,c.y=m-g.ty,c.scaleX=g.scaleX,c.scaleY=g.scaleY,e.scaleX<0?(c.scaleX*=-1,e.root.rotation=o(a(g.rotationNormalized)-180,0,360)):e.root.rotation=o(a(r(g.rotationNormalized))+90,0,360),e.scaleY<0&&(c.scaleY*=-1,e.scaleX<0?e.root.rotation-=2*a(g.rotationNormalized):e.root.rotation+=2*a(g.rotationNormalized)),c.updateWorldTransform(),d.drawSkeleton(c,e.preMultipliedAlpha),l&&(e.scrollFactorX=f,e.scrollFactorY=p,c.color.a=v),u.drawDebug||e.drawDebug){var M=c.x,x=c.y;c.x=0,c.y=0,d.drawSkeletonDebug(c,e.preMultipliedAlpha),c.x=M,c.y=x,d.end(),t.pipelines.rebind()}else t.nextTypeMatch||(d.end(),t.pipelines.rebind())}},9454:(t,e,i)=>{var n={VERSION:"3.70.0",BlendModes:i(8351),ScaleModes:i(8196),AUTO:0,CANVAS:1,WEBGL:2,HEADLESS:3,FOREVER:-1,NONE:4,UP:5,DOWN:6,LEFT:7,RIGHT:8};t.exports=n},1081:(t,e,i)=>{var n=i(7473),r=i(6748),s=new n({initialize:function(t,e){this.parent=t,this.events=e,e||(this.events=t.events?t.events:t),this.list={},this.values={},this._frozen=!1,!t.hasOwnProperty("sys")&&this.events&&this.events.once(r.DESTROY,this.destroy,this)},get:function(t){var e=this.list;if(Array.isArray(t)){for(var i=[],n=0;n<t.length;n++)i.push(e[t[n]]);return i}return e[t]},getAll:function(){var t={};for(var e in this.list)this.list.hasOwnProperty(e)&&(t[e]=this.list[e]);return t},query:function(t){var e={};for(var i in this.list)this.list.hasOwnProperty(i)&&i.match(t)&&(e[i]=this.list[i]);return e},set:function(t,e){if(this._frozen)return this;if("string"==typeof t)return this.setValue(t,e);for(var i in t)this.setValue(i,t[i]);return this},inc:function(t,e){if(this._frozen)return this;void 0===e&&(e=1);var i=this.get(t);return void 0===i&&(i=0),this.set(t,i+e),this},toggle:function(t){return this._frozen||this.set(t,!this.get(t)),this},setValue:function(t,e){if(this._frozen)return this;if(this.has(t))this.values[t]=e;else{var i=this,n=this.list,s=this.events,a=this.parent;Object.defineProperty(this.values,t,{enumerable:!0,configurable:!0,get:function(){return n[t]},set:function(e){if(!i._frozen){var o=n[t];n[t]=e,s.emit(r.CHANGE_DATA,a,t,e,o),s.emit(r.CHANGE_DATA_KEY+t,a,e,o)}}}),n[t]=e,s.emit(r.SET_DATA,a,t,e)}return this},each:function(t,e){for(var i=[this.parent,null,void 0],n=1;n<arguments.length;n++)i.push(arguments[n]);for(var r in this.list)i[1]=r,i[2]=this.list[r],t.apply(e,i);return this},merge:function(t,e){for(var i in void 0===e&&(e=!0),t)t.hasOwnProperty(i)&&(e||!e&&!this.has(i))&&this.setValue(i,t[i]);return this},remove:function(t){if(this._frozen)return this;if(!Array.isArray(t))return this.removeValue(t);for(var e=0;e<t.length;e++)this.removeValue(t[e]);return this},removeValue:function(t){if(this.has(t)){var e=this.list[t];delete this.list[t],delete this.values[t],this.events.emit(r.REMOVE_DATA,this.parent,t,e)}return this},pop:function(t){var e=void 0;return!this._frozen&&this.has(t)&&(e=this.list[t],delete this.list[t],delete this.values[t],this.events.emit(r.REMOVE_DATA,this.parent,t,e)),e},has:function(t){return this.list.hasOwnProperty(t)},setFreeze:function(t){return this._frozen=t,this},reset:function(){for(var t in this.list)delete this.list[t],delete this.values[t];return this._frozen=!1,this},destroy:function(){this.reset(),this.events.off(r.CHANGE_DATA),this.events.off(r.SET_DATA),this.events.off(r.REMOVE_DATA),this.parent=null},freeze:{get:function(){return this._frozen},set:function(t){this._frozen=!!t}},count:{get:function(){var t=0;for(var e in this.list)void 0!==this.list[e]&&t++;return t}}});t.exports=s},9044:t=>{t.exports="changedata"},7801:t=>{t.exports="changedata-"},4873:t=>{t.exports="destroy"},9966:t=>{t.exports="removedata"},4586:t=>{t.exports="setdata"},6748:(t,e,i)=>{t.exports={CHANGE_DATA:i(9044),CHANGE_DATA_KEY:i(7801),DESTROY:i(4873),REMOVE_DATA:i(9966),SET_DATA:i(4586)}},3004:(t,e,i)=>{var n=i(2776),r={flac:!1,aac:!1,audioData:!1,dolby:!1,m4a:!1,mp3:!1,ogg:!1,opus:!1,wav:!1,webAudio:!1,webm:!1};t.exports=function(){if("function"==typeof importScripts)return r;r.audioData=!!window.Audio,r.webAudio=!(!window.AudioContext&&!window.webkitAudioContext);var t=document.createElement("audio"),e=!!t.canPlayType;try{if(e){var i=function(e,i){var n=t.canPlayType("audio/"+e).replace(/^no$/,"");return i?Boolean(n||t.canPlayType("audio/"+i).replace(/^no$/,"")):Boolean(n)};if(r.ogg=i('ogg; codecs="vorbis"'),r.opus=i('ogg; codecs="opus"',"opus"),r.mp3=i("mpeg"),r.wav=i("wav"),r.m4a=i("x-m4a"),r.aac=i("aac"),r.flac=i("flac","x-flac"),r.webm=i('webm; codecs="vorbis"'),""!==t.canPlayType('audio/mp4; codecs="ec-3"'))if(n.edge)r.dolby=!0;else if(n.safari&&n.safariVersion>=9&&/Mac OS X (\d+)_(\d+)/.test(navigator.userAgent)){var s=parseInt(RegExp.$1,10),a=parseInt(RegExp.$2,10);(10===s&&a>=11||s>10)&&(r.dolby=!0)}}}catch(t){}return r}()},2776:(t,e,i)=>{var n,r=i(5203),s={chrome:!1,chromeVersion:0,edge:!1,firefox:!1,firefoxVersion:0,ie:!1,ieVersion:0,mobileSafari:!1,opera:!1,safari:!1,safariVersion:0,silk:!1,trident:!1,tridentVersion:0,es2019:!1};t.exports=(n=navigator.userAgent,/Edg\/\d+/.test(n)?(s.edge=!0,s.es2019=!0):/OPR/.test(n)?(s.opera=!0,s.es2019=!0):/Chrome\/(\d+)/.test(n)&&!r.windowsPhone?(s.chrome=!0,s.chromeVersion=parseInt(RegExp.$1,10),s.es2019=s.chromeVersion>69):/Firefox\D+(\d+)/.test(n)?(s.firefox=!0,s.firefoxVersion=parseInt(RegExp.$1,10),s.es2019=s.firefoxVersion>10):/AppleWebKit/.test(n)&&r.iOS?(s.mobileSafari=!0,s.es2019=!0):/MSIE (\d+\.\d+);/.test(n)?(s.ie=!0,s.ieVersion=parseInt(RegExp.$1,10)):/Version\/(\d+\.\d+) Safari/.test(n)&&!r.windowsPhone?(s.safari=!0,s.safariVersion=parseInt(RegExp.$1,10),s.es2019=s.safariVersion>10):/Trident\/(\d+\.\d+)(.*)rv:(\d+\.\d+)/.test(n)&&(s.ie=!0,s.trident=!0,s.tridentVersion=parseInt(RegExp.$1,10),s.ieVersion=parseInt(RegExp.$3,10)),/Silk/.test(n)&&(s.silk=!0),s)},6505:(t,e,i)=>{var n,r,s,a=i(8073),o={supportInverseAlpha:!1,supportNewBlendModes:!1};t.exports=("function"!=typeof importScripts&&void 0!==document&&(o.supportNewBlendModes=(n="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAABAQMAAADD8p2OAAAAA1BMVEX/",r="AAAACklEQVQI12NgAAAAAgAB4iG8MwAAAABJRU5ErkJggg==",(s=new Image).onload=function(){var t=new Image;t.onload=function(){var e=a.create2D(t,6).getContext("2d",{willReadFrequently:!0});if(e.globalCompositeOperation="multiply",e.drawImage(s,0,0),e.drawImage(t,2,0),!e.getImageData(2,0,1,1))return!1;var i=e.getImageData(2,0,1,1).data;a.remove(t),o.supportNewBlendModes=255===i[0]&&0===i[1]&&0===i[2]},t.src=n+"/wCKxvRF"+r},s.src=n+"AP804Oa6"+r,!1),o.supportInverseAlpha=function(){var t=a.create2D(this,2).getContext("2d",{willReadFrequently:!0});t.fillStyle="rgba(10, 20, 30, 0.5)",t.fillRect(0,0,1,1);var e=t.getImageData(0,0,1,1);if(null===e)return!1;t.putImageData(e,1,0);var i=t.getImageData(1,0,1,1),n=i.data[0]===e.data[0]&&i.data[1]===e.data[1]&&i.data[2]===e.data[2]&&i.data[3]===e.data[3];return a.remove(this),n}()),o)},6543:(t,e,i)=>{var n=i(5203),r=i(2776),s=i(8073),a={canvas:!1,canvasBitBltShift:null,file:!1,fileSystem:!1,getUserMedia:!0,littleEndian:!1,localStorage:!1,pointerLock:!1,stableSort:!1,support32bit:!1,vibration:!1,webGL:!1,worker:!1};t.exports=function(){if("function"==typeof importScripts)return a;a.canvas=!!window.CanvasRenderingContext2D;try{a.localStorage=!!localStorage.getItem}catch(t){a.localStorage=!1}a.file=!!(window.File&&window.FileReader&&window.FileList&&window.Blob),a.fileSystem=!!window.requestFileSystem;var t,e,i,o=!1;return a.webGL=function(){if(window.WebGLRenderingContext)try{var t=s.createWebGL(this),e=t.getContext("webgl")||t.getContext("experimental-webgl"),i=s.create2D(this),n=i.getContext("2d",{willReadFrequently:!0}).createImageData(1,1);return o=n.data instanceof Uint8ClampedArray,s.remove(t),s.remove(i),!!e}catch(t){return!1}return!1}(),a.worker=!!window.Worker,a.pointerLock="pointerLockElement"in document||"mozPointerLockElement"in document||"webkitPointerLockElement"in document,navigator.getUserMedia=navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia||navigator.oGetUserMedia,window.URL=window.URL||window.webkitURL||window.mozURL||window.msURL,a.getUserMedia=a.getUserMedia&&!!navigator.getUserMedia&&!!window.URL,r.firefox&&r.firefoxVersion<21&&(a.getUserMedia=!1),!n.iOS&&(r.ie||r.firefox||r.chrome)&&(a.canvasBitBltShift=!0),(r.safari||r.mobileSafari)&&(a.canvasBitBltShift=!1),navigator.vibrate=navigator.vibrate||navigator.webkitVibrate||navigator.mozVibrate||navigator.msVibrate,navigator.vibrate&&(a.vibration=!0),"undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint32Array&&(a.littleEndian=(t=new ArrayBuffer(4),e=new Uint8Array(t),i=new Uint32Array(t),e[0]=161,e[1]=178,e[2]=195,e[3]=212,3569595041===i[0]||2712847316!==i[0]&&null)),a.support32bit="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8ClampedArray&&"undefined"!=typeof Int32Array&&null!==a.littleEndian&&o,a}()},3922:t=>{var e={available:!1,cancel:"",keyboard:!1,request:""};t.exports=function(){if("function"==typeof importScripts)return e;var t,i="Fullscreen",n="FullScreen",r=["request"+i,"request"+n,"webkitRequest"+i,"webkitRequest"+n,"msRequest"+i,"msRequest"+n,"mozRequest"+n,"mozRequest"+i];for(t=0;t<r.length;t++)if(document.documentElement[r[t]]){e.available=!0,e.request=r[t];break}var s=["cancel"+n,"exit"+i,"webkitCancel"+n,"webkitExit"+i,"msCancel"+n,"msExit"+i,"mozCancel"+n,"mozExit"+i];if(e.available)for(t=0;t<s.length;t++)if(document[s[t]]){e.cancel=s[t];break}return window.Element&&Element.ALLOW_KEYBOARD_INPUT&&!/ Version\/5\.1(?:\.\d+)? Safari\//.test(navigator.userAgent)&&(e.keyboard=!0),Object.defineProperty(e,"active",{get:function(){return!!(document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement)}}),e}()},1454:(t,e,i)=>{var n=i(2776),r={gamepads:!1,mspointer:!1,touch:!1,wheelEvent:null};t.exports=("function"==typeof importScripts||(("ontouchstart"in document.documentElement||navigator.maxTouchPoints&&navigator.maxTouchPoints>=1)&&(r.touch=!0),(navigator.msPointerEnabled||navigator.pointerEnabled)&&(r.mspointer=!0),navigator.getGamepads&&(r.gamepads=!0),"onwheel"in window||n.ie&&"WheelEvent"in window?r.wheelEvent="wheel":"onmousewheel"in window?r.wheelEvent="mousewheel":n.firefox&&"MouseScrollEvent"in window&&(r.wheelEvent="DOMMouseScroll")),r)},5203:t=>{var e={android:!1,chromeOS:!1,cordova:!1,crosswalk:!1,desktop:!1,ejecta:!1,electron:!1,iOS:!1,iOSVersion:0,iPad:!1,iPhone:!1,kindle:!1,linux:!1,macOS:!1,node:!1,nodeWebkit:!1,pixelRatio:1,webApp:!1,windows:!1,windowsPhone:!1};t.exports=function(){if("function"==typeof importScripts)return e;var t=navigator.userAgent;/Windows/.test(t)?e.windows=!0:/Mac OS/.test(t)&&!/like Mac OS/.test(t)?navigator.maxTouchPoints&&navigator.maxTouchPoints>2?(e.iOS=!0,e.iPad=!0,navigator.appVersion.match(/Version\/(\d+)/),e.iOSVersion=parseInt(RegExp.$1,10)):e.macOS=!0:/Android/.test(t)?e.android=!0:/Linux/.test(t)?e.linux=!0:/iP[ao]d|iPhone/i.test(t)?(e.iOS=!0,navigator.appVersion.match(/OS (\d+)/),e.iOSVersion=parseInt(RegExp.$1,10),e.iPhone=-1!==t.toLowerCase().indexOf("iphone"),e.iPad=-1!==t.toLowerCase().indexOf("ipad")):/Kindle/.test(t)||/\bKF[A-Z][A-Z]+/.test(t)||/Silk.*Mobile Safari/.test(t)?e.kindle=!0:/CrOS/.test(t)&&(e.chromeOS=!0),(/Windows Phone/i.test(t)||/IEMobile/i.test(t))&&(e.android=!1,e.iOS=!1,e.macOS=!1,e.windows=!0,e.windowsPhone=!0);var i=/Silk/.test(t);return(e.windows||e.macOS||e.linux&&!i||e.chromeOS)&&(e.desktop=!0),(e.windowsPhone||/Windows NT/i.test(t)&&/Touch/i.test(t))&&(e.desktop=!1),navigator.standalone&&(e.webApp=!0),"function"!=typeof importScripts&&(void 0!==window.cordova&&(e.cordova=!0),void 0!==window.ejecta&&(e.ejecta=!0)),"undefined"!=typeof process&&process.versions&&process.versions.node&&(e.node=!0),e.node&&"object"==typeof process.versions&&(e.nodeWebkit=!!process.versions["node-webkit"],e.electron=!!process.versions.electron),/Crosswalk/.test(t)&&(e.crosswalk=!0),e.pixelRatio=window.devicePixelRatio||1,e}()},2131:(t,e,i)=>{var n=i(4597),r={h264:!1,hls:!1,mp4:!1,m4v:!1,ogg:!1,vp9:!1,webm:!1,hasRequestVideoFrame:!1};t.exports=function(){if("function"==typeof importScripts)return r;var t=document.createElement("video"),e=!!t.canPlayType,i=/^no$/;try{e&&(t.canPlayType('video/ogg; codecs="theora"').replace(i,"")&&(r.ogg=!0),t.canPlayType('video/mp4; codecs="avc1.42E01E"').replace(i,"")&&(r.h264=!0,r.mp4=!0),t.canPlayType("video/x-m4v").replace(i,"")&&(r.m4v=!0),t.canPlayType('video/webm; codecs="vp8, vorbis"').replace(i,"")&&(r.webm=!0),t.canPlayType('video/webm; codecs="vp9"').replace(i,"")&&(r.vp9=!0),t.canPlayType('application/x-mpegURL; codecs="avc1.42E01E"').replace(i,"")&&(r.hls=!0))}catch(t){}return t.parentNode&&t.parentNode.removeChild(t),r.getVideoURL=function(t){Array.isArray(t)||(t=[t]);for(var e=0;e<t.length;e++){var i,s=n(t[e],"url",t[e]);if(0===s.indexOf("blob:"))return{url:s,type:""};if(i=0===s.indexOf("data:")?s.split(",")[0].match(/\/(.*?);/):s.match(/\.([a-zA-Z0-9]+)($|\?)/),i=n(t[e],"type",i?i[1]:"").toLowerCase(),r[i])return{url:s,type:i}}return null},r}()},9356:(t,e,i)=>{t.exports={os:i(5203),browser:i(2776),features:i(6543),input:i(1454),audio:i(3004),video:i(2131),fullscreen:i(3922),canvasFeatures:i(6505)}},5686:(t,e,i)=>{var n=i(7473),r=new Float32Array(20),s=new n({initialize:function(){this._matrix=new Float32Array(20),this.alpha=1,this._dirty=!0,this._data=new Float32Array(20),this.reset()},set:function(t){return this._matrix.set(t),this._dirty=!0,this},reset:function(){var t=this._matrix;return t.fill(0),t[0]=1,t[6]=1,t[12]=1,t[18]=1,this.alpha=1,this._dirty=!0,this},getData:function(){var t=this._data;return this._dirty&&(t.set(this._matrix),t[4]/=255,t[9]/=255,t[14]/=255,t[19]/=255,this._dirty=!1),t},brightness:function(t,e){void 0===t&&(t=0),void 0===e&&(e=!1);var i=t;return this.multiply([i,0,0,0,0,0,i,0,0,0,0,0,i,0,0,0,0,0,1,0],e)},saturate:function(t,e){void 0===t&&(t=0),void 0===e&&(e=!1);var i=2*t/3+1,n=-.5*(i-1);return this.multiply([i,n,n,0,0,n,i,n,0,0,n,n,i,0,0,0,0,0,1,0],e)},desaturate:function(t){return void 0===t&&(t=!1),this.saturate(-1,t)},hue:function(t,e){void 0===t&&(t=0),void 0===e&&(e=!1),t=t/180*Math.PI;var i=Math.cos(t),n=Math.sin(t),r=.213,s=.715,a=.072;return this.multiply([r+.787*i+n*-r,s+i*-s+n*-s,a+i*-a+.928*n,0,0,r+i*-r+.143*n,s+i*(1-s)+.14*n,a+i*-a+-.283*n,0,0,r+i*-r+-.787*n,s+i*-s+n*s,a+.928*i+n*a,0,0,0,0,0,1,0],e)},grayscale:function(t,e){return void 0===t&&(t=1),void 0===e&&(e=!1),this.saturate(-t,e)},blackWhite:function(t){return void 0===t&&(t=!1),this.multiply(s.BLACK_WHITE,t)},contrast:function(t,e){void 0===t&&(t=0),void 0===e&&(e=!1);var i=t+1,n=-.5*(i-1);return this.multiply([i,0,0,0,n,0,i,0,0,n,0,0,i,0,n,0,0,0,1,0],e)},negative:function(t){return void 0===t&&(t=!1),this.multiply(s.NEGATIVE,t)},desaturateLuminance:function(t){return void 0===t&&(t=!1),this.multiply(s.DESATURATE_LUMINANCE,t)},sepia:function(t){return void 0===t&&(t=!1),this.multiply(s.SEPIA,t)},night:function(t,e){return void 0===t&&(t=.1),void 0===e&&(e=!1),this.multiply([-2*t,-t,0,0,0,-t,0,t,0,0,0,t,2*t,0,0,0,0,0,1,0],e)},lsd:function(t){return void 0===t&&(t=!1),this.multiply(s.LSD,t)},brown:function(t){return void 0===t&&(t=!1),this.multiply(s.BROWN,t)},vintagePinhole:function(t){return void 0===t&&(t=!1),this.multiply(s.VINTAGE,t)},kodachrome:function(t){return void 0===t&&(t=!1),this.multiply(s.KODACHROME,t)},technicolor:function(t){return void 0===t&&(t=!1),this.multiply(s.TECHNICOLOR,t)},polaroid:function(t){return void 0===t&&(t=!1),this.multiply(s.POLAROID,t)},shiftToBGR:function(t){return void 0===t&&(t=!1),this.multiply(s.SHIFT_BGR,t)},multiply:function(t,e){void 0===e&&(e=!1),e||this.reset();var i=this._matrix,n=r;return n.set(i),i.set([n[0]*t[0]+n[1]*t[5]+n[2]*t[10]+n[3]*t[15],n[0]*t[1]+n[1]*t[6]+n[2]*t[11]+n[3]*t[16],n[0]*t[2]+n[1]*t[7]+n[2]*t[12]+n[3]*t[17],n[0]*t[3]+n[1]*t[8]+n[2]*t[13]+n[3]*t[18],n[0]*t[4]+n[1]*t[9]+n[2]*t[14]+n[3]*t[19]+n[4],n[5]*t[0]+n[6]*t[5]+n[7]*t[10]+n[8]*t[15],n[5]*t[1]+n[6]*t[6]+n[7]*t[11]+n[8]*t[16],n[5]*t[2]+n[6]*t[7]+n[7]*t[12]+n[8]*t[17],n[5]*t[3]+n[6]*t[8]+n[7]*t[13]+n[8]*t[18],n[5]*t[4]+n[6]*t[9]+n[7]*t[14]+n[8]*t[19]+n[9],n[10]*t[0]+n[11]*t[5]+n[12]*t[10]+n[13]*t[15],n[10]*t[1]+n[11]*t[6]+n[12]*t[11]+n[13]*t[16],n[10]*t[2]+n[11]*t[7]+n[12]*t[12]+n[13]*t[17],n[10]*t[3]+n[11]*t[8]+n[12]*t[13]+n[13]*t[18],n[10]*t[4]+n[11]*t[9]+n[12]*t[14]+n[13]*t[19]+n[14],n[15]*t[0]+n[16]*t[5]+n[17]*t[10]+n[18]*t[15],n[15]*t[1]+n[16]*t[6]+n[17]*t[11]+n[18]*t[16],n[15]*t[2]+n[16]*t[7]+n[17]*t[12]+n[18]*t[17],n[15]*t[3]+n[16]*t[8]+n[17]*t[13]+n[18]*t[18],n[15]*t[4]+n[16]*t[9]+n[17]*t[14]+n[18]*t[19]+n[19]]),this._dirty=!0,this}});s.BLACK_WHITE=[.3,.6,.1,0,0,.3,.6,.1,0,0,.3,.6,.1,0,0,0,0,0,1,0],s.NEGATIVE=[-1,0,0,1,0,0,-1,0,1,0,0,0,-1,1,0,0,0,0,1,0],s.DESATURATE_LUMINANCE=[.2764723,.929708,.0938197,0,-37.1,.2764723,.929708,.0938197,0,-37.1,.2764723,.929708,.0938197,0,-37.1,0,0,0,1,0],s.SEPIA=[.393,.7689999,.18899999,0,0,.349,.6859999,.16799999,0,0,.272,.5339999,.13099999,0,0,0,0,0,1,0],s.LSD=[2,-.4,.5,0,0,-.5,2,-.4,0,0,-.4,-.5,3,0,0,0,0,0,1,0],s.BROWN=[.5997023498159715,.34553243048391263,-.2708298674538042,0,47.43192855600873,-.037703249837783157,.8609577587992641,.15059552388459913,0,-36.96841498319127,.24113635128153335,-.07441037908422492,.44972182064877153,0,-7.562075277591283,0,0,0,1,0],s.VINTAGE=[.6279345635605994,.3202183420819367,-.03965408211312453,0,9.651285835294123,.02578397704808868,.6441188644374771,.03259127616149294,0,7.462829176470591,.0466055556782719,-.0851232987247891,.5241648018700465,0,5.159190588235296,0,0,0,1,0],s.KODACHROME=[1.1285582396593525,-.3967382283601348,-.03992559172921793,0,63.72958762196502,-.16404339962244616,1.0835251566291304,-.05498805115633132,0,24.732407896706203,-.16786010706155763,-.5603416277695248,1.6014850761964943,0,35.62982807460946,0,0,0,1,0],s.TECHNICOLOR=[1.9125277891456083,-.8545344976951645,-.09155508482755585,0,11.793603434377337,-.3087833385928097,1.7658908555458428,-.10601743074722245,0,-70.35205161461398,-.231103377548616,-.7501899197440212,1.847597816108189,0,30.950940869491138,0,0,0,1,0],s.POLAROID=[1.438,-.062,-.062,0,0,-.122,1.378,-.122,0,0,-.016,-.016,1.483,0,0,0,0,0,1,0],s.SHIFT_BGR=[0,0,1,0,0,0,1,0,0,0,1,0,0,0,0,0,0,0,1,0],t.exports=s},8073:(t,e,i)=>{var n,r,s,a=i(9454),o=i(2150),h=[],l=!1;t.exports=(s=function(){var t=0;return h.forEach((function(e){e.parent&&t++})),t},{create2D:function(t,e,i){return n(t,e,i,a.CANVAS)},create:n=function(t,e,i,n,s){var u;void 0===e&&(e=1),void 0===i&&(i=1),void 0===n&&(n=a.CANVAS),void 0===s&&(s=!1);var c=r(n);return null===c?(c={parent:t,canvas:document.createElement("canvas"),type:n},n===a.CANVAS&&h.push(c),u=c.canvas):(c.parent=t,u=c.canvas),s&&(c.parent=u),u.width=e,u.height=i,l&&n===a.CANVAS&&o.disable(u.getContext("2d",{willReadFrequently:!1})),u},createWebGL:function(t,e,i){return n(t,e,i,a.WEBGL)},disableSmoothing:function(){l=!0},enableSmoothing:function(){l=!1},first:r=function(t){if(void 0===t&&(t=a.CANVAS),t===a.WEBGL)return null;for(var e=0;e<h.length;e++){var i=h[e];if(!i.parent&&i.type===t)return i}return null},free:function(){return h.length-s()},pool:h,remove:function(t){var e=t instanceof HTMLCanvasElement;h.forEach((function(i){(e&&i.canvas===t||!e&&i.parent===t)&&(i.parent=null,i.canvas.width=1,i.canvas.height=1)}))},total:s})},2150:t=>{var e,i="";t.exports={disable:function(t){return""===i&&(i=e(t)),i&&(t[i]=!1),t},enable:function(t){return""===i&&(i=e(t)),i&&(t[i]=!0),t},getPrefix:e=function(t){for(var e=["i","webkitI","msI","mozI","oI"],i=0;i<e.length;i++){var n=e[i]+"mageSmoothingEnabled";if(n in t)return n}return null},isEnabled:function(t){return null!==i?t[i]:null}}},7499:(t,e,i)=>{var n=i(7473),r=i(3649),s=new n({initialize:function(t,e,i,n,r,s){e||(e=t.sys.make.image({x:i,y:n,key:r,frame:s,add:!1})),this.bitmapMask=e,this.invertAlpha=!1,this.isStencil=!1},setBitmap:function(t){this.bitmapMask=t},preRenderWebGL:function(t,e,i){t.pipelines.BITMAPMASK_PIPELINE.beginMask(this,e,i)},postRenderWebGL:function(t,e,i){t.pipelines.BITMAPMASK_PIPELINE.endMask(this,e,i)},preRenderCanvas:function(){},postRenderCanvas:function(){},destroy:function(){this.bitmapMask=null}});r.register("bitmapMask",(function(t,e,i,n,r){return new s(this.scene,t,e,i,n,r)})),t.exports=s},6726:(t,e,i)=>{var n=new(i(7473))({initialize:function(t,e){this.geometryMask=e,this.invertAlpha=!1,this.isStencil=!0,this.level=0},setShape:function(t){return this.geometryMask=t,this},setInvertAlpha:function(t){return void 0===t&&(t=!0),this.invertAlpha=t,this},preRenderWebGL:function(t,e,i){var n=t.gl;t.flush(),0===t.maskStack.length&&(n.enable(n.STENCIL_TEST),n.clear(n.STENCIL_BUFFER_BIT),t.maskCount=0),t.currentCameraMask.mask!==this&&(t.currentMask.mask=this),t.maskStack.push({mask:this,camera:i}),this.applyStencil(t,i,!0),t.maskCount++},applyStencil:function(t,e,i){var n=t.gl,r=this.geometryMask,s=t.maskCount,a=255;n.colorMask(!1,!1,!1,!1),i?(n.stencilFunc(n.EQUAL,s,a),n.stencilOp(n.KEEP,n.KEEP,n.INCR),s++):(n.stencilFunc(n.EQUAL,s+1,a),n.stencilOp(n.KEEP,n.KEEP,n.DECR)),this.level=s,r.renderWebGL(t,r,e),t.flush(),n.colorMask(!0,!0,!0,!0),n.stencilOp(n.KEEP,n.KEEP,n.KEEP),this.invertAlpha?n.stencilFunc(n.NOTEQUAL,s,a):n.stencilFunc(n.EQUAL,s,a)},postRenderWebGL:function(t){var e=t.gl;t.maskStack.pop(),t.maskCount--,t.flush();var i=t.currentMask;if(0===t.maskStack.length)i.mask=null,e.disable(e.STENCIL_TEST);else{var n=t.maskStack[t.maskStack.length-1];n.mask.applyStencil(t,n.camera,!1),t.currentCameraMask.mask!==n.mask?(i.mask=n.mask,i.camera=n.camera):i.mask=null}},preRenderCanvas:function(t,e,i){var n=this.geometryMask;t.currentContext.save(),n.renderCanvas(t,n,i,null,null,!0),t.currentContext.clip()},postRenderCanvas:function(t){t.currentContext.restore()},destroy:function(){this.geometryMask=null}});t.exports=n},7340:(t,e,i)=>{var n=i(7473),r=i(6128),s=i(1571),a=new n({Extends:r,initialize:function(t,e){void 0===e&&(e=1),r.call(this,s.BARREL,t),this.amount=e}});t.exports=a},5170:(t,e,i)=>{var n=i(7473),r=i(6128),s=i(1571),a=new n({Extends:r,initialize:function(t,e,i,n,a,o,h){void 0===i&&(i=1),void 0===n&&(n=1),void 0===a&&(a=1),void 0===o&&(o=1),void 0===h&&(h=4),r.call(this,s.BLOOM,t),this.steps=h,this.offsetX=i,this.offsetY=n,this.blurStrength=a,this.strength=o,this.glcolor=[1,1,1],null!=e&&(this.color=e)},color:{get:function(){var t=this.glcolor;return(255*t[0]<<16)+(255*t[1]<<8)+(255*t[2]|0)},set:function(t){var e=this.glcolor;e[0]=(t>>16&255)/255,e[1]=(t>>8&255)/255,e[2]=(255&t)/255}}});t.exports=a},4199:(t,e,i)=>{var n=i(7473),r=i(6128),s=i(1571),a=new n({Extends:r,initialize:function(t,e,i,n,a,o,h){void 0===e&&(e=0),void 0===i&&(i=2),void 0===n&&(n=2),void 0===a&&(a=1),void 0===h&&(h=4),r.call(this,s.BLUR,t),this.quality=e,this.x=i,this.y=n,this.steps=h,this.strength=a,this.glcolor=[1,1,1],null!=o&&(this.color=o)},color:{get:function(){var t=this.glcolor;return(255*t[0]<<16)+(255*t[1]<<8)+(255*t[2]|0)},set:function(t){var e=this.glcolor;e[0]=(t>>16&255)/255,e[1]=(t>>8&255)/255,e[2]=(255&t)/255}}});t.exports=a},3132:(t,e,i)=>{var n=i(7473),r=i(6128),s=i(1571),a=new n({Extends:r,initialize:function(t,e,i,n,a,o,h,l){void 0===e&&(e=.5),void 0===i&&(i=1),void 0===n&&(n=.2),void 0===a&&(a=!1),void 0===o&&(o=1),void 0===h&&(h=1),void 0===l&&(l=1),r.call(this,s.BOKEH,t),this.radius=e,this.amount=i,this.contrast=n,this.isTiltShift=a,this.strength=l,this.blurX=o,this.blurY=h}});t.exports=a},6610:(t,e,i)=>{var n=i(7473),r=i(6128),s=i(1571),a=new n({Extends:r,initialize:function(t,e,i,n,a,o){void 0===e&&(e=8),void 0===a&&(a=1),void 0===o&&(o=.005),r.call(this,s.CIRCLE,t),this.scale=a,this.feather=o,this.thickness=e,this.glcolor=[1,.2,.7],this.glcolor2=[1,0,0,.4],null!=i&&(this.color=i),null!=n&&(this.backgroundColor=n)},color:{get:function(){var t=this.glcolor;return(255*t[0]<<16)+(255*t[1]<<8)+(255*t[2]|0)},set:function(t){var e=this.glcolor;e[0]=(t>>16&255)/255,e[1]=(t>>8&255)/255,e[2]=(255&t)/255}},backgroundColor:{get:function(){var t=this.glcolor2;return(255*t[0]<<16)+(255*t[1]<<8)+(255*t[2]|0)},set:function(t){var e=this.glcolor2;e[0]=(t>>16&255)/255,e[1]=(t>>8&255)/255,e[2]=(255&t)/255}},backgroundAlpha:{get:function(){return this.glcolor2[3]},set:function(t){this.glcolor2[3]=t}}});t.exports=a},4931:(t,e,i)=>{var n=i(7473),r=i(5686),s=i(1571),a=new n({Extends:r,initialize:function(t){r.call(this),this.type=s.COLOR_MATRIX,this.gameObject=t,this.active=!0},destroy:function(){this.gameObject=null,this._matrix=null,this._data=null}});t.exports=a},6128:(t,e,i)=>{var n=new(i(7473))({initialize:function(t,e){this.type=t,this.gameObject=e,this.active=!0},setActive:function(t){return this.active=t,this},destroy:function(){this.gameObject=null,this.active=!1}});t.exports=n},9195:(t,e,i)=>{var n=i(7473),r=i(6128),s=i(1571),a=new n({Extends:r,initialize:function(t,e,i,n){void 0===e&&(e="__WHITE"),void 0===i&&(i=.005),void 0===n&&(n=.005),r.call(this,s.DISPLACEMENT,t),this.x=i,this.y=n,this.glTexture,this.setTexture(e)},setTexture:function(t){var e=this.gameObject.scene.sys.textures.getFrame(t);return e&&(this.glTexture=e.glTexture),this}});t.exports=a},445:(t,e,i)=>{var n=i(7473),r=i(6128),s=i(1571),a=new n({Extends:r,initialize:function(t,e,i,n,a){void 0===i&&(i=4),void 0===n&&(n=0),void 0===a&&(a=!1),r.call(this,s.GLOW,t),this.outerStrength=i,this.innerStrength=n,this.knockout=a,this.glcolor=[1,1,1,1],void 0!==e&&(this.color=e)},color:{get:function(){var t=this.glcolor;return(255*t[0]<<16)+(255*t[1]<<8)+(255*t[2]|0)},set:function(t){var e=this.glcolor;e[0]=(t>>16&255)/255,e[1]=(t>>8&255)/255,e[2]=(255&t)/255}}});t.exports=a},7724:(t,e,i)=>{var n=i(7473),r=i(6128),s=i(1571),a=new n({Extends:r,initialize:function(t,e,i,n,a,o,h,l,u){void 0===n&&(n=.2),void 0===a&&(a=0),void 0===o&&(o=0),void 0===h&&(h=0),void 0===l&&(l=1),void 0===u&&(u=0),r.call(this,s.GRADIENT,t),this.alpha=n,this.size=u,this.fromX=a,this.fromY=o,this.toX=h,this.toY=l,this.glcolor1=[255,0,0],this.glcolor2=[0,255,0],null!=e&&(this.color1=e),null!=i&&(this.color2=i)},color1:{get:function(){var t=this.glcolor1;return(t[0]<<16)+(t[1]<<8)+(0|t[2])},set:function(t){var e=this.glcolor1;e[0]=t>>16&255,e[1]=t>>8&255,e[2]=255&t}},color2:{get:function(){var t=this.glcolor2;return(t[0]<<16)+(t[1]<<8)+(0|t[2])},set:function(t){var e=this.glcolor2;e[0]=t>>16&255,e[1]=t>>8&255,e[2]=255&t}}});t.exports=a},4412:(t,e,i)=>{var n=i(7473),r=i(6128),s=i(1571),a=new n({Extends:r,initialize:function(t,e){void 0===e&&(e=1),r.call(this,s.PIXELATE,t),this.amount=e}});t.exports=a},75:(t,e,i)=>{var n=i(7473),r=i(6128),s=i(1571),a=new n({Extends:r,initialize:function(t,e,i,n,a,o,h,l){void 0===e&&(e=0),void 0===i&&(i=0),void 0===n&&(n=.1),void 0===a&&(a=1),void 0===h&&(h=6),void 0===l&&(l=1),r.call(this,s.SHADOW,t),this.x=e,this.y=i,this.decay=n,this.power=a,this.glcolor=[0,0,0,1],this.samples=h,this.intensity=l,void 0!==o&&(this.color=o)},color:{get:function(){var t=this.glcolor;return(255*t[0]<<16)+(255*t[1]<<8)+(255*t[2]|0)},set:function(t){var e=this.glcolor;e[0]=(t>>16&255)/255,e[1]=(t>>8&255)/255,e[2]=(255&t)/255}}});t.exports=a},8734:(t,e,i)=>{var n=i(7473),r=i(6128),s=i(1571),a=new n({Extends:r,initialize:function(t,e,i,n,a){void 0===e&&(e=.5),void 0===i&&(i=.5),void 0===n&&(n=3),void 0===a&&(a=!1),r.call(this,s.SHINE,t),this.speed=e,this.lineWidth=i,this.gradient=n,this.reveal=a}});t.exports=a},2437:(t,e,i)=>{var n=i(7473),r=i(6128),s=i(1571),a=new n({Extends:r,initialize:function(t,e,i,n,a){void 0===e&&(e=.5),void 0===i&&(i=.5),void 0===n&&(n=.5),void 0===a&&(a=.5),r.call(this,s.VIGNETTE,t),this.x=e,this.y=i,this.radius=n,this.strength=a}});t.exports=a},5984:(t,e,i)=>{var n=i(7473),r=i(6128),s=i(1571),a=new n({Extends:r,initialize:function(t,e,i,n,a){void 0===e&&(e=.1),void 0===i&&(i=0),void 0===n&&(n=0),void 0===a&&(a=!1),r.call(this,s.WIPE,t),this.progress=0,this.wipeWidth=e,this.direction=i,this.axis=n,this.reveal=a}});t.exports=a},1571:t=>{t.exports={GLOW:4,SHADOW:5,PIXELATE:6,VIGNETTE:7,SHINE:8,BLUR:9,GRADIENT:12,BLOOM:13,COLOR_MATRIX:14,CIRCLE:15,BARREL:16,DISPLACEMENT:17,WIPE:18,BOKEH:19}},7347:(t,e,i)=>{var n=i(1030),r=i(1571),s={Barrel:i(7340),Controller:i(6128),Bloom:i(5170),Blur:i(4199),Bokeh:i(3132),Circle:i(6610),ColorMatrix:i(4931),Displacement:i(9195),Glow:i(445),Gradient:i(7724),Pixelate:i(4412),Shadow:i(75),Shine:i(8734),Vignette:i(2437),Wipe:i(5984)};s=n(!1,s,r),t.exports=s},2494:(t,e,i)=>{var n=i(8351),r=i(8361);t.exports=function(t,e,i){e.x=r(i,"x",0),e.y=r(i,"y",0),e.depth=r(i,"depth",0),e.flipX=r(i,"flipX",!1),e.flipY=r(i,"flipY",!1);var s=r(i,"scale",null);"number"==typeof s?e.setScale(s):null!==s&&(e.scaleX=r(s,"x",1),e.scaleY=r(s,"y",1));var a=r(i,"scrollFactor",null);"number"==typeof a?e.setScrollFactor(a):null!==a&&(e.scrollFactorX=r(a,"x",1),e.scrollFactorY=r(a,"y",1)),e.rotation=r(i,"rotation",0);var o=r(i,"angle",null);null!==o&&(e.angle=o),e.alpha=r(i,"alpha",1);var h=r(i,"origin",null);if("number"==typeof h)e.setOrigin(h);else if(null!==h){var l=r(h,"x",.5),u=r(h,"y",.5);e.setOrigin(l,u)}return e.blendMode=r(i,"blendMode",n.NORMAL),e.visible=r(i,"visible",!0),r(i,"add",!0)&&t.sys.displayList.add(e),e.preUpdate&&t.sys.updateList.add(e),e}},2273:(t,e,i)=>{var n=i(7473),r=i(6125),s=i(1081),a=i(4399),o=i(3389),h=i(204),l=new n({Extends:a,initialize:function(t,e){a.call(this),this.scene=t,this.displayList=null,this.type=e,this.state=0,this.parentContainer=null,this.name="",this.active=!0,this.tabIndex=-1,this.data=null,this.renderFlags=15,this.cameraFilter=0,this.input=null,this.body=null,this.ignoreDestroy=!1,this.on(o.ADDED_TO_SCENE,this.addedToScene,this),this.on(o.REMOVED_FROM_SCENE,this.removedFromScene,this),t.sys.queueDepthSort()},setActive:function(t){return this.active=t,this},setName:function(t){return this.name=t,this},setState:function(t){return this.state=t,this},setDataEnabled:function(){return this.data||(this.data=new s(this)),this},setData:function(t,e){return this.data||(this.data=new s(this)),this.data.set(t,e),this},incData:function(t,e){return this.data||(this.data=new s(this)),this.data.inc(t,e),this},toggleData:function(t){return this.data||(this.data=new s(this)),this.data.toggle(t),this},getData:function(t){return this.data||(this.data=new s(this)),this.data.get(t)},setInteractive:function(t,e,i){return this.scene.sys.input.enable(this,t,e,i),this},disableInteractive:function(){return this.scene.sys.input.disable(this),this},removeInteractive:function(){return this.scene.sys.input.clear(this),this.input=void 0,this},addedToScene:function(){},removedFromScene:function(){},update:function(){},toJSON:function(){return r(this)},willRender:function(t){return!(!(!this.displayList||!this.displayList.active||this.displayList.willRender(t))||l.RENDER_MASK!==this.renderFlags||0!==this.cameraFilter&&this.cameraFilter&t.id)},getIndexList:function(){for(var t=this,e=this.parentContainer,i=[];e&&(i.unshift(e.getIndex(t)),t=e,e.parentContainer);)e=e.parentContainer;return this.displayList?i.unshift(this.displayList.getIndex(t)):i.unshift(this.scene.sys.displayList.getIndex(t)),i},addToDisplayList:function(t){return void 0===t&&(t=this.scene.sys.displayList),this.displayList&&this.displayList!==t&&this.removeFromDisplayList(),t.exists(this)||(this.displayList=t,t.add(this,!0),t.queueDepthSort(),this.emit(o.ADDED_TO_SCENE,this,this.scene),t.events.emit(h.ADDED_TO_SCENE,this,this.scene)),this},addToUpdateList:function(){return this.scene&&this.preUpdate&&this.scene.sys.updateList.add(this),this},removeFromDisplayList:function(){var t=this.displayList||this.scene.sys.displayList;return t&&t.exists(this)&&(t.remove(this,!0),t.queueDepthSort(),this.displayList=null,this.emit(o.REMOVED_FROM_SCENE,this,this.scene),t.events.emit(h.REMOVED_FROM_SCENE,this,this.scene)),this},removeFromUpdateList:function(){return this.scene&&this.preUpdate&&this.scene.sys.updateList.remove(this),this},destroy:function(t){this.scene&&!this.ignoreDestroy&&(void 0===t&&(t=!1),this.preDestroy&&this.preDestroy.call(this),this.emit(o.DESTROY,this,t),this.removeAllListeners(),this.postPipelines&&this.resetPostPipeline(!0),this.removeFromDisplayList(),this.removeFromUpdateList(),this.input&&(this.scene.sys.input.clear(this),this.input=void 0),this.data&&(this.data.destroy(),this.data=void 0),this.body&&(this.body.destroy(),this.body=void 0),this.preFX&&(this.preFX.destroy(),this.preFX=void 0),this.postFX&&(this.postFX.destroy(),this.postFX=void 0),this.active=!1,this.visible=!1,this.scene=void 0,this.parentContainer=void 0)}});l.RENDER_MASK=15,t.exports=l},3649:(t,e,i)=>{var n=i(7473),r=i(8456),s=i(204),a=new n({initialize:function(t){this.scene=t,this.systems=t.sys,this.events=t.sys.events,this.displayList,this.updateList,this.events.once(s.BOOT,this.boot,this),this.events.on(s.START,this.start,this)},boot:function(){this.displayList=this.systems.displayList,this.updateList=this.systems.updateList,this.events.once(s.DESTROY,this.destroy,this)},start:function(){this.events.once(s.SHUTDOWN,this.shutdown,this)},existing:function(t){return(t.renderCanvas||t.renderWebGL)&&this.displayList.add(t),t.preUpdate&&this.updateList.add(t),t},shutdown:function(){this.events.off(s.SHUTDOWN,this.shutdown,this)},destroy:function(){this.shutdown(),this.events.off(s.START,this.start,this),this.scene=null,this.systems=null,this.events=null,this.displayList=null,this.updateList=null}});a.register=function(t,e){a.prototype.hasOwnProperty(t)||(a.prototype[t]=e)},a.remove=function(t){a.prototype.hasOwnProperty(t)&&delete a.prototype[t]},r.register("GameObjectFactory",a,"add"),t.exports=a},2208:(t,e,i)=>{var n=i(4227),r=new n,s=new n,a=new n,o={camera:r,sprite:s,calc:a};t.exports=function(t,e,i){var n=r,h=s,l=a;return h.applyITRS(t.x,t.y,t.rotation,t.scaleX,t.scaleY),n.copyFrom(e.matrix),i?(n.multiplyWithOffset(i,-e.scrollX*t.scrollFactorX,-e.scrollY*t.scrollFactorY),h.e=t.x,h.f=t.y):(h.e-=e.scrollX*t.scrollFactorX,h.f-=e.scrollY*t.scrollFactorY),n.multiply(h,l),o}},4344:(t,e,i)=>{var n=i(2915),r={_alpha:1,_alphaTL:1,_alphaTR:1,_alphaBL:1,_alphaBR:1,clearAlpha:function(){return this.setAlpha(1)},setAlpha:function(t,e,i,r){return void 0===t&&(t=1),void 0===e?this.alpha=t:(this._alphaTL=n(t,0,1),this._alphaTR=n(e,0,1),this._alphaBL=n(i,0,1),this._alphaBR=n(r,0,1)),this},alpha:{get:function(){return this._alpha},set:function(t){var e=n(t,0,1);this._alpha=e,this._alphaTL=e,this._alphaTR=e,this._alphaBL=e,this._alphaBR=e,0===e?this.renderFlags&=-3:this.renderFlags|=2}},alphaTopLeft:{get:function(){return this._alphaTL},set:function(t){var e=n(t,0,1);this._alphaTL=e,0!==e&&(this.renderFlags|=2)}},alphaTopRight:{get:function(){return this._alphaTR},set:function(t){var e=n(t,0,1);this._alphaTR=e,0!==e&&(this.renderFlags|=2)}},alphaBottomLeft:{get:function(){return this._alphaBL},set:function(t){var e=n(t,0,1);this._alphaBL=e,0!==e&&(this.renderFlags|=2)}},alphaBottomRight:{get:function(){return this._alphaBR},set:function(t){var e=n(t,0,1);this._alphaBR=e,0!==e&&(this.renderFlags|=2)}}};t.exports=r},4518:(t,e,i)=>{var n=i(2915),r={_alpha:1,clearAlpha:function(){return this.setAlpha(1)},setAlpha:function(t){return void 0===t&&(t=1),this.alpha=t,this},alpha:{get:function(){return this._alpha},set:function(t){var e=n(t,0,1);this._alpha=e,0===e?this.renderFlags&=-3:this.renderFlags|=2}}};t.exports=r},5173:(t,e,i)=>{var n=i(8351),r={_blendMode:n.NORMAL,blendMode:{get:function(){return this._blendMode},set:function(t){"string"==typeof t&&(t=n[t]),(t|=0)>=-1&&(this._blendMode=t)}},setBlendMode:function(t){return this.blendMode=t,this}};t.exports=r},1991:t=>{t.exports={width:0,height:0,displayWidth:{get:function(){return this.scaleX*this.width},set:function(t){this.scaleX=t/this.width}},displayHeight:{get:function(){return this.scaleY*this.height},set:function(t){this.scaleY=t/this.height}},setSize:function(t,e){return this.width=t,this.height=e,this},setDisplaySize:function(t,e){return this.displayWidth=t,this.displayHeight=e,this}}},8305:t=>{var e={texture:null,frame:null,isCropped:!1,setCrop:function(t,e,i,n){if(void 0===t)this.isCropped=!1;else if(this.frame){if("number"==typeof t)this.frame.setCropUVs(this._crop,t,e,i,n,this.flipX,this.flipY);else{var r=t;this.frame.setCropUVs(this._crop,r.x,r.y,r.width,r.height,this.flipX,this.flipY)}this.isCropped=!0}return this},resetCropObject:function(){return{u0:0,v0:0,u1:0,v1:0,width:0,height:0,x:0,y:0,flipX:!1,flipY:!1,cx:0,cy:0,cw:0,ch:0}}};t.exports=e},3131:t=>{var e={_depth:0,depth:{get:function(){return this._depth},set:function(t){this.displayList&&this.displayList.queueDepthSort(),this._depth=t}},setDepth:function(t){return void 0===t&&(t=0),this.depth=t,this}};t.exports=e},1626:(t,e,i)=>{var n=i(7473),r=i(7347),s=i(8935),a=new n({initialize:function(t,e){this.gameObject=t,this.isPost=e,this.enabled=!1,this.list=[],this.padding=0},setPadding:function(t){return void 0===t&&(t=0),this.padding=t,this.gameObject},onFXCopy:function(){},onFX:function(){},enable:function(t){if(!this.isPost){var e=this.gameObject.scene.sys.renderer;e&&e.pipelines?(this.gameObject.pipeline=e.pipelines.FX_PIPELINE,void 0!==t&&(this.padding=t),this.enabled=!0):this.enabled=!1}},clear:function(){if(this.isPost)this.gameObject.resetPostPipeline(!0);else{for(var t=this.list,e=0;e<t.length;e++)t[e].destroy();this.list=[]}return this.enabled=!1,this.gameObject},remove:function(t){var e;if(this.isPost){var i=this.gameObject.getPostPipeline(String(t.type));for(Array.isArray(i)||(i=[i]),e=0;e<i.length;e++){var n=i[e];if(n.controller===t){this.gameObject.removePostPipeline(n),t.destroy();break}}}else{var r=this.list;for(e=0;e<r.length;e++)r[e]===t&&(s(r,e),t.destroy())}return this.gameObject},disable:function(t){return void 0===t&&(t=!1),this.isPost||this.gameObject.resetPipeline(),this.enabled=!1,t&&this.clear(),this.gameObject},add:function(t,e){if(!this.isPost)return this.enabled||this.enable(),this.list.push(t),t;var i=String(t.type);this.gameObject.setPostPipeline(i,e);var n=this.gameObject.getPostPipeline(i);return n?(Array.isArray(n)&&(n=n.pop()),n.controller=t,t):void 0},addGlow:function(t,e,i,n,s,a){return this.add(new r.Glow(this.gameObject,t,e,i,n),{quality:s,distance:a})},addShadow:function(t,e,i,n,s,a,o){return this.add(new r.Shadow(this.gameObject,t,e,i,n,s,a,o))},addPixelate:function(t){return this.add(new r.Pixelate(this.gameObject,t))},addVignette:function(t,e,i,n){return this.add(new r.Vignette(this.gameObject,t,e,i,n))},addShine:function(t,e,i,n){return this.add(new r.Shine(this.gameObject,t,e,i,n))},addBlur:function(t,e,i,n,s,a){return this.add(new r.Blur(this.gameObject,t,e,i,n,s,a))},addGradient:function(t,e,i,n,s,a,o,h){return this.add(new r.Gradient(this.gameObject,t,e,i,n,s,a,o,h))},addBloom:function(t,e,i,n,s,a){return this.add(new r.Bloom(this.gameObject,t,e,i,n,s,a))},addColorMatrix:function(){return this.add(new r.ColorMatrix(this.gameObject))},addCircle:function(t,e,i,n,s){return this.add(new r.Circle(this.gameObject,t,e,i,n,s))},addBarrel:function(t){return this.add(new r.Barrel(this.gameObject,t))},addDisplacement:function(t,e,i){return this.add(new r.Displacement(this.gameObject,t,e,i))},addWipe:function(t,e,i){return this.add(new r.Wipe(this.gameObject,t,e,i))},addReveal:function(t,e,i){return this.add(new r.Wipe(this.gameObject,t,e,i,!0))},addBokeh:function(t,e,i){return this.add(new r.Bokeh(this.gameObject,t,e,i))},addTiltShift:function(t,e,i,n,s,a){return this.add(new r.Bokeh(this.gameObject,t,e,i,!0,n,s,a))},destroy:function(){this.clear(),this.gameObject=null}});t.exports=a},9660:t=>{t.exports={flipX:!1,flipY:!1,toggleFlipX:function(){return this.flipX=!this.flipX,this},toggleFlipY:function(){return this.flipY=!this.flipY,this},setFlipX:function(t){return this.flipX=t,this},setFlipY:function(t){return this.flipY=t,this},setFlip:function(t,e){return this.flipX=t,this.flipY=e,this},resetFlip:function(){return this.flipX=!1,this.flipY=!1,this}}},3671:(t,e,i)=>{var n=i(1392),r=i(9876),s=i(2529),a={prepareBoundsOutput:function(t,e){(void 0===e&&(e=!1),0!==this.rotation&&r(t,this.x,this.y,this.rotation),e&&this.parentContainer)&&this.parentContainer.getBoundsTransformMatrix().transformPoint(t.x,t.y,t);return t},getCenter:function(t,e){return void 0===t&&(t=new s),t.x=this.x-this.displayWidth*this.originX+this.displayWidth/2,t.y=this.y-this.displayHeight*this.originY+this.displayHeight/2,this.prepareBoundsOutput(t,e)},getTopLeft:function(t,e){return t||(t=new s),t.x=this.x-this.displayWidth*this.originX,t.y=this.y-this.displayHeight*this.originY,this.prepareBoundsOutput(t,e)},getTopCenter:function(t,e){return t||(t=new s),t.x=this.x-this.displayWidth*this.originX+this.displayWidth/2,t.y=this.y-this.displayHeight*this.originY,this.prepareBoundsOutput(t,e)},getTopRight:function(t,e){return t||(t=new s),t.x=this.x-this.displayWidth*this.originX+this.displayWidth,t.y=this.y-this.displayHeight*this.originY,this.prepareBoundsOutput(t,e)},getLeftCenter:function(t,e){return t||(t=new s),t.x=this.x-this.displayWidth*this.originX,t.y=this.y-this.displayHeight*this.originY+this.displayHeight/2,this.prepareBoundsOutput(t,e)},getRightCenter:function(t,e){return t||(t=new s),t.x=this.x-this.displayWidth*this.originX+this.displayWidth,t.y=this.y-this.displayHeight*this.originY+this.displayHeight/2,this.prepareBoundsOutput(t,e)},getBottomLeft:function(t,e){return t||(t=new s),t.x=this.x-this.displayWidth*this.originX,t.y=this.y-this.displayHeight*this.originY+this.displayHeight,this.prepareBoundsOutput(t,e)},getBottomCenter:function(t,e){return t||(t=new s),t.x=this.x-this.displayWidth*this.originX+this.displayWidth/2,t.y=this.y-this.displayHeight*this.originY+this.displayHeight,this.prepareBoundsOutput(t,e)},getBottomRight:function(t,e){return t||(t=new s),t.x=this.x-this.displayWidth*this.originX+this.displayWidth,t.y=this.y-this.displayHeight*this.originY+this.displayHeight,this.prepareBoundsOutput(t,e)},getBounds:function(t){var e,i,r,s,a,o,h,l;if(void 0===t&&(t=new n),this.parentContainer){var u=this.parentContainer.getBoundsTransformMatrix();this.getTopLeft(t),u.transformPoint(t.x,t.y,t),e=t.x,i=t.y,this.getTopRight(t),u.transformPoint(t.x,t.y,t),r=t.x,s=t.y,this.getBottomLeft(t),u.transformPoint(t.x,t.y,t),a=t.x,o=t.y,this.getBottomRight(t),u.transformPoint(t.x,t.y,t),h=t.x,l=t.y}else this.getTopLeft(t),e=t.x,i=t.y,this.getTopRight(t),r=t.x,s=t.y,this.getBottomLeft(t),a=t.x,o=t.y,this.getBottomRight(t),h=t.x,l=t.y;return t.x=Math.min(e,r,a,h),t.y=Math.min(i,s,o,l),t.width=Math.max(e,r,a,h)-t.x,t.height=Math.max(i,s,o,l)-t.y,t}};t.exports=a},2246:(t,e,i)=>{var n=i(7499),r=i(6726),s={mask:null,setMask:function(t){return this.mask=t,this},clearMask:function(t){return void 0===t&&(t=!1),t&&this.mask&&this.mask.destroy(),this.mask=null,this},createBitmapMask:function(t,e,i,r,s){return void 0===t&&(this.texture||this.shader||this.geom)&&(t=this),new n(this.scene,t,e,i,r,s)},createGeometryMask:function(t){return void 0!==t||"Graphics"!==this.type&&!this.geom||(t=this),new r(this.scene,t)}};t.exports=s},5085:t=>{var e={_originComponent:!0,originX:.5,originY:.5,_displayOriginX:0,_displayOriginY:0,displayOriginX:{get:function(){return this._displayOriginX},set:function(t){this._displayOriginX=t,this.originX=t/this.width}},displayOriginY:{get:function(){return this._displayOriginY},set:function(t){this._displayOriginY=t,this.originY=t/this.height}},setOrigin:function(t,e){return void 0===t&&(t=.5),void 0===e&&(e=t),this.originX=t,this.originY=e,this.updateDisplayOrigin()},setOriginFromFrame:function(){return this.frame&&this.frame.customPivot?(this.originX=this.frame.pivotX,this.originY=this.frame.pivotY,this.updateDisplayOrigin()):this.setOrigin()},setDisplayOrigin:function(t,e){return void 0===t&&(t=0),void 0===e&&(e=t),this.displayOriginX=t,this.displayOriginY=e,this},updateDisplayOrigin:function(){return this._displayOriginX=this.originX*this.width,this._displayOriginY=this.originY*this.height,this}};t.exports=e},77:(t,e,i)=>{var n=i(7149),r=i(1864),s=i(5851),a=i(3747),o=i(2529),h={path:null,rotateToPath:!1,pathRotationOffset:0,pathOffset:null,pathVector:null,pathDelta:null,pathTween:null,pathConfig:null,_prevDirection:a.PLAYING_FORWARD,setPath:function(t,e){void 0===e&&(e=this.pathConfig);var i=this.pathTween;return i&&i.isPlaying()&&i.stop(),this.path=t,e&&this.startFollow(e),this},setRotateToPath:function(t,e){return void 0===e&&(e=0),this.rotateToPath=t,this.pathRotationOffset=e,this},isFollowing:function(){var t=this.pathTween;return t&&t.isPlaying()},startFollow:function(t,e){void 0===t&&(t={}),void 0===e&&(e=0);var i=this.pathTween;i&&i.isPlaying()&&i.stop(),"number"==typeof t&&(t={duration:t}),t.from=s(t,"from",0),t.to=s(t,"to",1);var h=r(t,"positionOnPath",!1);this.rotateToPath=r(t,"rotateToPath",!1),this.pathRotationOffset=s(t,"rotationOffset",0);var l=s(t,"startAt",e);if(l&&(t.onStart=function(t){var e=t.data[0];e.progress=l,e.elapsed=e.duration*l;var i=e.ease(e.progress);e.current=e.start+(e.end-e.start)*i,e.setTargetValue()}),this.pathOffset||(this.pathOffset=new o(this.x,this.y)),this.pathVector||(this.pathVector=new o),this.pathDelta||(this.pathDelta=new o),this.pathDelta.reset(),t.persist=!0,this.pathTween=this.scene.sys.tweens.addCounter(t),this.path.getStartPoint(this.pathOffset),h&&(this.x=this.pathOffset.x,this.y=this.pathOffset.y),this.pathOffset.x=this.x-this.pathOffset.x,this.pathOffset.y=this.y-this.pathOffset.y,this._prevDirection=a.PLAYING_FORWARD,this.rotateToPath){var u=this.path.getPoint(.1);this.rotation=Math.atan2(u.y-this.y,u.x-this.x)+n(this.pathRotationOffset)}return this.pathConfig=t,this},pauseFollow:function(){var t=this.pathTween;return t&&t.isPlaying()&&t.pause(),this},resumeFollow:function(){var t=this.pathTween;return t&&t.isPaused()&&t.resume(),this},stopFollow:function(){var t=this.pathTween;return t&&t.isPlaying()&&t.stop(),this},pathUpdate:function(){var t=this.pathTween;if(t&&t.data){var e=t.data[0],i=this.pathDelta,r=this.pathVector;if(i.copy(r).negate(),e.state===a.COMPLETE)return this.path.getPoint(e.end,r),i.add(r),r.add(this.pathOffset),void this.setPosition(r.x,r.y);if(e.state!==a.PLAYING_FORWARD&&e.state!==a.PLAYING_BACKWARD)return;this.path.getPoint(t.getValue(),r),i.add(r),r.add(this.pathOffset);var s=this.x,o=this.y;this.setPosition(r.x,r.y);var h=this.x-s,l=this.y-o;if(0===h&&0===l)return;if(e.state!==this._prevDirection)return void(this._prevDirection=e.state);this.rotateToPath&&(this.rotation=Math.atan2(l,h)+n(this.pathRotationOffset))}}};t.exports=h},986:(t,e,i)=>{var n=i(3911),r={defaultPipeline:null,pipeline:null,pipelineData:null,initPipeline:function(t){this.pipelineData={};var e=this.scene.sys.renderer;if(!e)return!1;var i=e.pipelines;if(i){void 0===t&&(t=i.default);var n=i.get(t);if(n)return this.defaultPipeline=n,this.pipeline=n,!0}return!1},setPipeline:function(t,e,i){var r=this.scene.sys.renderer;if(!r)return this;var s=r.pipelines;if(s){var a=s.get(t);a&&(this.pipeline=a),e&&(this.pipelineData=i?n(e):e)}return this},setPipelineData:function(t,e){var i=this.pipelineData;return void 0===e?delete i[t]:i[t]=e,this},resetPipeline:function(t){return void 0===t&&(t=!1),this.pipeline=this.defaultPipeline,t&&(this.pipelineData={}),null!==this.pipeline},getPipelineName:function(){return this.pipeline.name}};t.exports=r},4461:(t,e,i)=>{var n=i(3911),r=i(1626),s=i(8935),a={hasPostPipeline:!1,postPipelines:null,postPipelineData:null,preFX:null,postFX:null,initPostPipeline:function(t){this.postPipelines=[],this.postPipelineData={},this.postFX=new r(this,!0),t&&(this.preFX=new r(this,!1))},setPostPipeline:function(t,e,i){var r=this.scene.sys.renderer;if(!r)return this;var s=r.pipelines;if(s){Array.isArray(t)||(t=[t]);for(var a=0;a<t.length;a++){var o=s.getPostPipeline(t[a],this,e);o&&this.postPipelines.push(o)}e&&(this.postPipelineData=i?n(e):e)}return this.hasPostPipeline=this.postPipelines.length>0,this},setPostPipelineData:function(t,e){var i=this.postPipelineData;return void 0===e?delete i[t]:i[t]=e,this},getPostPipeline:function(t){for(var e="string"==typeof t,i=this.postPipelines,n=[],r=0;r<i.length;r++){var s=i[r];(e&&s.name===t||!e&&s instanceof t)&&n.push(s)}return 1===n.length?n[0]:n},resetPostPipeline:function(t){void 0===t&&(t=!1);for(var e=this.postPipelines,i=0;i<e.length;i++)e[i].destroy();this.postPipelines=[],this.hasPostPipeline=!1,t&&(this.postPipelineData={})},removePostPipeline:function(t){for(var e="string"==typeof t,i=this.postPipelines,n=i.length-1;n>=0;n--){var r=i[n];(e&&r.name===t||!e&&r===t)&&(r.destroy(),s(i,n))}return this.hasPostPipeline=this.postPipelines.length>0,this},clearFX:function(){return this.preFX&&this.preFX.clear(),this.postFX&&this.postFX.clear(),this}};t.exports=a},4627:t=>{var e={scrollFactorX:1,scrollFactorY:1,setScrollFactor:function(t,e){return void 0===e&&(e=t),this.scrollFactorX=t,this.scrollFactorY=e,this}};t.exports=e},1868:t=>{var e={_sizeComponent:!0,width:0,height:0,displayWidth:{get:function(){return Math.abs(this.scaleX*this.frame.realWidth)},set:function(t){this.scaleX=t/this.frame.realWidth}},displayHeight:{get:function(){return Math.abs(this.scaleY*this.frame.realHeight)},set:function(t){this.scaleY=t/this.frame.realHeight}},setSizeToFrame:function(t){t||(t=this.frame),this.width=t.realWidth,this.height=t.realHeight;var e=this.input;return e&&!e.customHitArea&&(e.hitArea.width=this.width,e.hitArea.height=this.height),this},setSize:function(t,e){return this.width=t,this.height=e,this},setDisplaySize:function(t,e){return this.displayWidth=t,this.displayHeight=e,this}};t.exports=e},4976:(t,e,i)=>{var n=i(2362),r={texture:null,frame:null,isCropped:!1,setTexture:function(t,e,i,n){return this.texture=this.scene.sys.textures.get(t),this.setFrame(e,i,n)},setFrame:function(t,e,i){return void 0===e&&(e=!0),void 0===i&&(i=!0),t instanceof n?(this.texture=this.scene.sys.textures.get(t.texture.key),this.frame=t):this.frame=this.texture.get(t),this.frame.cutWidth&&this.frame.cutHeight?this.renderFlags|=8:this.renderFlags&=-9,this._sizeComponent&&e&&this.setSizeToFrame(),this._originComponent&&i&&(this.frame.customPivot?this.setOrigin(this.frame.pivotX,this.frame.pivotY):this.updateDisplayOrigin()),this}};t.exports=r},9243:(t,e,i)=>{var n=i(2362),r={texture:null,frame:null,isCropped:!1,setCrop:function(t,e,i,n){if(void 0===t)this.isCropped=!1;else if(this.frame){if("number"==typeof t)this.frame.setCropUVs(this._crop,t,e,i,n,this.flipX,this.flipY);else{var r=t;this.frame.setCropUVs(this._crop,r.x,r.y,r.width,r.height,this.flipX,this.flipY)}this.isCropped=!0}return this},setTexture:function(t,e){return this.texture=this.scene.sys.textures.get(t),this.setFrame(e)},setFrame:function(t,e,i){return void 0===e&&(e=!0),void 0===i&&(i=!0),t instanceof n?(this.texture=this.scene.sys.textures.get(t.texture.key),this.frame=t):this.frame=this.texture.get(t),this.frame.cutWidth&&this.frame.cutHeight?this.renderFlags|=8:this.renderFlags&=-9,this._sizeComponent&&e&&this.setSizeToFrame(),this._originComponent&&i&&(this.frame.customPivot?this.setOrigin(this.frame.pivotX,this.frame.pivotY):this.updateDisplayOrigin()),this.isCropped&&this.frame.updateCropUVs(this._crop,this.flipX,this.flipY),this},resetCropObject:function(){return{u0:0,v0:0,u1:0,v1:0,width:0,height:0,x:0,y:0,flipX:!1,flipY:!1,cx:0,cy:0,cw:0,ch:0}}};t.exports=r},5693:t=>{var e={tintTopLeft:16777215,tintTopRight:16777215,tintBottomLeft:16777215,tintBottomRight:16777215,tintFill:!1,clearTint:function(){return this.setTint(16777215),this},setTint:function(t,e,i,n){return void 0===t&&(t=16777215),void 0===e&&(e=t,i=t,n=t),this.tintTopLeft=t,this.tintTopRight=e,this.tintBottomLeft=i,this.tintBottomRight=n,this.tintFill=!1,this},setTintFill:function(t,e,i,n){return this.setTint(t,e,i,n),this.tintFill=!0,this},tint:{get:function(){return this.tintTopLeft},set:function(t){this.setTint(t,t,t,t)}},isTinted:{get:function(){var t=16777215;return this.tintFill||this.tintTopLeft!==t||this.tintTopRight!==t||this.tintBottomLeft!==t||this.tintBottomRight!==t}}};t.exports=e},6125:t=>{t.exports=function(t){var e={name:t.name,type:t.type,x:t.x,y:t.y,depth:t.depth,scale:{x:t.scaleX,y:t.scaleY},origin:{x:t.originX,y:t.originY},flipX:t.flipX,flipY:t.flipY,rotation:t.rotation,alpha:t.alpha,visible:t.visible,blendMode:t.blendMode,textureKey:"",frameKey:"",data:{}};return t.texture&&(e.textureKey=t.texture.key,e.frameKey=t.frame.name),e}},3212:(t,e,i)=>{var n=i(7425),r=i(4227),s=i(7556),a=i(3692),o=i(2820),h=i(2529),l={hasTransformComponent:!0,_scaleX:1,_scaleY:1,_rotation:0,x:0,y:0,z:0,w:0,scale:{get:function(){return(this._scaleX+this._scaleY)/2},set:function(t){this._scaleX=t,this._scaleY=t,0===t?this.renderFlags&=-5:this.renderFlags|=4}},scaleX:{get:function(){return this._scaleX},set:function(t){this._scaleX=t,0===t?this.renderFlags&=-5:0!==this._scaleY&&(this.renderFlags|=4)}},scaleY:{get:function(){return this._scaleY},set:function(t){this._scaleY=t,0===t?this.renderFlags&=-5:0!==this._scaleX&&(this.renderFlags|=4)}},angle:{get:function(){return o(this._rotation*n.RAD_TO_DEG)},set:function(t){this.rotation=o(t)*n.DEG_TO_RAD}},rotation:{get:function(){return this._rotation},set:function(t){this._rotation=a(t)}},setPosition:function(t,e,i,n){return void 0===t&&(t=0),void 0===e&&(e=t),void 0===i&&(i=0),void 0===n&&(n=0),this.x=t,this.y=e,this.z=i,this.w=n,this},copyPosition:function(t){return void 0!==t.x&&(this.x=t.x),void 0!==t.y&&(this.y=t.y),void 0!==t.z&&(this.z=t.z),void 0!==t.w&&(this.w=t.w),this},setRandomPosition:function(t,e,i,n){return void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=this.scene.sys.scale.width),void 0===n&&(n=this.scene.sys.scale.height),this.x=t+Math.random()*i,this.y=e+Math.random()*n,this},setRotation:function(t){return void 0===t&&(t=0),this.rotation=t,this},setAngle:function(t){return void 0===t&&(t=0),this.angle=t,this},setScale:function(t,e){return void 0===t&&(t=1),void 0===e&&(e=t),this.scaleX=t,this.scaleY=e,this},setX:function(t){return void 0===t&&(t=0),this.x=t,this},setY:function(t){return void 0===t&&(t=0),this.y=t,this},setZ:function(t){return void 0===t&&(t=0),this.z=t,this},setW:function(t){return void 0===t&&(t=0),this.w=t,this},getLocalTransformMatrix:function(t){return void 0===t&&(t=new r),t.applyITRS(this.x,this.y,this._rotation,this._scaleX,this._scaleY)},getWorldTransformMatrix:function(t,e){void 0===t&&(t=new r);var i=this.parentContainer;if(!i)return this.getLocalTransformMatrix(t);for(e||(e=new r),t.applyITRS(this.x,this.y,this._rotation,this._scaleX,this._scaleY);i;)e.applyITRS(i.x,i.y,i._rotation,i._scaleX,i._scaleY),e.multiply(t,t),i=i.parentContainer;return t},getLocalPoint:function(t,e,i,n){i||(i=new h),n||(n=this.scene.sys.cameras.main);var r=n.scrollX,a=n.scrollY,o=t+r*this.scrollFactorX-r,l=e+a*this.scrollFactorY-a;return this.parentContainer?this.getWorldTransformMatrix().applyInverse(o,l,i):s(o,l,this.x,this.y,this.rotation,this.scaleX,this.scaleY,i),this._originComponent&&(i.x+=this._displayOriginX,i.y+=this._displayOriginY),i},getParentRotation:function(){for(var t=0,e=this.parentContainer;e;)t+=e.rotation,e=e.parentContainer;return t}};t.exports=l},4227:(t,e,i)=>{var n=i(7473),r=i(7425),s=i(2529),a=new n({initialize:function(t,e,i,n,r,s){void 0===t&&(t=1),void 0===e&&(e=0),void 0===i&&(i=0),void 0===n&&(n=1),void 0===r&&(r=0),void 0===s&&(s=0),this.matrix=new Float32Array([t,e,i,n,r,s,0,0,1]),this.decomposedMatrix={translateX:0,translateY:0,scaleX:1,scaleY:1,rotation:0},this.quad=new Float32Array(8)},a:{get:function(){return this.matrix[0]},set:function(t){this.matrix[0]=t}},b:{get:function(){return this.matrix[1]},set:function(t){this.matrix[1]=t}},c:{get:function(){return this.matrix[2]},set:function(t){this.matrix[2]=t}},d:{get:function(){return this.matrix[3]},set:function(t){this.matrix[3]=t}},e:{get:function(){return this.matrix[4]},set:function(t){this.matrix[4]=t}},f:{get:function(){return this.matrix[5]},set:function(t){this.matrix[5]=t}},tx:{get:function(){return this.matrix[4]},set:function(t){this.matrix[4]=t}},ty:{get:function(){return this.matrix[5]},set:function(t){this.matrix[5]=t}},rotation:{get:function(){return Math.acos(this.a/this.scaleX)*(Math.atan(-this.c/this.a)<0?-1:1)}},rotationNormalized:{get:function(){var t=this.matrix,e=t[0],i=t[1],n=t[2],s=t[3];return e||i?i>0?Math.acos(e/this.scaleX):-Math.acos(e/this.scaleX):n||s?r.TAU-(s>0?Math.acos(-n/this.scaleY):-Math.acos(n/this.scaleY)):0}},scaleX:{get:function(){return Math.sqrt(this.a*this.a+this.b*this.b)}},scaleY:{get:function(){return Math.sqrt(this.c*this.c+this.d*this.d)}},loadIdentity:function(){var t=this.matrix;return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,this},translate:function(t,e){var i=this.matrix;return i[4]=i[0]*t+i[2]*e+i[4],i[5]=i[1]*t+i[3]*e+i[5],this},scale:function(t,e){var i=this.matrix;return i[0]*=t,i[1]*=t,i[2]*=e,i[3]*=e,this},rotate:function(t){var e=Math.sin(t),i=Math.cos(t),n=this.matrix,r=n[0],s=n[1],a=n[2],o=n[3];return n[0]=r*i+a*e,n[1]=s*i+o*e,n[2]=r*-e+a*i,n[3]=s*-e+o*i,this},multiply:function(t,e){var i=this.matrix,n=t.matrix,r=i[0],s=i[1],a=i[2],o=i[3],h=i[4],l=i[5],u=n[0],c=n[1],d=n[2],f=n[3],p=n[4],v=n[5],g=void 0===e?i:e.matrix;return g[0]=u*r+c*a,g[1]=u*s+c*o,g[2]=d*r+f*a,g[3]=d*s+f*o,g[4]=p*r+v*a+h,g[5]=p*s+v*o+l,g},multiplyWithOffset:function(t,e,i){var n=this.matrix,r=t.matrix,s=n[0],a=n[1],o=n[2],h=n[3],l=e*s+i*o+n[4],u=e*a+i*h+n[5],c=r[0],d=r[1],f=r[2],p=r[3],v=r[4],g=r[5];return n[0]=c*s+d*o,n[1]=c*a+d*h,n[2]=f*s+p*o,n[3]=f*a+p*h,n[4]=v*s+g*o+l,n[5]=v*a+g*h+u,this},transform:function(t,e,i,n,r,s){var a=this.matrix,o=a[0],h=a[1],l=a[2],u=a[3],c=a[4],d=a[5];return a[0]=t*o+e*l,a[1]=t*h+e*u,a[2]=i*o+n*l,a[3]=i*h+n*u,a[4]=r*o+s*l+c,a[5]=r*h+s*u+d,this},transformPoint:function(t,e,i){void 0===i&&(i={x:0,y:0});var n=this.matrix,r=n[0],s=n[1],a=n[2],o=n[3],h=n[4],l=n[5];return i.x=t*r+e*a+h,i.y=t*s+e*o+l,i},invert:function(){var t=this.matrix,e=t[0],i=t[1],n=t[2],r=t[3],s=t[4],a=t[5],o=e*r-i*n;return t[0]=r/o,t[1]=-i/o,t[2]=-n/o,t[3]=e/o,t[4]=(n*a-r*s)/o,t[5]=-(e*a-i*s)/o,this},copyFrom:function(t){var e=this.matrix;return e[0]=t.a,e[1]=t.b,e[2]=t.c,e[3]=t.d,e[4]=t.e,e[5]=t.f,this},copyFromArray:function(t){var e=this.matrix;return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],this},copyToContext:function(t){var e=this.matrix;return t.transform(e[0],e[1],e[2],e[3],e[4],e[5]),t},setToContext:function(t){return t.setTransform(this),t},copyToArray:function(t){var e=this.matrix;return void 0===t?t=[e[0],e[1],e[2],e[3],e[4],e[5]]:(t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5]),t},setTransform:function(t,e,i,n,r,s){var a=this.matrix;return a[0]=t,a[1]=e,a[2]=i,a[3]=n,a[4]=r,a[5]=s,this},decomposeMatrix:function(){var t=this.decomposedMatrix,e=this.matrix,i=e[0],n=e[1],r=e[2],s=e[3],a=i*s-n*r;if(t.translateX=e[4],t.translateY=e[5],i||n){var o=Math.sqrt(i*i+n*n);t.rotation=n>0?Math.acos(i/o):-Math.acos(i/o),t.scaleX=o,t.scaleY=a/o}else if(r||s){var h=Math.sqrt(r*r+s*s);t.rotation=.5*Math.PI-(s>0?Math.acos(-r/h):-Math.acos(r/h)),t.scaleX=a/h,t.scaleY=h}else t.rotation=0,t.scaleX=0,t.scaleY=0;return t},applyITRS:function(t,e,i,n,r){var s=this.matrix,a=Math.sin(i),o=Math.cos(i);return s[4]=t,s[5]=e,s[0]=o*n,s[1]=a*n,s[2]=-a*r,s[3]=o*r,this},applyInverse:function(t,e,i){void 0===i&&(i=new s);var n=this.matrix,r=n[0],a=n[1],o=n[2],h=n[3],l=n[4],u=n[5],c=1/(r*h+o*-a);return i.x=h*c*t+-o*c*e+(u*o-l*h)*c,i.y=r*c*e+-a*c*t+(-u*r+l*a)*c,i},setQuad:function(t,e,i,n,r,s){void 0===r&&(r=!1),void 0===s&&(s=this.quad);var a=this.matrix,o=a[0],h=a[1],l=a[2],u=a[3],c=a[4],d=a[5];return r?(s[0]=Math.round(t*o+e*l+c),s[1]=Math.round(t*h+e*u+d),s[2]=Math.round(t*o+n*l+c),s[3]=Math.round(t*h+n*u+d),s[4]=Math.round(i*o+n*l+c),s[5]=Math.round(i*h+n*u+d),s[6]=Math.round(i*o+e*l+c),s[7]=Math.round(i*h+e*u+d)):(s[0]=t*o+e*l+c,s[1]=t*h+e*u+d,s[2]=t*o+n*l+c,s[3]=t*h+n*u+d,s[4]=i*o+n*l+c,s[5]=i*h+n*u+d,s[6]=i*o+e*l+c,s[7]=i*h+e*u+d),s},getX:function(t,e){return t*this.a+e*this.c+this.e},getY:function(t,e){return t*this.b+e*this.d+this.f},getXRound:function(t,e,i){var n=this.getX(t,e);return i&&(n=Math.round(n)),n},getYRound:function(t,e,i){var n=this.getY(t,e);return i&&(n=Math.round(n)),n},getCSSMatrix:function(){var t=this.matrix;return"matrix("+t[0]+","+t[1]+","+t[2]+","+t[3]+","+t[4]+","+t[5]+")"},destroy:function(){this.matrix=null,this.quad=null,this.decomposedMatrix=null}});t.exports=a},8414:t=>{var e={_visible:!0,visible:{get:function(){return this._visible},set:function(t){t?(this._visible=!0,this.renderFlags|=1):(this._visible=!1,this.renderFlags&=-2)}},setVisible:function(t){return this.visible=t,this}};t.exports=e},4286:(t,e,i)=>{t.exports={Alpha:i(4344),AlphaSingle:i(4518),BlendMode:i(5173),ComputedSize:i(1991),Crop:i(8305),Depth:i(3131),Flip:i(9660),FX:i(1626),GetBounds:i(3671),Mask:i(2246),Origin:i(5085),PathFollower:i(77),Pipeline:i(986),PostPipeline:i(4461),ScrollFactor:i(4627),Size:i(1868),Texture:i(4976),TextureCrop:i(9243),Tint:i(5693),ToJSON:i(6125),Transform:i(3212),TransformMatrix:i(4227),Visible:i(8414)}},7361:(t,e,i)=>{var n=i(1953),r=i(8351),s=i(7473),a=i(4286),o=i(3389),h=i(2273),l=i(1392),u=i(3232),c=i(9422),d=i(2529),f=new s({Extends:h,Mixins:[a.AlphaSingle,a.BlendMode,a.ComputedSize,a.Depth,a.Mask,a.PostPipeline,a.Transform,a.Visible,u],initialize:function(t,e,i,n){h.call(this,t,"Container"),this.list=[],this.exclusive=!0,this.maxSize=-1,this.position=0,this.localTransform=new a.TransformMatrix,this.tempTransformMatrix=new a.TransformMatrix,this._sortKey="",this._sysEvents=t.sys.events,this.scrollFactorX=1,this.scrollFactorY=1,this.initPostPipeline(),this.setPosition(e,i),this.setBlendMode(r.SKIP_CHECK),n&&this.add(n)},originX:{get:function(){return.5}},originY:{get:function(){return.5}},displayOriginX:{get:function(){return.5*this.width}},displayOriginY:{get:function(){return.5*this.height}},setExclusive:function(t){return void 0===t&&(t=!0),this.exclusive=t,this},getBounds:function(t){if(void 0===t&&(t=new l),t.setTo(this.x,this.y,0,0),this.parentContainer){var e=this.parentContainer.getBoundsTransformMatrix().transformPoint(this.x,this.y);t.setTo(e.x,e.y,0,0)}if(this.list.length>0){var i=this.list,n=new l,r=!1;t.setEmpty();for(var s=0;s<i.length;s++){var a=i[s];a.getBounds&&(a.getBounds(n),r?c(n,t,t):(t.setTo(n.x,n.y,n.width,n.height),r=!0))}}return t},addHandler:function(t){t.once(o.DESTROY,this.remove,this),this.exclusive&&(t.parentContainer&&t.parentContainer.remove(t),t.parentContainer=this,t.removeFromDisplayList(),t.addedToScene())},removeHandler:function(t){t.off(o.DESTROY,this.remove,this),this.exclusive&&(t.parentContainer=null,t.removedFromScene(),t.addToDisplayList())},pointToContainer:function(t,e){void 0===e&&(e=new d),this.parentContainer?this.parentContainer.pointToContainer(t,e):(e.x=t.x,e.y=t.y);var i=this.tempTransformMatrix;return i.applyITRS(this.x,this.y,this.rotation,this.scaleX,this.scaleY),i.invert(),i.transformPoint(t.x,t.y,e),e},getBoundsTransformMatrix:function(){return this.getWorldTransformMatrix(this.tempTransformMatrix,this.localTransform)},add:function(t){return n.Add(this.list,t,this.maxSize,this.addHandler,this),this},addAt:function(t,e){return n.AddAt(this.list,t,e,this.maxSize,this.addHandler,this),this},getAt:function(t){return this.list[t]},getIndex:function(t){return this.list.indexOf(t)},sort:function(t,e){return t?(void 0===e&&(e=function(e,i){return e[t]-i[t]}),n.StableSort(this.list,e),this):this},getByName:function(t){return n.GetFirst(this.list,"name",t)},getRandom:function(t,e){return n.GetRandom(this.list,t,e)},getFirst:function(t,e,i,r){return n.GetFirst(this.list,t,e,i,r)},getAll:function(t,e,i,r){return n.GetAll(this.list,t,e,i,r)},count:function(t,e,i,r){return n.CountAllMatching(this.list,t,e,i,r)},swap:function(t,e){return n.Swap(this.list,t,e),this},moveTo:function(t,e){return n.MoveTo(this.list,t,e),this},moveAbove:function(t,e){return n.MoveAbove(this.list,t,e),this},moveBelow:function(t,e){return n.MoveBelow(this.list,t,e),this},remove:function(t,e){var i=n.Remove(this.list,t,this.removeHandler,this);if(e&&i){Array.isArray(i)||(i=[i]);for(var r=0;r<i.length;r++)i[r].destroy()}return this},removeAt:function(t,e){var i=n.RemoveAt(this.list,t,this.removeHandler,this);return e&&i&&i.destroy(),this},removeBetween:function(t,e,i){var r=n.RemoveBetween(this.list,t,e,this.removeHandler,this);if(i)for(var s=0;s<r.length;s++)r[s].destroy();return this},removeAll:function(t){var e=this.list;if(t){for(var i=0;i<e.length;i++)e[i]&&e[i].scene&&(e[i].off(o.DESTROY,this.remove,this),e[i].destroy());this.list=[]}else n.RemoveBetween(e,0,e.length,this.removeHandler,this);return this},bringToTop:function(t){return n.BringToTop(this.list,t),this},sendToBack:function(t){return n.SendToBack(this.list,t),this},moveUp:function(t){return n.MoveUp(this.list,t),this},moveDown:function(t){return n.MoveDown(this.list,t),this},reverse:function(){return this.list.reverse(),this},shuffle:function(){return n.Shuffle(this.list),this},replace:function(t,e,i){return n.Replace(this.list,t,e)&&(this.addHandler(e),this.removeHandler(t),i&&t.destroy()),this},exists:function(t){return this.list.indexOf(t)>-1},setAll:function(t,e,i,r){return n.SetAll(this.list,t,e,i,r),this},each:function(t,e){var i,n=[null],r=this.list.slice(),s=r.length;for(i=2;i<arguments.length;i++)n.push(arguments[i]);for(i=0;i<s;i++)n[0]=r[i],t.apply(e,n);return this},iterate:function(t,e){var i,n=[null];for(i=2;i<arguments.length;i++)n.push(arguments[i]);for(i=0;i<this.list.length;i++)n[0]=this.list[i],t.apply(e,n);return this},setScrollFactor:function(t,e,i){return void 0===e&&(e=t),void 0===i&&(i=!1),this.scrollFactorX=t,this.scrollFactorY=e,i&&(n.SetAll(this.list,"scrollFactorX",t),n.SetAll(this.list,"scrollFactorY",e)),this},length:{get:function(){return this.list.length}},first:{get:function(){return this.position=0,this.list.length>0?this.list[0]:null}},last:{get:function(){return this.list.length>0?(this.position=this.list.length-1,this.list[this.position]):null}},next:{get:function(){return this.position<this.list.length?(this.position++,this.list[this.position]):null}},previous:{get:function(){return this.position>0?(this.position--,this.list[this.position]):null}},preDestroy:function(){this.removeAll(!!this.exclusive),this.localTransform.destroy(),this.tempTransformMatrix.destroy(),this.list=[]}});t.exports=f},3232:(t,e,i)=>{var n=i(1984),r=n,s=n;r=i(4343),t.exports={renderWebGL:r,renderCanvas:s}},4343:t=>{t.exports=function(t,e,i,n){i.addToRenderList(e);var r=e.list,s=r.length;if(0!==s){var a=e.localTransform;n?(a.loadIdentity(),a.multiply(n),a.translate(e.x,e.y),a.rotate(e.rotation),a.scale(e.scaleX,e.scaleY)):a.applyITRS(e.x,e.y,e.rotation,e.scaleX,e.scaleY),t.pipelines.preBatch(e);var o=-1!==e.blendMode;o||t.setBlendMode(0);for(var h=e.alpha,l=e.scrollFactorX,u=e.scrollFactorY,c=0;c<s;c++){var d=r[c];if(d.willRender(i)){var f,p,v,g;if(void 0!==d.alphaTopLeft)f=d.alphaTopLeft,p=d.alphaTopRight,v=d.alphaBottomLeft,g=d.alphaBottomRight;else{var m=d.alpha;f=m,p=m,v=m,g=m}var M=d.scrollFactorX,x=d.scrollFactorY;o||d.blendMode===t.currentBlendMode||t.setBlendMode(d.blendMode);var y=d.mask;y&&y.preRenderWebGL(t,d,i);var w=d.type;w!==t.currentType&&(t.newType=!0,t.currentType=w),t.nextTypeMatch=c<s-1&&r[c+1].type===t.currentType,d.setScrollFactor(M*l,x*u),d.setAlpha(f*h,p*h,v*h,g*h),d.renderWebGL(t,d,i,a,e),d.setAlpha(f,p,v,g),d.setScrollFactor(M,x),y&&y.postRenderWebGL(t,i),t.newType=!1}}t.pipelines.postBatch(e)}}},6608:t=>{t.exports="addedtoscene"},4265:t=>{t.exports="destroy"},8671:t=>{t.exports="removedfromscene"},3420:t=>{t.exports="complete"},601:t=>{t.exports="created"},7919:t=>{t.exports="error"},6231:t=>{t.exports="locked"},5241:t=>{t.exports="loop"},8325:t=>{t.exports="playing"},3356:t=>{t.exports="play"},7513:t=>{t.exports="seeked"},5788:t=>{t.exports="seeking"},7111:t=>{t.exports="stalled"},8118:t=>{t.exports="stop"},9184:t=>{t.exports="textureready"},4287:t=>{t.exports="unlocked"},857:t=>{t.exports="unsupported"},3389:(t,e,i)=>{t.exports={ADDED_TO_SCENE:i(6608),DESTROY:i(4265),REMOVED_FROM_SCENE:i(8671),VIDEO_COMPLETE:i(3420),VIDEO_CREATED:i(601),VIDEO_ERROR:i(7919),VIDEO_LOCKED:i(6231),VIDEO_LOOP:i(5241),VIDEO_PLAY:i(3356),VIDEO_PLAYING:i(8325),VIDEO_SEEKED:i(7513),VIDEO_SEEKING:i(5788),VIDEO_STALLED:i(7111),VIDEO_STOP:i(8118),VIDEO_TEXTURE:i(9184),VIDEO_UNLOCKED:i(4287),VIDEO_UNSUPPORTED:i(857)}},1643:t=>{t.exports={CIRCLE:0,ELLIPSE:1,LINE:2,POINT:3,POLYGON:4,RECTANGLE:5,TRIANGLE:6}},8881:(t,e,i)=>{var n=i(7655);t.exports=function(t,e,i){return void 0===i&&(i=new n),i.x=t.x1+(t.x2-t.x1)*e,i.y=t.y1+(t.y2-t.y1)*e,i}},4479:(t,e,i)=>{var n=i(4771),r=i(7655);t.exports=function(t,e,i,s){void 0===s&&(s=[]),!e&&i>0&&(e=n(t)/i);for(var a=t.x1,o=t.y1,h=t.x2,l=t.y2,u=0;u<e;u++){var c=u/e,d=a+(h-a)*c,f=o+(l-o)*c;s.push(new r(d,f))}return s}},4771:t=>{t.exports=function(t){return Math.sqrt((t.x2-t.x1)*(t.x2-t.x1)+(t.y2-t.y1)*(t.y2-t.y1))}},284:(t,e,i)=>{var n=i(7473),r=i(8881),s=i(4479),a=i(1643),o=i(3915),h=i(2529),l=new n({initialize:function(t,e,i,n){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=0),void 0===n&&(n=0),this.type=a.LINE,this.x1=t,this.y1=e,this.x2=i,this.y2=n},getPoint:function(t,e){return r(this,t,e)},getPoints:function(t,e,i){return s(this,t,e,i)},getRandomPoint:function(t){return o(this,t)},setTo:function(t,e,i,n){return void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=0),void 0===n&&(n=0),this.x1=t,this.y1=e,this.x2=i,this.y2=n,this},setFromObjects:function(t,e){return this.x1=t.x,this.y1=t.y,this.x2=e.x,this.y2=e.y,this},getPointA:function(t){return void 0===t&&(t=new h),t.set(this.x1,this.y1),t},getPointB:function(t){return void 0===t&&(t=new h),t.set(this.x2,this.y2),t},left:{get:function(){return Math.min(this.x1,this.x2)},set:function(t){this.x1<=this.x2?this.x1=t:this.x2=t}},right:{get:function(){return Math.max(this.x1,this.x2)},set:function(t){this.x1>this.x2?this.x1=t:this.x2=t}},top:{get:function(){return Math.min(this.y1,this.y2)},set:function(t){this.y1<=this.y2?this.y1=t:this.y2=t}},bottom:{get:function(){return Math.max(this.y1,this.y2)},set:function(t){this.y1>this.y2?this.y1=t:this.y2=t}}});t.exports=l},3915:(t,e,i)=>{var n=i(7655);t.exports=function(t,e){void 0===e&&(e=new n);var i=Math.random();return e.x=t.x1+i*(t.x2-t.x1),e.y=t.y1+i*(t.y2-t.y1),e}},7655:(t,e,i)=>{var n=i(7473),r=i(1643),s=new n({initialize:function(t,e){void 0===t&&(t=0),void 0===e&&(e=t),this.type=r.POINT,this.x=t,this.y=e},setTo:function(t,e){return void 0===t&&(t=0),void 0===e&&(e=t),this.x=t,this.y=e,this}});t.exports=s},5956:t=>{t.exports=function(t,e,i){return!(t.width<=0||t.height<=0)&&(t.x<=e&&t.x+t.width>=e&&t.y<=i&&t.y+t.height>=i)}},716:(t,e,i)=>{var n=i(7120),r=i(7655);t.exports=function(t,e,i){if(void 0===i&&(i=new r),e<=0||e>=1)return i.x=t.x,i.y=t.y,i;var s=n(t)*e;return e>.5?(s-=t.width+t.height)<=t.width?(i.x=t.right-s,i.y=t.bottom):(i.x=t.x,i.y=t.bottom-(s-t.width)):s<=t.width?(i.x=t.x+s,i.y=t.y):(i.x=t.right,i.y=t.y+(s-t.width)),i}},8151:(t,e,i)=>{var n=i(716),r=i(7120);t.exports=function(t,e,i,s){void 0===s&&(s=[]),!e&&i>0&&(e=r(t)/i);for(var a=0;a<e;a++){var o=a/e;s.push(n(t,o))}return s}},7120:t=>{t.exports=function(t){return 2*(t.width+t.height)}},2161:(t,e,i)=>{var n=i(7655);t.exports=function(t,e){return void 0===e&&(e=new n),e.x=t.x+Math.random()*t.width,e.y=t.y+Math.random()*t.height,e}},1392:(t,e,i)=>{var n=i(7473),r=i(5956),s=i(716),a=i(8151),o=i(1643),h=i(284),l=i(2161),u=new n({initialize:function(t,e,i,n){void 0===t&&(t=0),void 0===e&&(e=0),void 0===i&&(i=0),void 0===n&&(n=0),this.type=o.RECTANGLE,this.x=t,this.y=e,this.width=i,this.height=n},contains:function(t,e){return r(this,t,e)},getPoint:function(t,e){return s(this,t,e)},getPoints:function(t,e,i){return a(this,t,e,i)},getRandomPoint:function(t){return l(this,t)},setTo:function(t,e,i,n){return this.x=t,this.y=e,this.width=i,this.height=n,this},setEmpty:function(){return this.setTo(0,0,0,0)},setPosition:function(t,e){return void 0===e&&(e=t),this.x=t,this.y=e,this},setSize:function(t,e){return void 0===e&&(e=t),this.width=t,this.height=e,this},isEmpty:function(){return this.width<=0||this.height<=0},getLineA:function(t){return void 0===t&&(t=new h),t.setTo(this.x,this.y,this.right,this.y),t},getLineB:function(t){return void 0===t&&(t=new h),t.setTo(this.right,this.y,this.right,this.bottom),t},getLineC:function(t){return void 0===t&&(t=new h),t.setTo(this.right,this.bottom,this.x,this.bottom),t},getLineD:function(t){return void 0===t&&(t=new h),t.setTo(this.x,this.bottom,this.x,this.y),t},left:{get:function(){return this.x},set:function(t){t>=this.right?this.width=0:this.width=this.right-t,this.x=t}},right:{get:function(){return this.x+this.width},set:function(t){t<=this.x?this.width=0:this.width=t-this.x}},top:{get:function(){return this.y},set:function(t){t>=this.bottom?this.height=0:this.height=this.bottom-t,this.y=t}},bottom:{get:function(){return this.y+this.height},set:function(t){t<=this.y?this.height=0:this.height=t-this.y}},centerX:{get:function(){return this.x+this.width/2},set:function(t){this.x=t-this.width/2}},centerY:{get:function(){return this.y+this.height/2},set:function(t){this.y=t-this.height/2}}});t.exports=u},9422:(t,e,i)=>{var n=i(1392);t.exports=function(t,e,i){void 0===i&&(i=new n);var r=Math.min(t.x,e.x),s=Math.min(t.y,e.y),a=Math.max(t.right,e.right)-r,o=Math.max(t.bottom,e.bottom)-s;return i.setTo(r,s,a,o)}},1593:(t,e,i)=>{var n=i(7473),r=i(4359),s=i(1179),a=i(4597),o=i(5593),h=i(7410),l=i(5874),u=i(707),c=new n({initialize:function(t,e){if(this.loader=t,this.cache=a(e,"cache",!1),this.type=a(e,"type",!1),!this.type)throw new Error("Invalid File type: "+this.type);this.key=a(e,"key",!1);var i=this.key;if(t.prefix&&""!==t.prefix&&(this.key=t.prefix+i),!this.key)throw new Error("Invalid File key: "+this.key);var n=a(e,"url");void 0===n?n=t.path+i+"."+a(e,"extension",""):"string"!=typeof n||n.match(/^(?:blob:|data:|capacitor:\/\/|http:\/\/|https:\/\/|\/\/)/)||(n=t.path+n),this.url=n,this.src="",this.xhrSettings=u(a(e,"responseType",void 0)),a(e,"xhrSettings",!1)&&(this.xhrSettings=h(this.xhrSettings,a(e,"xhrSettings",{}))),this.xhrLoader=null,this.state="function"==typeof this.url?r.FILE_POPULATED:r.FILE_PENDING,this.bytesTotal=0,this.bytesLoaded=-1,this.percentComplete=-1,this.crossOrigin=void 0,this.data=void 0,this.config=a(e,"config",{}),this.multiFile,this.linkFile},setLink:function(t){this.linkFile=t,t.linkFile=this},resetXHR:function(){this.xhrLoader&&(this.xhrLoader.onload=void 0,this.xhrLoader.onerror=void 0,this.xhrLoader.onprogress=void 0)},load:function(){this.state===r.FILE_POPULATED?this.loader.nextFile(this,!0):(this.state=r.FILE_LOADING,this.src=o(this,this.loader.baseURL),0===this.src.indexOf("data:")?console.warn("Local data URIs are not supported: "+this.key):this.xhrLoader=l(this,this.loader.xhr))},onLoad:function(t,e){var i=t.responseURL&&this.loader.localSchemes.some((function(e){return 0===t.responseURL.indexOf(e)}))&&0===e.target.status,n=!(e.target&&200!==e.target.status)||i;4===t.readyState&&t.status>=400&&t.status<=599&&(n=!1),this.state=r.FILE_LOADED,this.resetXHR(),this.loader.nextFile(this,n)},onError:function(){this.resetXHR(),this.loader.nextFile(this,!1)},onProgress:function(t){t.lengthComputable&&(this.bytesLoaded=t.loaded,this.bytesTotal=t.total,this.percentComplete=Math.min(this.bytesLoaded/this.bytesTotal,1),this.loader.emit(s.FILE_PROGRESS,this,this.percentComplete))},onProcess:function(){this.state=r.FILE_PROCESSING,this.onProcessComplete()},onProcessComplete:function(){this.state=r.FILE_COMPLETE,this.multiFile&&this.multiFile.onFileComplete(this),this.loader.fileProcessComplete(this)},onProcessError:function(){console.error('Failed to process file: %s "%s"',this.type,this.key),this.state=r.FILE_ERRORED,this.multiFile&&this.multiFile.onFileFailed(this),this.loader.fileProcessComplete(this)},hasCacheConflict:function(){return this.cache&&this.cache.exists(this.key)},addToCache:function(){this.cache&&this.data&&this.cache.add(this.key,this.data)},pendingDestroy:function(t){if(this.state!==r.FILE_PENDING_DESTROY){void 0===t&&(t=this.data);var e=this.key,i=this.type;this.loader.emit(s.FILE_COMPLETE,e,i,t),this.loader.emit(s.FILE_KEY_COMPLETE+i+"-"+e,e,i,t),this.loader.flagForRemoval(this),this.state=r.FILE_PENDING_DESTROY}},destroy:function(){this.loader=null,this.cache=null,this.xhrSettings=null,this.multiFile=null,this.linkFile=null,this.data=null}});c.createObjectURL=function(t,e,i){if("function"==typeof URL)t.src=URL.createObjectURL(e);else{var n=new FileReader;n.onload=function(){t.removeAttribute("crossOrigin"),t.src="data:"+(e.type||i)+";base64,"+n.result.split(",")[1]},n.onerror=t.onerror,n.readAsDataURL(e)}},c.revokeObjectURL=function(t){"function"==typeof URL&&URL.revokeObjectURL(t.src)},t.exports=c},9845:t=>{var e={},i={install:function(t){for(var i in e)t[i]=e[i]},register:function(t,i){e[t]=i},destroy:function(){e={}}};t.exports=i},5593:t=>{t.exports=function(t,e){return!!t.url&&(t.url.match(/^(?:blob:|data:|capacitor:\/\/|http:\/\/|https:\/\/|\/\/)/)?t.url:e+t.url)}},7410:(t,e,i)=>{var n=i(1030),r=i(707);t.exports=function(t,e){var i=void 0===t?r():n({},t);if(e)for(var s in e)void 0!==e[s]&&(i[s]=e[s]);return i}},3137:(t,e,i)=>{var n=i(7473),r=i(4359),s=i(1179),a=new n({initialize:function(t,e,i,n){var s=[];n.forEach((function(t){t&&s.push(t)})),this.loader=t,this.type=e,this.key=i;var a=this.key;t.prefix&&""!==t.prefix&&(this.key=t.prefix+a),this.multiKeyIndex=t.multiKeyIndex++,this.files=s,this.state=r.FILE_PENDING,this.complete=!1,this.pending=s.length,this.failed=0,this.config={},this.baseURL=t.baseURL,this.path=t.path,this.prefix=t.prefix;for(var o=0;o<s.length;o++)s[o].multiFile=this},isReadyToProcess:function(){return 0===this.pending&&0===this.failed&&!this.complete},addToMultiFile:function(t){return this.files.push(t),t.multiFile=this,this.pending++,this.complete=!1,this},onFileComplete:function(t){-1!==this.files.indexOf(t)&&this.pending--},onFileFailed:function(t){-1!==this.files.indexOf(t)&&(this.failed++,console.error('File failed: %s "%s" (via %s "%s")',this.type,this.key,t.type,t.key))},pendingDestroy:function(){if(this.state!==r.FILE_PENDING_DESTROY){var t=this.key,e=this.type;this.loader.emit(s.FILE_COMPLETE,t,e),this.loader.emit(s.FILE_KEY_COMPLETE+e+"-"+t,t,e),this.loader.flagForRemoval(this);for(var i=0;i<this.files.length;i++)this.files[i].pendingDestroy();this.state=r.FILE_PENDING_DESTROY}},destroy:function(){this.loader=null,this.files=null,this.config=null}});t.exports=a},5874:(t,e,i)=>{var n=i(7410);t.exports=function(t,e){var i=n(e,t.xhrSettings),r=new XMLHttpRequest;if(r.open("GET",t.src,i.async,i.user,i.password),r.responseType=t.xhrSettings.responseType,r.timeout=i.timeout,i.headers)for(var s in i.headers)r.setRequestHeader(s,i.headers[s]);return i.header&&i.headerValue&&r.setRequestHeader(i.header,i.headerValue),i.requestedWith&&r.setRequestHeader("X-Requested-With",i.requestedWith),i.overrideMimeType&&r.overrideMimeType(i.overrideMimeType),i.withCredentials&&(r.withCredentials=!0),r.onload=t.onLoad.bind(t,r),r.onerror=t.onError.bind(t,r),r.onprogress=t.onProgress.bind(t),r.send(),r}},707:t=>{t.exports=function(t,e,i,n,r,s){return void 0===t&&(t=""),void 0===e&&(e=!0),void 0===i&&(i=""),void 0===n&&(n=""),void 0===r&&(r=0),void 0===s&&(s=!1),{responseType:t,async:e,user:i,password:n,timeout:r,headers:void 0,header:void 0,headerValue:void 0,requestedWith:!1,overrideMimeType:void 0,withCredentials:s}}},4359:t=>{t.exports={LOADER_IDLE:0,LOADER_LOADING:1,LOADER_PROCESSING:2,LOADER_COMPLETE:3,LOADER_SHUTDOWN:4,LOADER_DESTROYED:5,FILE_PENDING:10,FILE_LOADING:11,FILE_LOADED:12,FILE_FAILED:13,FILE_PROCESSING:14,FILE_ERRORED:16,FILE_COMPLETE:17,FILE_DESTROYED:18,FILE_POPULATED:19,FILE_PENDING_DESTROY:20}},462:t=>{t.exports="addfile"},7297:t=>{t.exports="complete"},8660:t=>{t.exports="filecomplete"},6484:t=>{t.exports="filecomplete-"},7972:t=>{t.exports="loaderror"},1906:t=>{t.exports="load"},1441:t=>{t.exports="fileprogress"},1072:t=>{t.exports="postprocess"},1927:t=>{t.exports="progress"},6597:t=>{t.exports="start"},1179:(t,e,i)=>{t.exports={ADD:i(462),COMPLETE:i(7297),FILE_COMPLETE:i(8660),FILE_KEY_COMPLETE:i(6484),FILE_LOAD_ERROR:i(7972),FILE_LOAD:i(1906),FILE_PROGRESS:i(1441),POST_PROCESS:i(1072),PROGRESS:i(1927),START:i(6597)}},6732:(t,e,i)=>{var n=i(7473),r=i(4359),s=i(1593),a=i(9845),o=i(4597),h=i(2482),l=i(5593),u=new n({Extends:s,initialize:function t(e,i,n,r,a){var l,u="png";if(h(i)){var c=i;i=o(c,"key"),n=o(c,"url"),l=o(c,"normalMap"),r=o(c,"xhrSettings"),u=o(c,"extension",u),a=o(c,"frameConfig")}Array.isArray(n)&&(l=n[1],n=n[0]);var d={type:"image",cache:e.textureManager,extension:u,responseType:"blob",key:i,url:n,xhrSettings:r,config:a};if(s.call(this,e,d),l){var f=new t(e,this.key,l,r,a);f.type="normalMap",this.setLink(f),e.addFile(f)}this.useImageElementLoad="HTMLImageElement"===e.imageLoadType,this.useImageElementLoad&&(this.load=this.loadImage,this.onProcess=this.onProcessImage)},onProcess:function(){this.state=r.FILE_PROCESSING,this.data=new Image,this.data.crossOrigin=this.crossOrigin;var t=this;this.data.onload=function(){s.revokeObjectURL(t.data),t.onProcessComplete()},this.data.onerror=function(){s.revokeObjectURL(t.data),t.onProcessError()},s.createObjectURL(this.data,this.xhrLoader.response,"image/png")},onProcessImage:function(){var t=this.state;this.state=r.FILE_PROCESSING,t===r.FILE_LOADED?this.onProcessComplete():this.onProcessError()},loadImage:function(){if(this.state=r.FILE_LOADING,this.src=l(this,this.loader.baseURL),0===this.src.indexOf("data:"))console.warn("Local data URIs are not supported: "+this.key);else{this.data=new Image,this.data.crossOrigin=this.crossOrigin;var t=this;this.data.onload=function(){t.state=r.FILE_LOADED,t.loader.nextFile(t,!0)},this.data.onerror=function(){t.loader.nextFile(t,!1)},this.data.src=this.src}},addToCache:function(){var t=this.linkFile;t?t.state>=r.FILE_COMPLETE&&("spritesheet"===t.type?t.addToCache():"normalMap"===this.type?this.cache.addImage(this.key,t.data,this.data):this.cache.addImage(this.key,this.data,t.data)):this.cache.addImage(this.key,this.data)}});a.register("image",(function(t,e,i){if(Array.isArray(t))for(var n=0;n<t.length;n++)this.addFile(new u(this,t[n]));else this.addFile(new u(this,t,e,i));return this})),t.exports=u},704:(t,e,i)=>{var n=i(7473),r=i(4359),s=i(1593),a=i(9845),o=i(4597),h=i(5851),l=i(2482),u=new n({Extends:s,initialize:function(t,e,i,n,a){var u="json";if(l(e)){var c=e;e=o(c,"key"),i=o(c,"url"),n=o(c,"xhrSettings"),u=o(c,"extension",u),a=o(c,"dataKey",a)}var d={type:"json",cache:t.cacheManager.json,extension:u,responseType:"text",key:e,url:i,xhrSettings:n,config:a};s.call(this,t,d),l(i)&&(this.data=a?h(i,a):i,this.state=r.FILE_POPULATED)},onProcess:function(){if(this.state!==r.FILE_POPULATED){this.state=r.FILE_PROCESSING;try{var t=JSON.parse(this.xhrLoader.responseText)}catch(t){throw this.onProcessError(),t}var e=this.config;this.data="string"==typeof e?h(t,e,t):t}this.onProcessComplete()}});a.register("json",(function(t,e,i,n){if(Array.isArray(t))for(var r=0;r<t.length;r++)this.addFile(new u(this,t[r]));else this.addFile(new u(this,t,e,n,i));return this})),t.exports=u},1192:(t,e,i)=>{var n=i(7473),r=i(4359),s=i(1593),a=i(9845),o=i(4597),h=i(2482),l=new n({Extends:s,initialize:function(t,e,i,n){var r="text",a="txt",l=t.cacheManager.text;if(h(e)){var u=e;e=o(u,"key"),i=o(u,"url"),n=o(u,"xhrSettings"),a=o(u,"extension",a),r=o(u,"type",r),l=o(u,"cache",l)}var c={type:r,cache:l,extension:a,responseType:"text",key:e,url:i,xhrSettings:n};s.call(this,t,c)},onProcess:function(){this.state=r.FILE_PROCESSING,this.data=this.xhrLoader.responseText,this.onProcessComplete()}});a.register("text",(function(t,e,i){if(Array.isArray(t))for(var n=0;n<t.length;n++)this.addFile(new l(this,t[n]));else this.addFile(new l(this,t,e,i));return this})),t.exports=l},3136:t=>{t.exports=function(t){for(var e=0,i=0;i<t.length;i++)e+=+t[i];return e/t.length}},785:(t,e,i)=>{var n=i(3916);t.exports=function(t,e){return n(t)/n(e)/n(t-e)}},7025:t=>{t.exports=function(t,e){return Math.floor(Math.random()*(e-t+1)+t)}},48:t=>{t.exports=function(t,e,i,n,r){var s=.5*(n-e),a=.5*(r-i),o=t*t;return(2*i-2*n+s+a)*(t*o)+(-3*i+3*n-2*s-a)*o+s*t+i}},5035:t=>{t.exports=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=10);var n=Math.pow(i,-e);return Math.ceil(t*n)/n}},2915:t=>{t.exports=function(t,e,i){return Math.max(e,Math.min(i,t))}},7149:(t,e,i)=>{var n=i(7425);t.exports=function(t){return t*n.DEG_TO_RAD}},2975:t=>{t.exports=function(t,e){return Math.abs(t-e)}},2107:(t,e,i)=>{var n=i(2915),r=i(7473),s=i(9652),a=i(1984),o=new s,h=new r({initialize:function t(e,i,n,r){void 0===e&&(e=0),void 0===i&&(i=0),void 0===n&&(n=0),void 0===r&&(r=t.DefaultOrder),this._x=e,this._y=i,this._z=n,this._order=r,this.onChangeCallback=a},x:{get:function(){return this._x},set:function(t){this._x=t,this.onChangeCallback(this)}},y:{get:function(){return this._y},set:function(t){this._y=t,this.onChangeCallback(this)}},z:{get:function(){return this._z},set:function(t){this._z=t,this.onChangeCallback(this)}},order:{get:function(){return this._order},set:function(t){this._order=t,this.onChangeCallback(this)}},set:function(t,e,i,n){return void 0===n&&(n=this._order),this._x=t,this._y=e,this._z=i,this._order=n,this.onChangeCallback(this),this},copy:function(t){return this.set(t.x,t.y,t.z,t.order)},setFromQuaternion:function(t,e,i){return void 0===e&&(e=this._order),void 0===i&&(i=!1),o.fromQuat(t),this.setFromRotationMatrix(o,e,i)},setFromRotationMatrix:function(t,e,i){void 0===e&&(e=this._order),void 0===i&&(i=!1);var r=t.val,s=r[0],a=r[4],o=r[8],h=r[1],l=r[5],u=r[9],c=r[2],d=r[6],f=r[10],p=0,v=0,g=0,m=.99999;switch(e){case"XYZ":v=Math.asin(n(o,-1,1)),Math.abs(o)<m?(p=Math.atan2(-u,f),g=Math.atan2(-a,s)):p=Math.atan2(d,l);break;case"YXZ":p=Math.asin(-n(u,-1,1)),Math.abs(u)<m?(v=Math.atan2(o,f),g=Math.atan2(h,l)):v=Math.atan2(-c,s);break;case"ZXY":p=Math.asin(n(d,-1,1)),Math.abs(d)<m?(v=Math.atan2(-c,f),g=Math.atan2(-a,l)):g=Math.atan2(h,s);break;case"ZYX":v=Math.asin(-n(c,-1,1)),Math.abs(c)<m?(p=Math.atan2(d,f),g=Math.atan2(h,s)):g=Math.atan2(-a,l);break;case"YZX":g=Math.asin(n(h,-1,1)),Math.abs(h)<m?(p=Math.atan2(-u,l),v=Math.atan2(-c,s)):v=Math.atan2(o,f);break;case"XZY":g=Math.asin(-n(a,-1,1)),Math.abs(a)<m?(p=Math.atan2(d,l),v=Math.atan2(o,s)):p=Math.atan2(-u,f)}return this._x=p,this._y=v,this._z=g,this._order=e,i&&this.onChangeCallback(this),this}});h.RotationOrders=["XYZ","YXZ","ZXY","ZYX","YZX","XZY"],h.DefaultOrder="XYZ",t.exports=h},3916:t=>{t.exports=function(t){if(0===t)return 1;for(var e=t;--t;)e*=t;return e}},104:t=>{t.exports=function(t,e){return Math.random()*(e-t)+t}},4941:t=>{t.exports=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=10);var n=Math.pow(i,-e);return Math.floor(t*n)/n}},1555:(t,e,i)=>{var n=i(2915);t.exports=function(t,e,i){return(i-e)*(t=n(t,0,1))+e}},5005:t=>{t.exports=function(t,e){return t/e/1e3}},3702:t=>{t.exports=function(t){return t==parseFloat(t)?!(t%2):void 0}},8820:t=>{t.exports=function(t){return t===parseFloat(t)?!(t%2):void 0}},1743:t=>{t.exports=function(t,e,i){return(e-t)*i+t}},3416:t=>{t.exports=function(t,e,i){return void 0===i&&(i=0),t.clone().lerp(e,i)}},2149:(t,e,i)=>{var n=new(i(7473))({initialize:function(t){this.val=new Float32Array(9),t?this.copy(t):this.identity()},clone:function(){return new n(this)},set:function(t){return this.copy(t)},copy:function(t){var e=this.val,i=t.val;return e[0]=i[0],e[1]=i[1],e[2]=i[2],e[3]=i[3],e[4]=i[4],e[5]=i[5],e[6]=i[6],e[7]=i[7],e[8]=i[8],this},fromMat4:function(t){var e=t.val,i=this.val;return i[0]=e[0],i[1]=e[1],i[2]=e[2],i[3]=e[4],i[4]=e[5],i[5]=e[6],i[6]=e[8],i[7]=e[9],i[8]=e[10],this},fromArray:function(t){var e=this.val;return e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],e[6]=t[6],e[7]=t[7],e[8]=t[8],this},identity:function(){var t=this.val;return t[0]=1,t[1]=0,t[2]=0,t[3]=0,t[4]=1,t[5]=0,t[6]=0,t[7]=0,t[8]=1,this},transpose:function(){var t=this.val,e=t[1],i=t[2],n=t[5];return t[1]=t[3],t[2]=t[6],t[3]=e,t[5]=t[7],t[6]=i,t[7]=n,this},invert:function(){var t=this.val,e=t[0],i=t[1],n=t[2],r=t[3],s=t[4],a=t[5],o=t[6],h=t[7],l=t[8],u=l*s-a*h,c=-l*r+a*o,d=h*r-s*o,f=e*u+i*c+n*d;return f?(f=1/f,t[0]=u*f,t[1]=(-l*i+n*h)*f,t[2]=(a*i-n*s)*f,t[3]=c*f,t[4]=(l*e-n*o)*f,t[5]=(-a*e+n*r)*f,t[6]=d*f,t[7]=(-h*e+i*o)*f,t[8]=(s*e-i*r)*f,this):null},adjoint:function(){var t=this.val,e=t[0],i=t[1],n=t[2],r=t[3],s=t[4],a=t[5],o=t[6],h=t[7],l=t[8];return t[0]=s*l-a*h,t[1]=n*h-i*l,t[2]=i*a-n*s,t[3]=a*o-r*l,t[4]=e*l-n*o,t[5]=n*r-e*a,t[6]=r*h-s*o,t[7]=i*o-e*h,t[8]=e*s-i*r,this},determinant:function(){var t=this.val,e=t[0],i=t[1],n=t[2],r=t[3],s=t[4],a=t[5],o=t[6],h=t[7],l=t[8];return e*(l*s-a*h)+i*(-l*r+a*o)+n*(h*r-s*o)},multiply:function(t){var e=this.val,i=e[0],n=e[1],r=e[2],s=e[3],a=e[4],o=e[5],h=e[6],l=e[7],u=e[8],c=t.val,d=c[0],f=c[1],p=c[2],v=c[3],g=c[4],m=c[5],M=c[6],x=c[7],y=c[8];return e[0]=d*i+f*s+p*h,e[1]=d*n+f*a+p*l,e[2]=d*r+f*o+p*u,e[3]=v*i+g*s+m*h,e[4]=v*n+g*a+m*l,e[5]=v*r+g*o+m*u,e[6]=M*i+x*s+y*h,e[7]=M*n+x*a+y*l,e[8]=M*r+x*o+y*u,this},translate:function(t){var e=this.val,i=t.x,n=t.y;return e[6]=i*e[0]+n*e[3]+e[6],e[7]=i*e[1]+n*e[4]+e[7],e[8]=i*e[2]+n*e[5]+e[8],this},rotate:function(t){var e=this.val,i=e[0],n=e[1],r=e[2],s=e[3],a=e[4],o=e[5],h=Math.sin(t),l=Math.cos(t);return e[0]=l*i+h*s,e[1]=l*n+h*a,e[2]=l*r+h*o,e[3]=l*s-h*i,e[4]=l*a-h*n,e[5]=l*o-h*r,this},scale:function(t){var e=this.val,i=t.x,n=t.y;return e[0]=i*e[0],e[1]=i*e[1],e[2]=i*e[2],e[3]=n*e[3],e[4]=n*e[4],e[5]=n*e[5],this},fromQuat:function(t){var e=t.x,i=t.y,n=t.z,r=t.w,s=e+e,a=i+i,o=n+n,h=e*s,l=e*a,u=e*o,c=i*a,d=i*o,f=n*o,p=r*s,v=r*a,g=r*o,m=this.val;return m[0]=1-(c+f),m[3]=l+g,m[6]=u-v,m[1]=l-g,m[4]=1-(h+f),m[7]=d+p,m[2]=u+v,m[5]=d-p,m[8]=1-(h+c),this},normalFromMat4:function(t){var e=t.val,i=this.val,n=e[0],r=e[1],s=e[2],a=e[3],o=e[4],h=e[5],l=e[6],u=e[7],c=e[8],d=e[9],f=e[10],p=e[11],v=e[12],g=e[13],m=e[14],M=e[15],x=n*h-r*o,y=n*l-s*o,w=n*u-a*o,E=r*l-s*h,b=r*u-a*h,T=s*u-a*l,A=c*g-d*v,S=c*m-f*v,R=c*M-p*v,C=d*m-f*g,I=d*M-p*g,P=f*M-p*m,L=x*P-y*I+w*C+E*R-b*S+T*A;return L?(L=1/L,i[0]=(h*P-l*I+u*C)*L,i[1]=(l*R-o*P-u*S)*L,i[2]=(o*I-h*R+u*A)*L,i[3]=(s*I-r*P-a*C)*L,i[4]=(n*P-s*R+a*S)*L,i[5]=(r*R-n*I-a*A)*L,i[6]=(g*T-m*b+M*E)*L,i[7]=(m*w-v*T-M*y)*L,i[8]=(v*b-g*w+M*x)*L,this):null}});t.exports=n},9652:(t,e,i)=>{var n=i(7473),r=i(5689),s=1e-6,a=new n({initialize:function(t){this.val=new Float32Array(16),t?this.copy(t):this.identity()},clone:function(){return new a(this)},set:function(t){return this.copy(t)},setValues:function(t,e,i,n,r,s,a,o,h,l,u,c,d,f,p,v){var g=this.val;return g[0]=t,g[1]=e,g[2]=i,g[3]=n,g[4]=r,g[5]=s,g[6]=a,g[7]=o,g[8]=h,g[9]=l,g[10]=u,g[11]=c,g[12]=d,g[13]=f,g[14]=p,g[15]=v,this},copy:function(t){var e=t.val;return this.setValues(e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15])},fromArray:function(t){return this.setValues(t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8],t[9],t[10],t[11],t[12],t[13],t[14],t[15])},zero:function(){return this.setValues(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)},transform:function(t,e,i){var n=o.fromQuat(i).val,r=e.x,s=e.y,a=e.z;return this.setValues(n[0]*r,n[1]*r,n[2]*r,0,n[4]*s,n[5]*s,n[6]*s,0,n[8]*a,n[9]*a,n[10]*a,0,t.x,t.y,t.z,1)},xyz:function(t,e,i){this.identity();var n=this.val;return n[12]=t,n[13]=e,n[14]=i,this},scaling:function(t,e,i){this.zero();var n=this.val;return n[0]=t,n[5]=e,n[10]=i,n[15]=1,this},identity:function(){return this.setValues(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)},transpose:function(){var t=this.val,e=t[1],i=t[2],n=t[3],r=t[6],s=t[7],a=t[11];return t[1]=t[4],t[2]=t[8],t[3]=t[12],t[4]=e,t[6]=t[9],t[7]=t[13],t[8]=i,t[9]=r,t[11]=t[14],t[12]=n,t[13]=s,t[14]=a,this},getInverse:function(t){return this.copy(t),this.invert()},invert:function(){var t=this.val,e=t[0],i=t[1],n=t[2],r=t[3],s=t[4],a=t[5],o=t[6],h=t[7],l=t[8],u=t[9],c=t[10],d=t[11],f=t[12],p=t[13],v=t[14],g=t[15],m=e*a-i*s,M=e*o-n*s,x=e*h-r*s,y=i*o-n*a,w=i*h-r*a,E=n*h-r*o,b=l*p-u*f,T=l*v-c*f,A=l*g-d*f,S=u*v-c*p,R=u*g-d*p,C=c*g-d*v,I=m*C-M*R+x*S+y*A-w*T+E*b;return I?(I=1/I,this.setValues((a*C-o*R+h*S)*I,(n*R-i*C-r*S)*I,(p*E-v*w+g*y)*I,(c*w-u*E-d*y)*I,(o*A-s*C-h*T)*I,(e*C-n*A+r*T)*I,(v*x-f*E-g*M)*I,(l*E-c*x+d*M)*I,(s*R-a*A+h*b)*I,(i*A-e*R-r*b)*I,(f*w-p*x+g*m)*I,(u*x-l*w-d*m)*I,(a*T-s*S-o*b)*I,(e*S-i*T+n*b)*I,(p*M-f*y-v*m)*I,(l*y-u*M+c*m)*I)):this},adjoint:function(){var t=this.val,e=t[0],i=t[1],n=t[2],r=t[3],s=t[4],a=t[5],o=t[6],h=t[7],l=t[8],u=t[9],c=t[10],d=t[11],f=t[12],p=t[13],v=t[14],g=t[15];return this.setValues(a*(c*g-d*v)-u*(o*g-h*v)+p*(o*d-h*c),-(i*(c*g-d*v)-u*(n*g-r*v)+p*(n*d-r*c)),i*(o*g-h*v)-a*(n*g-r*v)+p*(n*h-r*o),-(i*(o*d-h*c)-a*(n*d-r*c)+u*(n*h-r*o)),-(s*(c*g-d*v)-l*(o*g-h*v)+f*(o*d-h*c)),e*(c*g-d*v)-l*(n*g-r*v)+f*(n*d-r*c),-(e*(o*g-h*v)-s*(n*g-r*v)+f*(n*h-r*o)),e*(o*d-h*c)-s*(n*d-r*c)+l*(n*h-r*o),s*(u*g-d*p)-l*(a*g-h*p)+f*(a*d-h*u),-(e*(u*g-d*p)-l*(i*g-r*p)+f*(i*d-r*u)),e*(a*g-h*p)-s*(i*g-r*p)+f*(i*h-r*a),-(e*(a*d-h*u)-s*(i*d-r*u)+l*(i*h-r*a)),-(s*(u*v-c*p)-l*(a*v-o*p)+f*(a*c-o*u)),e*(u*v-c*p)-l*(i*v-n*p)+f*(i*c-n*u),-(e*(a*v-o*p)-s*(i*v-n*p)+f*(i*o-n*a)),e*(a*c-o*u)-s*(i*c-n*u)+l*(i*o-n*a))},determinant:function(){var t=this.val,e=t[0],i=t[1],n=t[2],r=t[3],s=t[4],a=t[5],o=t[6],h=t[7],l=t[8],u=t[9],c=t[10],d=t[11],f=t[12],p=t[13],v=t[14],g=t[15];return(e*a-i*s)*(c*g-d*v)-(e*o-n*s)*(u*g-d*p)+(e*h-r*s)*(u*v-c*p)+(i*o-n*a)*(l*g-d*f)-(i*h-r*a)*(l*v-c*f)+(n*h-r*o)*(l*p-u*f)},multiply:function(t){var e=this.val,i=e[0],n=e[1],r=e[2],s=e[3],a=e[4],o=e[5],h=e[6],l=e[7],u=e[8],c=e[9],d=e[10],f=e[11],p=e[12],v=e[13],g=e[14],m=e[15],M=t.val,x=M[0],y=M[1],w=M[2],E=M[3];return e[0]=x*i+y*a+w*u+E*p,e[1]=x*n+y*o+w*c+E*v,e[2]=x*r+y*h+w*d+E*g,e[3]=x*s+y*l+w*f+E*m,x=M[4],y=M[5],w=M[6],E=M[7],e[4]=x*i+y*a+w*u+E*p,e[5]=x*n+y*o+w*c+E*v,e[6]=x*r+y*h+w*d+E*g,e[7]=x*s+y*l+w*f+E*m,x=M[8],y=M[9],w=M[10],E=M[11],e[8]=x*i+y*a+w*u+E*p,e[9]=x*n+y*o+w*c+E*v,e[10]=x*r+y*h+w*d+E*g,e[11]=x*s+y*l+w*f+E*m,x=M[12],y=M[13],w=M[14],E=M[15],e[12]=x*i+y*a+w*u+E*p,e[13]=x*n+y*o+w*c+E*v,e[14]=x*r+y*h+w*d+E*g,e[15]=x*s+y*l+w*f+E*m,this},multiplyLocal:function(t){var e=this.val,i=t.val;return this.setValues(e[0]*i[0]+e[1]*i[4]+e[2]*i[8]+e[3]*i[12],e[0]*i[1]+e[1]*i[5]+e[2]*i[9]+e[3]*i[13],e[0]*i[2]+e[1]*i[6]+e[2]*i[10]+e[3]*i[14],e[0]*i[3]+e[1]*i[7]+e[2]*i[11]+e[3]*i[15],e[4]*i[0]+e[5]*i[4]+e[6]*i[8]+e[7]*i[12],e[4]*i[1]+e[5]*i[5]+e[6]*i[9]+e[7]*i[13],e[4]*i[2]+e[5]*i[6]+e[6]*i[10]+e[7]*i[14],e[4]*i[3]+e[5]*i[7]+e[6]*i[11]+e[7]*i[15],e[8]*i[0]+e[9]*i[4]+e[10]*i[8]+e[11]*i[12],e[8]*i[1]+e[9]*i[5]+e[10]*i[9]+e[11]*i[13],e[8]*i[2]+e[9]*i[6]+e[10]*i[10]+e[11]*i[14],e[8]*i[3]+e[9]*i[7]+e[10]*i[11]+e[11]*i[15],e[12]*i[0]+e[13]*i[4]+e[14]*i[8]+e[15]*i[12],e[12]*i[1]+e[13]*i[5]+e[14]*i[9]+e[15]*i[13],e[12]*i[2]+e[13]*i[6]+e[14]*i[10]+e[15]*i[14],e[12]*i[3]+e[13]*i[7]+e[14]*i[11]+e[15]*i[15])},premultiply:function(t){return this.multiplyMatrices(t,this)},multiplyMatrices:function(t,e){var i=t.val,n=e.val,r=i[0],s=i[4],a=i[8],o=i[12],h=i[1],l=i[5],u=i[9],c=i[13],d=i[2],f=i[6],p=i[10],v=i[14],g=i[3],m=i[7],M=i[11],x=i[15],y=n[0],w=n[4],E=n[8],b=n[12],T=n[1],A=n[5],S=n[9],R=n[13],C=n[2],I=n[6],P=n[10],L=n[14],O=n[3],_=n[7],k=n[11],F=n[15];return this.setValues(r*y+s*T+a*C+o*O,h*y+l*T+u*C+c*O,d*y+f*T+p*C+v*O,g*y+m*T+M*C+x*O,r*w+s*A+a*I+o*_,h*w+l*A+u*I+c*_,d*w+f*A+p*I+v*_,g*w+m*A+M*I+x*_,r*E+s*S+a*P+o*k,h*E+l*S+u*P+c*k,d*E+f*S+p*P+v*k,g*E+m*S+M*P+x*k,r*b+s*R+a*L+o*F,h*b+l*R+u*L+c*F,d*b+f*R+p*L+v*F,g*b+m*R+M*L+x*F)},translate:function(t){return this.translateXYZ(t.x,t.y,t.z)},translateXYZ:function(t,e,i){var n=this.val;return n[12]=n[0]*t+n[4]*e+n[8]*i+n[12],n[13]=n[1]*t+n[5]*e+n[9]*i+n[13],n[14]=n[2]*t+n[6]*e+n[10]*i+n[14],n[15]=n[3]*t+n[7]*e+n[11]*i+n[15],this},scale:function(t){return this.scaleXYZ(t.x,t.y,t.z)},scaleXYZ:function(t,e,i){var n=this.val;return n[0]=n[0]*t,n[1]=n[1]*t,n[2]=n[2]*t,n[3]=n[3]*t,n[4]=n[4]*e,n[5]=n[5]*e,n[6]=n[6]*e,n[7]=n[7]*e,n[8]=n[8]*i,n[9]=n[9]*i,n[10]=n[10]*i,n[11]=n[11]*i,this},makeRotationAxis:function(t,e){var i=Math.cos(e),n=Math.sin(e),r=1-i,s=t.x,a=t.y,o=t.z,h=r*s,l=r*a;return this.setValues(h*s+i,h*a-n*o,h*o+n*a,0,h*a+n*o,l*a+i,l*o-n*s,0,h*o-n*a,l*o+n*s,r*o*o+i,0,0,0,0,1)},rotate:function(t,e){var i=this.val,n=e.x,r=e.y,a=e.z,o=Math.sqrt(n*n+r*r+a*a);if(Math.abs(o)<s)return this;n*=o=1/o,r*=o,a*=o;var h=Math.sin(t),l=Math.cos(t),u=1-l,c=i[0],d=i[1],f=i[2],p=i[3],v=i[4],g=i[5],m=i[6],M=i[7],x=i[8],y=i[9],w=i[10],E=i[11],b=i[12],T=i[13],A=i[14],S=i[15],R=n*n*u+l,C=r*n*u+a*h,I=a*n*u-r*h,P=n*r*u-a*h,L=r*r*u+l,O=a*r*u+n*h,_=n*a*u+r*h,k=r*a*u-n*h,F=a*a*u+l;return this.setValues(c*R+v*C+x*I,d*R+g*C+y*I,f*R+m*C+w*I,p*R+M*C+E*I,c*P+v*L+x*O,d*P+g*L+y*O,f*P+m*L+w*O,p*P+M*L+E*O,c*_+v*k+x*F,d*_+g*k+y*F,f*_+m*k+w*F,p*_+M*k+E*F,b,T,A,S)},rotateX:function(t){var e=this.val,i=Math.sin(t),n=Math.cos(t),r=e[4],s=e[5],a=e[6],o=e[7],h=e[8],l=e[9],u=e[10],c=e[11];return e[4]=r*n+h*i,e[5]=s*n+l*i,e[6]=a*n+u*i,e[7]=o*n+c*i,e[8]=h*n-r*i,e[9]=l*n-s*i,e[10]=u*n-a*i,e[11]=c*n-o*i,this},rotateY:function(t){var e=this.val,i=Math.sin(t),n=Math.cos(t),r=e[0],s=e[1],a=e[2],o=e[3],h=e[8],l=e[9],u=e[10],c=e[11];return e[0]=r*n-h*i,e[1]=s*n-l*i,e[2]=a*n-u*i,e[3]=o*n-c*i,e[8]=r*i+h*n,e[9]=s*i+l*n,e[10]=a*i+u*n,e[11]=o*i+c*n,this},rotateZ:function(t){var e=this.val,i=Math.sin(t),n=Math.cos(t),r=e[0],s=e[1],a=e[2],o=e[3],h=e[4],l=e[5],u=e[6],c=e[7];return e[0]=r*n+h*i,e[1]=s*n+l*i,e[2]=a*n+u*i,e[3]=o*n+c*i,e[4]=h*n-r*i,e[5]=l*n-s*i,e[6]=u*n-a*i,e[7]=c*n-o*i,this},fromRotationTranslation:function(t,e){var i=t.x,n=t.y,r=t.z,s=t.w,a=i+i,o=n+n,h=r+r,l=i*a,u=i*o,c=i*h,d=n*o,f=n*h,p=r*h,v=s*a,g=s*o,m=s*h;return this.setValues(1-(d+p),u+m,c-g,0,u-m,1-(l+p),f+v,0,c+g,f-v,1-(l+d),0,e.x,e.y,e.z,1)},fromQuat:function(t){var e=t.x,i=t.y,n=t.z,r=t.w,s=e+e,a=i+i,o=n+n,h=e*s,l=e*a,u=e*o,c=i*a,d=i*o,f=n*o,p=r*s,v=r*a,g=r*o;return this.setValues(1-(c+f),l+g,u-v,0,l-g,1-(h+f),d+p,0,u+v,d-p,1-(h+c),0,0,0,0,1)},frustum:function(t,e,i,n,r,s){var a=1/(e-t),o=1/(n-i),h=1/(r-s);return this.setValues(2*r*a,0,0,0,0,2*r*o,0,0,(e+t)*a,(n+i)*o,(s+r)*h,-1,0,0,s*r*2*h,0)},perspective:function(t,e,i,n){var r=1/Math.tan(t/2),s=1/(i-n);return this.setValues(r/e,0,0,0,0,r,0,0,0,0,(n+i)*s,-1,0,0,2*n*i*s,0)},perspectiveLH:function(t,e,i,n){return this.setValues(2*i/t,0,0,0,0,2*i/e,0,0,0,0,-n/(i-n),1,0,0,i*n/(i-n),0)},ortho:function(t,e,i,n,r,s){var a=t-e,o=i-n,h=r-s;return a=0===a?a:1/a,o=0===o?o:1/o,h=0===h?h:1/h,this.setValues(-2*a,0,0,0,0,-2*o,0,0,0,0,2*h,0,(t+e)*a,(n+i)*o,(s+r)*h,1)},lookAtRH:function(t,e,i){var n=this.val;return c.subVectors(t,e),0===c.getLengthSquared()&&(c.z=1),c.normalize(),l.crossVectors(i,c),0===l.getLengthSquared()&&(1===Math.abs(i.z)?c.x+=1e-4:c.z+=1e-4,c.normalize(),l.crossVectors(i,c)),l.normalize(),u.crossVectors(c,l),n[0]=l.x,n[1]=l.y,n[2]=l.z,n[4]=u.x,n[5]=u.y,n[6]=u.z,n[8]=c.x,n[9]=c.y,n[10]=c.z,this},lookAt:function(t,e,i){var n=t.x,r=t.y,a=t.z,o=i.x,h=i.y,l=i.z,u=e.x,c=e.y,d=e.z;if(Math.abs(n-u)<s&&Math.abs(r-c)<s&&Math.abs(a-d)<s)return this.identity();var f=n-u,p=r-c,v=a-d,g=1/Math.sqrt(f*f+p*p+v*v),m=h*(v*=g)-l*(p*=g),M=l*(f*=g)-o*v,x=o*p-h*f;(g=Math.sqrt(m*m+M*M+x*x))?(m*=g=1/g,M*=g,x*=g):(m=0,M=0,x=0);var y=p*x-v*M,w=v*m-f*x,E=f*M-p*m;return(g=Math.sqrt(y*y+w*w+E*E))?(y*=g=1/g,w*=g,E*=g):(y=0,w=0,E=0),this.setValues(m,y,f,0,M,w,p,0,x,E,v,0,-(m*n+M*r+x*a),-(y*n+w*r+E*a),-(f*n+p*r+v*a),1)},yawPitchRoll:function(t,e,i){this.zero(),o.zero(),h.zero();var n=this.val,r=o.val,s=h.val,a=Math.sin(i),l=Math.cos(i);return n[10]=1,n[15]=1,n[0]=l,n[1]=a,n[4]=-a,n[5]=l,a=Math.sin(e),l=Math.cos(e),r[0]=1,r[15]=1,r[5]=l,r[10]=l,r[9]=-a,r[6]=a,a=Math.sin(t),l=Math.cos(t),s[5]=1,s[15]=1,s[0]=l,s[2]=-a,s[8]=a,s[10]=l,this.multiplyLocal(o),this.multiplyLocal(h),this},setWorldMatrix:function(t,e,i,n,r){return this.yawPitchRoll(t.y,t.x,t.z),o.scaling(i.x,i.y,i.z),h.xyz(e.x,e.y,e.z),this.multiplyLocal(o),this.multiplyLocal(h),n&&this.multiplyLocal(n),r&&this.multiplyLocal(r),this},multiplyToMat4:function(t,e){var i=this.val,n=t.val,r=i[0],s=i[1],a=i[2],o=i[3],h=i[4],l=i[5],u=i[6],c=i[7],d=i[8],f=i[9],p=i[10],v=i[11],g=i[12],m=i[13],M=i[14],x=i[15],y=n[0],w=n[1],E=n[2],b=n[3],T=n[4],A=n[5],S=n[6],R=n[7],C=n[8],I=n[9],P=n[10],L=n[11],O=n[12],_=n[13],k=n[14],F=n[15];return e.setValues(y*r+w*h+E*d+b*g,w*s+w*l+E*f+b*m,E*a+w*u+E*p+b*M,b*o+w*c+E*v+b*x,T*r+A*h+S*d+R*g,T*s+A*l+S*f+R*m,T*a+A*u+S*p+R*M,T*o+A*c+S*v+R*x,C*r+I*h+P*d+L*g,C*s+I*l+P*f+L*m,C*a+I*u+P*p+L*M,C*o+I*c+P*v+L*x,O*r+_*h+k*d+F*g,O*s+_*l+k*f+F*m,O*a+_*u+k*p+F*M,O*o+_*c+k*v+F*x)},fromRotationXYTranslation:function(t,e,i){var n=e.x,r=e.y,s=e.z,a=Math.sin(t.x),o=Math.cos(t.x),h=Math.sin(t.y),l=Math.cos(t.y),u=n,c=r,d=s,f=-a,p=0-f*h,v=0-o*h,g=f*l,m=o*l;return i||(u=l*n+h*s,c=p*n+o*r+g*s,d=v*n+a*r+m*s),this.setValues(l,p,v,0,0,o,a,0,h,g,m,0,u,c,d,1)},getMaxScaleOnAxis:function(){var t=this.val,e=t[0]*t[0]+t[1]*t[1]+t[2]*t[2],i=t[4]*t[4]+t[5]*t[5]+t[6]*t[6],n=t[8]*t[8]+t[9]*t[9]+t[10]*t[10];return Math.sqrt(Math.max(e,i,n))}}),o=new a,h=new a,l=new r,u=new r,c=new r;t.exports=a},3733:t=>{t.exports=function(t,e,i){return Math.min(t+e,i)}},44:t=>{t.exports=function(t){var e=t.length;if(0===e)return 0;t.sort((function(t,e){return t-e}));var i=Math.floor(e/2);return e%2==0?(t[i]+t[i-1])/2:t[i]}},5385:t=>{t.exports=function(t,e,i){return Math.max(t-e,i)}},8585:t=>{t.exports=function(t,e,i,n){void 0===i&&(i=e+1);var r=(t-e)/(i-e);return r>1?void 0!==n?(r=(n-t)/(n-i))<0&&(r=0):r=1:r<0&&(r=0),r}},372:(t,e,i)=>{var n=i(7473),r=i(2149),s=i(1984),a=i(5689),o=1e-6,h=new Int8Array([1,2,0]),l=new Float32Array([0,0,0]),u=new a(1,0,0),c=new a(0,1,0),d=new a,f=new r,p=new n({initialize:function(t,e,i,n){this.onChangeCallback=s,this.set(t,e,i,n)},x:{get:function(){return this._x},set:function(t){this._x=t,this.onChangeCallback(this)}},y:{get:function(){return this._y},set:function(t){this._y=t,this.onChangeCallback(this)}},z:{get:function(){return this._z},set:function(t){this._z=t,this.onChangeCallback(this)}},w:{get:function(){return this._w},set:function(t){this._w=t,this.onChangeCallback(this)}},copy:function(t){return this.set(t)},set:function(t,e,i,n,r){return void 0===r&&(r=!0),"object"==typeof t?(this._x=t.x||0,this._y=t.y||0,this._z=t.z||0,this._w=t.w||0):(this._x=t||0,this._y=e||0,this._z=i||0,this._w=n||0),r&&this.onChangeCallback(this),this},add:function(t){return this._x+=t.x,this._y+=t.y,this._z+=t.z,this._w+=t.w,this.onChangeCallback(this),this},subtract:function(t){return this._x-=t.x,this._y-=t.y,this._z-=t.z,this._w-=t.w,this.onChangeCallback(this),this},scale:function(t){return this._x*=t,this._y*=t,this._z*=t,this._w*=t,this.onChangeCallback(this),this},length:function(){var t=this.x,e=this.y,i=this.z,n=this.w;return Math.sqrt(t*t+e*e+i*i+n*n)},lengthSq:function(){var t=this.x,e=this.y,i=this.z,n=this.w;return t*t+e*e+i*i+n*n},normalize:function(){var t=this.x,e=this.y,i=this.z,n=this.w,r=t*t+e*e+i*i+n*n;return r>0&&(r=1/Math.sqrt(r),this._x=t*r,this._y=e*r,this._z=i*r,this._w=n*r),this.onChangeCallback(this),this},dot:function(t){return this.x*t.x+this.y*t.y+this.z*t.z+this.w*t.w},lerp:function(t,e){void 0===e&&(e=0);var i=this.x,n=this.y,r=this.z,s=this.w;return this.set(i+e*(t.x-i),n+e*(t.y-n),r+e*(t.z-r),s+e*(t.w-s))},rotationTo:function(t,e){var i=t.x*e.x+t.y*e.y+t.z*e.z;return i<-.999999?(d.copy(u).cross(t).length()<o&&d.copy(c).cross(t),d.normalize(),this.setAxisAngle(d,Math.PI)):i>.999999?this.set(0,0,0,1):(d.copy(t).cross(e),this._x=d.x,this._y=d.y,this._z=d.z,this._w=1+i,this.normalize())},setAxes:function(t,e,i){var n=f.val;return n[0]=e.x,n[3]=e.y,n[6]=e.z,n[1]=i.x,n[4]=i.y,n[7]=i.z,n[2]=-t.x,n[5]=-t.y,n[8]=-t.z,this.fromMat3(f).normalize()},identity:function(){return this.set(0,0,0,1)},setAxisAngle:function(t,e){e*=.5;var i=Math.sin(e);return this.set(i*t.x,i*t.y,i*t.z,Math.cos(e))},multiply:function(t){var e=this.x,i=this.y,n=this.z,r=this.w,s=t.x,a=t.y,o=t.z,h=t.w;return this.set(e*h+r*s+i*o-n*a,i*h+r*a+n*s-e*o,n*h+r*o+e*a-i*s,r*h-e*s-i*a-n*o)},slerp:function(t,e){var i=this.x,n=this.y,r=this.z,s=this.w,a=t.x,h=t.y,l=t.z,u=t.w,c=i*a+n*h+r*l+s*u;c<0&&(c=-c,a=-a,h=-h,l=-l,u=-u);var d=1-e,f=e;if(1-c>o){var p=Math.acos(c),v=Math.sin(p);d=Math.sin((1-e)*p)/v,f=Math.sin(e*p)/v}return this.set(d*i+f*a,d*n+f*h,d*r+f*l,d*s+f*u)},invert:function(){var t=this.x,e=this.y,i=this.z,n=this.w,r=t*t+e*e+i*i+n*n,s=r?1/r:0;return this.set(-t*s,-e*s,-i*s,n*s)},conjugate:function(){return this._x=-this.x,this._y=-this.y,this._z=-this.z,this.onChangeCallback(this),this},rotateX:function(t){t*=.5;var e=this.x,i=this.y,n=this.z,r=this.w,s=Math.sin(t),a=Math.cos(t);return this.set(e*a+r*s,i*a+n*s,n*a-i*s,r*a-e*s)},rotateY:function(t){t*=.5;var e=this.x,i=this.y,n=this.z,r=this.w,s=Math.sin(t),a=Math.cos(t);return this.set(e*a-n*s,i*a+r*s,n*a+e*s,r*a-i*s)},rotateZ:function(t){t*=.5;var e=this.x,i=this.y,n=this.z,r=this.w,s=Math.sin(t),a=Math.cos(t);return this.set(e*a+i*s,i*a-e*s,n*a+r*s,r*a-n*s)},calculateW:function(){var t=this.x,e=this.y,i=this.z;return this.w=-Math.sqrt(1-t*t-e*e-i*i),this},setFromEuler:function(t,e){var i=t.x/2,n=t.y/2,r=t.z/2,s=Math.cos(i),a=Math.cos(n),o=Math.cos(r),h=Math.sin(i),l=Math.sin(n),u=Math.sin(r);switch(t.order){case"XYZ":this.set(h*a*o+s*l*u,s*l*o-h*a*u,s*a*u+h*l*o,s*a*o-h*l*u,e);break;case"YXZ":this.set(h*a*o+s*l*u,s*l*o-h*a*u,s*a*u-h*l*o,s*a*o+h*l*u,e);break;case"ZXY":this.set(h*a*o-s*l*u,s*l*o+h*a*u,s*a*u+h*l*o,s*a*o-h*l*u,e);break;case"ZYX":this.set(h*a*o-s*l*u,s*l*o+h*a*u,s*a*u-h*l*o,s*a*o+h*l*u,e);break;case"YZX":this.set(h*a*o+s*l*u,s*l*o+h*a*u,s*a*u-h*l*o,s*a*o-h*l*u,e);break;case"XZY":this.set(h*a*o-s*l*u,s*l*o-h*a*u,s*a*u+h*l*o,s*a*o+h*l*u,e)}return this},setFromRotationMatrix:function(t){var e,i=t.val,n=i[0],r=i[4],s=i[8],a=i[1],o=i[5],h=i[9],l=i[2],u=i[6],c=i[10],d=n+o+c;return d>0?(e=.5/Math.sqrt(d+1),this.set((u-h)*e,(s-l)*e,(a-r)*e,.25/e)):n>o&&n>c?(e=2*Math.sqrt(1+n-o-c),this.set(.25*e,(r+a)/e,(s+l)/e,(u-h)/e)):o>c?(e=2*Math.sqrt(1+o-n-c),this.set((r+a)/e,.25*e,(h+u)/e,(s-l)/e)):(e=2*Math.sqrt(1+c-n-o),this.set((s+l)/e,(h+u)/e,.25*e,(a-r)/e)),this},fromMat3:function(t){var e,i=t.val,n=i[0]+i[4]+i[8];if(n>0)e=Math.sqrt(n+1),this.w=.5*e,e=.5/e,this._x=(i[7]-i[5])*e,this._y=(i[2]-i[6])*e,this._z=(i[3]-i[1])*e;else{var r=0;i[4]>i[0]&&(r=1),i[8]>i[3*r+r]&&(r=2);var s=h[r],a=h[s];e=Math.sqrt(i[3*r+r]-i[3*s+s]-i[3*a+a]+1),l[r]=.5*e,e=.5/e,l[s]=(i[3*s+r]+i[3*r+s])*e,l[a]=(i[3*a+r]+i[3*r+a])*e,this._x=l[0],this._y=l[1],this._z=l[2],this._w=(i[3*a+s]-i[3*s+a])*e}return this.onChangeCallback(this),this}});t.exports=p},4208:(t,e,i)=>{var n=i(7425);t.exports=function(t){return t*n.RAD_TO_DEG}},1705:t=>{t.exports=function(t,e){void 0===e&&(e=1);var i=2*Math.random()*Math.PI;return t.x=Math.cos(i)*e,t.y=Math.sin(i)*e,t}},6650:t=>{t.exports=function(t,e){void 0===e&&(e=1);var i=2*Math.random()*Math.PI,n=2*Math.random()-1,r=Math.sqrt(1-n*n)*e;return t.x=Math.cos(i)*r,t.y=Math.sin(i)*r,t.z=n*e,t}},2037:t=>{t.exports=function(t,e){return void 0===e&&(e=1),t.x=(2*Math.random()-1)*e,t.y=(2*Math.random()-1)*e,t.z=(2*Math.random()-1)*e,t.w=(2*Math.random()-1)*e,t}},6283:t=>{t.exports=function(t,e){var i=t.x,n=t.y;return t.x=i*Math.cos(e)-n*Math.sin(e),t.y=i*Math.sin(e)+n*Math.cos(e),t}},9876:t=>{t.exports=function(t,e,i,n){var r=Math.cos(n),s=Math.sin(n),a=t.x-e,o=t.y-i;return t.x=a*r-o*s+e,t.y=a*s+o*r+i,t}},8348:t=>{t.exports=function(t,e,i,n,r){var s=n+Math.atan2(t.y-i,t.x-e);return t.x=e+r*Math.cos(s),t.y=i+r*Math.sin(s),t}},4497:t=>{t.exports=function(t,e,i,n,r){return t.x=e+r*Math.cos(n),t.y=i+r*Math.sin(n),t}},9640:(t,e,i)=>{var n=i(5689),r=i(9652),s=i(372),a=new r,o=new s,h=new n;t.exports=function(t,e,i){return o.setAxisAngle(e,i),a.fromRotationTranslation(o,h.set(0,0,0)),t.transformMat4(a)}},4078:t=>{t.exports=function(t){return t>0?Math.ceil(t):Math.floor(t)}},855:t=>{t.exports=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=10);var n=Math.pow(i,-e);return Math.round(t*n)/n}},4936:t=>{t.exports=function(t,e,i,n){void 0===e&&(e=1),void 0===i&&(i=1),void 0===n&&(n=1),n*=Math.PI/t;for(var r=[],s=[],a=0;a<t;a++)e+=(i-=e*n)*n,r[a]=i,s[a]=e;return{sin:s,cos:r,length:t}}},2733:t=>{t.exports=function(t,e,i){return t<=e?0:t>=i?1:(t=(t-e)/(i-e))*t*(3-2*t)}},278:t=>{t.exports=function(t,e,i){return(t=Math.max(0,Math.min(1,(t-e)/(i-e))))*t*t*(t*(6*t-15)+10)}},163:(t,e,i)=>{var n=i(2529);t.exports=function(t,e,i,r){void 0===r&&(r=new n);var s=0,a=0;return t>0&&t<=e*i&&(s=t>e-1?t-(a=Math.floor(t/e))*e:t),r.set(s,a)}},7556:(t,e,i)=>{var n=i(2529);t.exports=function(t,e,i,r,s,a,o,h){void 0===h&&(h=new n);var l=Math.sin(s),u=Math.cos(s),c=u*a,d=l*a,f=-l*o,p=u*o,v=1/(c*p+f*-d);return h.x=p*v*t+-f*v*e+(r*f-i*p)*v,h.y=c*v*e+-d*v*t+(-r*c+i*d)*v,h}},2529:(t,e,i)=>{var n=i(7473),r=i(12),s=new n({initialize:function(t,e){this.x=0,this.y=0,"object"==typeof t?(this.x=t.x||0,this.y=t.y||0):(void 0===e&&(e=t),this.x=t||0,this.y=e||0)},clone:function(){return new s(this.x,this.y)},copy:function(t){return this.x=t.x||0,this.y=t.y||0,this},setFromObject:function(t){return this.x=t.x||0,this.y=t.y||0,this},set:function(t,e){return void 0===e&&(e=t),this.x=t,this.y=e,this},setTo:function(t,e){return this.set(t,e)},setToPolar:function(t,e){return null==e&&(e=1),this.x=Math.cos(t)*e,this.y=Math.sin(t)*e,this},equals:function(t){return this.x===t.x&&this.y===t.y},fuzzyEquals:function(t,e){return r(this.x,t.x,e)&&r(this.y,t.y,e)},angle:function(){var t=Math.atan2(this.y,this.x);return t<0&&(t+=2*Math.PI),t},setAngle:function(t){return this.setToPolar(t,this.length())},add:function(t){return this.x+=t.x,this.y+=t.y,this},subtract:function(t){return this.x-=t.x,this.y-=t.y,this},multiply:function(t){return this.x*=t.x,this.y*=t.y,this},scale:function(t){return isFinite(t)?(this.x*=t,this.y*=t):(this.x=0,this.y=0),this},divide:function(t){return this.x/=t.x,this.y/=t.y,this},negate:function(){return this.x=-this.x,this.y=-this.y,this},distance:function(t){var e=t.x-this.x,i=t.y-this.y;return Math.sqrt(e*e+i*i)},distanceSq:function(t){var e=t.x-this.x,i=t.y-this.y;return e*e+i*i},length:function(){var t=this.x,e=this.y;return Math.sqrt(t*t+e*e)},setLength:function(t){return this.normalize().scale(t)},lengthSq:function(){var t=this.x,e=this.y;return t*t+e*e},normalize:function(){var t=this.x,e=this.y,i=t*t+e*e;return i>0&&(i=1/Math.sqrt(i),this.x=t*i,this.y=e*i),this},normalizeRightHand:function(){var t=this.x;return this.x=-1*this.y,this.y=t,this},normalizeLeftHand:function(){var t=this.x;return this.x=this.y,this.y=-1*t,this},dot:function(t){return this.x*t.x+this.y*t.y},cross:function(t){return this.x*t.y-this.y*t.x},lerp:function(t,e){void 0===e&&(e=0);var i=this.x,n=this.y;return this.x=i+e*(t.x-i),this.y=n+e*(t.y-n),this},transformMat3:function(t){var e=this.x,i=this.y,n=t.val;return this.x=n[0]*e+n[3]*i+n[6],this.y=n[1]*e+n[4]*i+n[7],this},transformMat4:function(t){var e=this.x,i=this.y,n=t.val;return this.x=n[0]*e+n[4]*i+n[12],this.y=n[1]*e+n[5]*i+n[13],this},reset:function(){return this.x=0,this.y=0,this},limit:function(t){var e=this.length();return e&&e>t&&this.scale(t/e),this},reflect:function(t){return t=t.clone().normalize(),this.subtract(t.scale(2*this.dot(t)))},mirror:function(t){return this.reflect(t).negate()},rotate:function(t){var e=Math.cos(t),i=Math.sin(t);return this.set(e*this.x-i*this.y,i*this.x+e*this.y)},project:function(t){var e=this.dot(t)/t.dot(t);return this.copy(t).scale(e)}});s.ZERO=new s,s.RIGHT=new s(1,0),s.LEFT=new s(-1,0),s.UP=new s(0,-1),s.DOWN=new s(0,1),s.ONE=new s(1,1),t.exports=s},5689:(t,e,i)=>{var n=new(i(7473))({initialize:function(t,e,i){this.x=0,this.y=0,this.z=0,"object"==typeof t?(this.x=t.x||0,this.y=t.y||0,this.z=t.z||0):(this.x=t||0,this.y=e||0,this.z=i||0)},up:function(){return this.x=0,this.y=1,this.z=0,this},min:function(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this.z=Math.min(this.z,t.z),this},max:function(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this.z=Math.max(this.z,t.z),this},clone:function(){return new n(this.x,this.y,this.z)},addVectors:function(t,e){return this.x=t.x+e.x,this.y=t.y+e.y,this.z=t.z+e.z,this},crossVectors:function(t,e){var i=t.x,n=t.y,r=t.z,s=e.x,a=e.y,o=e.z;return this.x=n*o-r*a,this.y=r*s-i*o,this.z=i*a-n*s,this},equals:function(t){return this.x===t.x&&this.y===t.y&&this.z===t.z},copy:function(t){return this.x=t.x,this.y=t.y,this.z=t.z||0,this},set:function(t,e,i){return"object"==typeof t?(this.x=t.x||0,this.y=t.y||0,this.z=t.z||0):(this.x=t||0,this.y=e||0,this.z=i||0),this},setFromMatrixPosition:function(t){return this.fromArray(t.val,12)},setFromMatrixColumn:function(t,e){return this.fromArray(t.val,4*e)},fromArray:function(t,e){return void 0===e&&(e=0),this.x=t[e],this.y=t[e+1],this.z=t[e+2],this},add:function(t){return this.x+=t.x,this.y+=t.y,this.z+=t.z||0,this},addScalar:function(t){return this.x+=t,this.y+=t,this.z+=t,this},addScale:function(t,e){return this.x+=t.x*e,this.y+=t.y*e,this.z+=t.z*e||0,this},subtract:function(t){return this.x-=t.x,this.y-=t.y,this.z-=t.z||0,this},multiply:function(t){return this.x*=t.x,this.y*=t.y,this.z*=t.z||1,this},scale:function(t){return isFinite(t)?(this.x*=t,this.y*=t,this.z*=t):(this.x=0,this.y=0,this.z=0),this},divide:function(t){return this.x/=t.x,this.y/=t.y,this.z/=t.z||1,this},negate:function(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this},distance:function(t){var e=t.x-this.x,i=t.y-this.y,n=t.z-this.z||0;return Math.sqrt(e*e+i*i+n*n)},distanceSq:function(t){var e=t.x-this.x,i=t.y-this.y,n=t.z-this.z||0;return e*e+i*i+n*n},length:function(){var t=this.x,e=this.y,i=this.z;return Math.sqrt(t*t+e*e+i*i)},lengthSq:function(){var t=this.x,e=this.y,i=this.z;return t*t+e*e+i*i},normalize:function(){var t=this.x,e=this.y,i=this.z,n=t*t+e*e+i*i;return n>0&&(n=1/Math.sqrt(n),this.x=t*n,this.y=e*n,this.z=i*n),this},dot:function(t){return this.x*t.x+this.y*t.y+this.z*t.z},cross:function(t){var e=this.x,i=this.y,n=this.z,r=t.x,s=t.y,a=t.z;return this.x=i*a-n*s,this.y=n*r-e*a,this.z=e*s-i*r,this},lerp:function(t,e){void 0===e&&(e=0);var i=this.x,n=this.y,r=this.z;return this.x=i+e*(t.x-i),this.y=n+e*(t.y-n),this.z=r+e*(t.z-r),this},applyMatrix3:function(t){var e=this.x,i=this.y,n=this.z,r=t.val;return this.x=r[0]*e+r[3]*i+r[6]*n,this.y=r[1]*e+r[4]*i+r[7]*n,this.z=r[2]*e+r[5]*i+r[8]*n,this},applyMatrix4:function(t){var e=this.x,i=this.y,n=this.z,r=t.val,s=1/(r[3]*e+r[7]*i+r[11]*n+r[15]);return this.x=(r[0]*e+r[4]*i+r[8]*n+r[12])*s,this.y=(r[1]*e+r[5]*i+r[9]*n+r[13])*s,this.z=(r[2]*e+r[6]*i+r[10]*n+r[14])*s,this},transformMat3:function(t){var e=this.x,i=this.y,n=this.z,r=t.val;return this.x=e*r[0]+i*r[3]+n*r[6],this.y=e*r[1]+i*r[4]+n*r[7],this.z=e*r[2]+i*r[5]+n*r[8],this},transformMat4:function(t){var e=this.x,i=this.y,n=this.z,r=t.val;return this.x=r[0]*e+r[4]*i+r[8]*n+r[12],this.y=r[1]*e+r[5]*i+r[9]*n+r[13],this.z=r[2]*e+r[6]*i+r[10]*n+r[14],this},transformCoordinates:function(t){var e=this.x,i=this.y,n=this.z,r=t.val,s=e*r[0]+i*r[4]+n*r[8]+r[12],a=e*r[1]+i*r[5]+n*r[9]+r[13],o=e*r[2]+i*r[6]+n*r[10]+r[14],h=e*r[3]+i*r[7]+n*r[11]+r[15];return this.x=s/h,this.y=a/h,this.z=o/h,this},transformQuat:function(t){var e=this.x,i=this.y,n=this.z,r=t.x,s=t.y,a=t.z,o=t.w,h=o*e+s*n-a*i,l=o*i+a*e-r*n,u=o*n+r*i-s*e,c=-r*e-s*i-a*n;return this.x=h*o+c*-r+l*-a-u*-s,this.y=l*o+c*-s+u*-r-h*-a,this.z=u*o+c*-a+h*-s-l*-r,this},project:function(t){var e=this.x,i=this.y,n=this.z,r=t.val,s=r[0],a=r[1],o=r[2],h=r[3],l=r[4],u=r[5],c=r[6],d=r[7],f=r[8],p=r[9],v=r[10],g=r[11],m=r[12],M=r[13],x=r[14],y=1/(e*h+i*d+n*g+r[15]);return this.x=(e*s+i*l+n*f+m)*y,this.y=(e*a+i*u+n*p+M)*y,this.z=(e*o+i*c+n*v+x)*y,this},projectViewMatrix:function(t,e){return this.applyMatrix4(t).applyMatrix4(e)},unprojectViewMatrix:function(t,e){return this.applyMatrix4(t).applyMatrix4(e)},unproject:function(t,e){var i=t.x,n=t.y,r=t.z,s=t.w,a=this.x-i,o=s-this.y-1-n,h=this.z;return this.x=2*a/r-1,this.y=2*o/s-1,this.z=2*h-1,this.project(e)},reset:function(){return this.x=0,this.y=0,this.z=0,this}});n.ZERO=new n,n.RIGHT=new n(1,0,0),n.LEFT=new n(-1,0,0),n.UP=new n(0,-1,0),n.DOWN=new n(0,1,0),n.FORWARD=new n(0,0,1),n.BACK=new n(0,0,-1),n.ONE=new n(1,1,1),t.exports=n},9279:(t,e,i)=>{var n=new(i(7473))({initialize:function(t,e,i,n){this.x=0,this.y=0,this.z=0,this.w=0,"object"==typeof t?(this.x=t.x||0,this.y=t.y||0,this.z=t.z||0,this.w=t.w||0):(this.x=t||0,this.y=e||0,this.z=i||0,this.w=n||0)},clone:function(){return new n(this.x,this.y,this.z,this.w)},copy:function(t){return this.x=t.x,this.y=t.y,this.z=t.z||0,this.w=t.w||0,this},equals:function(t){return this.x===t.x&&this.y===t.y&&this.z===t.z&&this.w===t.w},set:function(t,e,i,n){return"object"==typeof t?(this.x=t.x||0,this.y=t.y||0,this.z=t.z||0,this.w=t.w||0):(this.x=t||0,this.y=e||0,this.z=i||0,this.w=n||0),this},add:function(t){return this.x+=t.x,this.y+=t.y,this.z+=t.z||0,this.w+=t.w||0,this},subtract:function(t){return this.x-=t.x,this.y-=t.y,this.z-=t.z||0,this.w-=t.w||0,this},scale:function(t){return this.x*=t,this.y*=t,this.z*=t,this.w*=t,this},length:function(){var t=this.x,e=this.y,i=this.z,n=this.w;return Math.sqrt(t*t+e*e+i*i+n*n)},lengthSq:function(){var t=this.x,e=this.y,i=this.z,n=this.w;return t*t+e*e+i*i+n*n},normalize:function(){var t=this.x,e=this.y,i=this.z,n=this.w,r=t*t+e*e+i*i+n*n;return r>0&&(r=1/Math.sqrt(r),this.x=t*r,this.y=e*r,this.z=i*r,this.w=n*r),this},dot:function(t){return this.x*t.x+this.y*t.y+this.z*t.z+this.w*t.w},lerp:function(t,e){void 0===e&&(e=0);var i=this.x,n=this.y,r=this.z,s=this.w;return this.x=i+e*(t.x-i),this.y=n+e*(t.y-n),this.z=r+e*(t.z-r),this.w=s+e*(t.w-s),this},multiply:function(t){return this.x*=t.x,this.y*=t.y,this.z*=t.z||1,this.w*=t.w||1,this},divide:function(t){return this.x/=t.x,this.y/=t.y,this.z/=t.z||1,this.w/=t.w||1,this},distance:function(t){var e=t.x-this.x,i=t.y-this.y,n=t.z-this.z||0,r=t.w-this.w||0;return Math.sqrt(e*e+i*i+n*n+r*r)},distanceSq:function(t){var e=t.x-this.x,i=t.y-this.y,n=t.z-this.z||0,r=t.w-this.w||0;return e*e+i*i+n*n+r*r},negate:function(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this.w=-this.w,this},transformMat4:function(t){var e=this.x,i=this.y,n=this.z,r=this.w,s=t.val;return this.x=s[0]*e+s[4]*i+s[8]*n+s[12]*r,this.y=s[1]*e+s[5]*i+s[9]*n+s[13]*r,this.z=s[2]*e+s[6]*i+s[10]*n+s[14]*r,this.w=s[3]*e+s[7]*i+s[11]*n+s[15]*r,this},transformQuat:function(t){var e=this.x,i=this.y,n=this.z,r=t.x,s=t.y,a=t.z,o=t.w,h=o*e+s*n-a*i,l=o*i+a*e-r*n,u=o*n+r*i-s*e,c=-r*e-s*i-a*n;return this.x=h*o+c*-r+l*-a-u*-s,this.y=l*o+c*-s+u*-r-h*-a,this.z=u*o+c*-a+h*-s-l*-r,this},reset:function(){return this.x=0,this.y=0,this.z=0,this.w=0,this}});n.prototype.sub=n.prototype.subtract,n.prototype.mul=n.prototype.multiply,n.prototype.div=n.prototype.divide,n.prototype.dist=n.prototype.distance,n.prototype.distSq=n.prototype.distanceSq,n.prototype.len=n.prototype.length,n.prototype.lenSq=n.prototype.lengthSq,t.exports=n},4119:t=>{t.exports=function(t,e,i){return Math.abs(t-e)<=i}},8445:t=>{t.exports=function(t,e,i){var n=i-e;return e+((t-e)%n+n)%n}},6412:t=>{t.exports=function(t,e,i,n){return Math.atan2(n-e,i-t)}},760:t=>{t.exports=function(t,e){return Math.atan2(e.y-t.y,e.x-t.x)}},6909:t=>{t.exports=function(t,e){return Math.atan2(e.x-t.x,e.y-t.y)}},6947:t=>{t.exports=function(t,e,i,n){return Math.atan2(i-t,n-e)}},3426:(t,e,i)=>{var n=i(7425);t.exports=function(t){return t>Math.PI&&(t-=n.PI2),Math.abs(((t+n.TAU)%n.PI2-n.PI2)%n.PI2)}},6906:t=>{t.exports=function(t){return(t%=2*Math.PI)>=0?t:t+2*Math.PI}},3270:(t,e,i)=>{var n=i(104);t.exports=function(){return n(-Math.PI,Math.PI)}},2748:(t,e,i)=>{var n=i(104);t.exports=function(){return n(-180,180)}},936:(t,e,i)=>{var n=i(6906);t.exports=function(t){return n(t+Math.PI)}},1935:(t,e,i)=>{var n=i(7425);t.exports=function(t,e,i){return void 0===i&&(i=.05),t===e||(Math.abs(e-t)<=i||Math.abs(e-t)>=n.PI2-i?t=e:(Math.abs(e-t)>Math.PI&&(e<t?e+=n.PI2:e-=n.PI2),e>t?t+=i:e<t&&(t-=i))),t}},5393:t=>{t.exports=function(t,e){var i=e-t;return 0===i?0:i-360*Math.floor((i- -180)/360)}},3692:(t,e,i)=>{var n=i(8445);t.exports=function(t){return n(t,-Math.PI,Math.PI)}},2820:(t,e,i)=>{var n=i(8445);t.exports=function(t){return n(t,-180,180)}},1833:(t,e,i)=>{t.exports={Between:i(6412),BetweenPoints:i(760),BetweenPointsY:i(6909),BetweenY:i(6947),CounterClockwise:i(3426),Normalize:i(6906),Random:i(3270),RandomDegrees:i(2748),Reverse:i(936),RotateTo:i(1935),ShortestBetween:i(5393),Wrap:i(3692),WrapDegrees:i(2820)}},7425:t=>{var e={PI2:2*Math.PI,TAU:.5*Math.PI,EPSILON:1e-6,DEG_TO_RAD:Math.PI/180,RAD_TO_DEG:180/Math.PI,RND:null,MIN_SAFE_INTEGER:Number.MIN_SAFE_INTEGER||-9007199254740991,MAX_SAFE_INTEGER:Number.MAX_SAFE_INTEGER||9007199254740991};t.exports=e},1518:t=>{t.exports=function(t,e,i,n){var r=t-i,s=e-n;return Math.sqrt(r*r+s*s)}},5372:t=>{t.exports=function(t,e){var i=t.x-e.x,n=t.y-e.y;return Math.sqrt(i*i+n*n)}},4430:t=>{t.exports=function(t,e){var i=t.x-e.x,n=t.y-e.y;return i*i+n*n}},4361:t=>{t.exports=function(t,e,i,n){return Math.max(Math.abs(t-i),Math.abs(e-n))}},7798:t=>{t.exports=function(t,e,i,n,r){return void 0===r&&(r=2),Math.sqrt(Math.pow(i-t,r)+Math.pow(n-e,r))}},8290:t=>{t.exports=function(t,e,i,n){return Math.abs(t-i)+Math.abs(e-n)}},3788:t=>{t.exports=function(t,e,i,n){var r=t-i,s=e-n;return r*r+s*s}},6338:(t,e,i)=>{t.exports={Between:i(1518),BetweenPoints:i(5372),BetweenPointsSquared:i(4430),Chebyshev:i(4361),Power:i(7798),Snake:i(8290),Squared:i(3788)}},5751:t=>{t.exports=function(t,e){return void 0===e&&(e=1.70158),t*t*((e+1)*t-e)}},6203:t=>{t.exports=function(t,e){void 0===e&&(e=1.70158);var i=1.525*e;return(t*=2)<1?t*t*((i+1)*t-i)*.5:.5*((t-=2)*t*((i+1)*t+i)+2)}},9103:t=>{t.exports=function(t,e){return void 0===e&&(e=1.70158),--t*t*((e+1)*t+e)+1}},4938:(t,e,i)=>{t.exports={In:i(5751),Out:i(9103),InOut:i(6203)}},8677:t=>{t.exports=function(t){return(t=1-t)<1/2.75?1-7.5625*t*t:t<2/2.75?1-(7.5625*(t-=1.5/2.75)*t+.75):t<2.5/2.75?1-(7.5625*(t-=2.25/2.75)*t+.9375):1-(7.5625*(t-=2.625/2.75)*t+.984375)}},4649:t=>{t.exports=function(t){var e=!1;return t<.5?(t=1-2*t,e=!0):t=2*t-1,t<1/2.75?t*=7.5625*t:t=t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375,e?.5*(1-t):.5*t+.5}},504:t=>{t.exports=function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375}},8872:(t,e,i)=>{t.exports={In:i(8677),Out:i(504),InOut:i(4649)}},3170:t=>{t.exports=function(t){return 1-Math.sqrt(1-t*t)}},2627:t=>{t.exports=function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)}},1349:t=>{t.exports=function(t){return Math.sqrt(1- --t*t)}},5006:(t,e,i)=>{t.exports={In:i(3170),Out:i(1349),InOut:i(2627)}},6046:t=>{t.exports=function(t){return t*t*t}},9531:t=>{t.exports=function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)}},4836:t=>{t.exports=function(t){return--t*t*t+1}},875:(t,e,i)=>{t.exports={In:i(6046),Out:i(4836),InOut:i(9531)}},7619:t=>{t.exports=function(t,e,i){if(void 0===e&&(e=.1),void 0===i&&(i=.1),0===t)return 0;if(1===t)return 1;var n=i/4;return e<1?e=1:n=i*Math.asin(1/e)/(2*Math.PI),-e*Math.pow(2,10*(t-=1))*Math.sin((t-n)*(2*Math.PI)/i)}},7437:t=>{t.exports=function(t,e,i){if(void 0===e&&(e=.1),void 0===i&&(i=.1),0===t)return 0;if(1===t)return 1;var n=i/4;return e<1?e=1:n=i*Math.asin(1/e)/(2*Math.PI),(t*=2)<1?e*Math.pow(2,10*(t-=1))*Math.sin((t-n)*(2*Math.PI)/i)*-.5:e*Math.pow(2,-10*(t-=1))*Math.sin((t-n)*(2*Math.PI)/i)*.5+1}},8119:t=>{t.exports=function(t,e,i){if(void 0===e&&(e=.1),void 0===i&&(i=.1),0===t)return 0;if(1===t)return 1;var n=i/4;return e<1?e=1:n=i*Math.asin(1/e)/(2*Math.PI),e*Math.pow(2,-10*t)*Math.sin((t-n)*(2*Math.PI)/i)+1}},2884:(t,e,i)=>{t.exports={In:i(7619),Out:i(8119),InOut:i(7437)}},5456:t=>{t.exports=function(t){return Math.pow(2,10*(t-1))-.001}},3461:t=>{t.exports=function(t){return(t*=2)<1?.5*Math.pow(2,10*(t-1)):.5*(2-Math.pow(2,-10*(t-1)))}},2711:t=>{t.exports=function(t){return 1-Math.pow(2,-10*t)}},6287:(t,e,i)=>{t.exports={In:i(5456),Out:i(2711),InOut:i(3461)}},8613:(t,e,i)=>{t.exports={Back:i(4938),Bounce:i(8872),Circular:i(5006),Cubic:i(875),Elastic:i(2884),Expo:i(6287),Linear:i(4233),Quadratic:i(6341),Quartic:i(762),Quintic:i(345),Sine:i(8698),Stepped:i(7051)}},744:t=>{t.exports=function(t){return t}},4233:(t,e,i)=>{t.exports=i(744)},9810:t=>{t.exports=function(t){return t*t}},8163:t=>{t.exports=function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)}},6123:t=>{t.exports=function(t){return t*(2-t)}},6341:(t,e,i)=>{t.exports={In:i(9810),Out:i(6123),InOut:i(8163)}},7337:t=>{t.exports=function(t){return t*t*t*t}},4878:t=>{t.exports=function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)}},9012:t=>{t.exports=function(t){return 1- --t*t*t*t}},762:(t,e,i)=>{t.exports={In:i(7337),Out:i(9012),InOut:i(4878)}},303:t=>{t.exports=function(t){return t*t*t*t*t}},553:t=>{t.exports=function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)}},1632:t=>{t.exports=function(t){return--t*t*t*t*t+1}},345:(t,e,i)=>{t.exports={In:i(303),Out:i(1632),InOut:i(553)}},8455:t=>{t.exports=function(t){return 0===t?0:1===t?1:1-Math.cos(t*Math.PI/2)}},1844:t=>{t.exports=function(t){return 0===t?0:1===t?1:.5*(1-Math.cos(Math.PI*t))}},990:t=>{t.exports=function(t){return 0===t?0:1===t?1:Math.sin(t*Math.PI/2)}},8698:(t,e,i)=>{t.exports={In:i(8455),Out:i(990),InOut:i(1844)}},6745:t=>{t.exports=function(t,e){return void 0===e&&(e=1),t<=0?0:t>=1?1:1/e*(1+(e*t|0))}},7051:(t,e,i)=>{t.exports=i(6745)},3158:t=>{t.exports=function(t,e){return void 0===e&&(e=1e-4),Math.ceil(t-e)}},12:t=>{t.exports=function(t,e,i){return void 0===i&&(i=1e-4),Math.abs(t-e)<i}},1326:t=>{t.exports=function(t,e){return void 0===e&&(e=1e-4),Math.floor(t+e)}},7373:t=>{t.exports=function(t,e,i){return void 0===i&&(i=1e-4),t>e-i}},2622:t=>{t.exports=function(t,e,i){return void 0===i&&(i=1e-4),t<e+i}},7927:(t,e,i)=>{t.exports={Ceil:i(3158),Equal:i(12),Floor:i(1326),GreaterThan:i(7373),LessThan:i(2622)}},4675:(t,e,i)=>{var n=i(7425),r=i(1030),s={Angle:i(1833),Distance:i(6338),Easing:i(8613),Fuzzy:i(7927),Interpolation:i(2140),Pow2:i(7897),Snap:i(3943),RandomDataGenerator:i(6957),Average:i(3136),Bernstein:i(785),Between:i(7025),CatmullRom:i(48),CeilTo:i(5035),Clamp:i(2915),DegToRad:i(7149),Difference:i(2975),Euler:i(2107),Factorial:i(3916),FloatBetween:i(104),FloorTo:i(4941),FromPercent:i(1555),GetSpeed:i(5005),IsEven:i(3702),IsEvenStrict:i(8820),Linear:i(1743),LinearXY:i(3416),MaxAdd:i(3733),Median:i(44),MinSub:i(5385),Percent:i(8585),RadToDeg:i(4208),RandomXY:i(1705),RandomXYZ:i(6650),RandomXYZW:i(2037),Rotate:i(6283),RotateAround:i(9876),RotateAroundDistance:i(8348),RotateTo:i(4497),RoundAwayFromZero:i(4078),RoundTo:i(855),SinCosTableGenerator:i(4936),SmootherStep:i(278),SmoothStep:i(2733),ToXY:i(163),TransformXY:i(7556),Within:i(4119),Wrap:i(8445),Vector2:i(2529),Vector3:i(5689),Vector4:i(9279),Matrix3:i(2149),Matrix4:i(9652),Quaternion:i(372),RotateVec3:i(9640)};s=r(!1,s,n),t.exports=s},1640:(t,e,i)=>{var n=i(785);t.exports=function(t,e){for(var i=0,r=t.length-1,s=0;s<=r;s++)i+=Math.pow(1-e,r-s)*Math.pow(e,s)*t[s]*n(r,s);return i}},6105:(t,e,i)=>{var n=i(48);t.exports=function(t,e){var i=t.length-1,r=i*e,s=Math.floor(r);return t[0]===t[i]?(e<0&&(s=Math.floor(r=i*(1+e))),n(r-s,t[(s-1+i)%i],t[s],t[(s+1)%i],t[(s+2)%i])):e<0?t[0]-(n(-r,t[0],t[0],t[1],t[1])-t[0]):e>1?t[i]-(n(r-i,t[i],t[i],t[i-1],t[i-1])-t[i]):n(r-s,t[s?s-1:0],t[s],t[i<s+1?i:s+1],t[i<s+2?i:s+2])}},4002:t=>{t.exports=function(t,e,i,n,r){return function(t,e){var i=1-t;return i*i*i*e}(t,e)+function(t,e){var i=1-t;return 3*i*i*t*e}(t,i)+function(t,e){return 3*(1-t)*t*t*e}(t,n)+function(t,e){return t*t*t*e}(t,r)}},6765:(t,e,i)=>{var n=i(1743);t.exports=function(t,e){var i=t.length-1,r=i*e,s=Math.floor(r);return e<0?n(t[0],t[1],r):e>1?n(t[i],t[i-1],i-r):n(t[s],t[s+1>i?i:s+1],r-s)}},6388:t=>{t.exports=function(t,e,i,n){return function(t,e){var i=1-t;return i*i*e}(t,e)+function(t,e){return 2*(1-t)*t*e}(t,i)+function(t,e){return t*t*e}(t,n)}},5735:(t,e,i)=>{var n=i(2733);t.exports=function(t,e,i){return e+(i-e)*n(t,0,1)}},8705:(t,e,i)=>{var n=i(278);t.exports=function(t,e,i){return e+(i-e)*n(t,0,1)}},2140:(t,e,i)=>{t.exports={Bezier:i(1640),CatmullRom:i(6105),CubicBezier:i(4002),Linear:i(6765),QuadraticBezier:i(6388),SmoothStep:i(5735),SmootherStep:i(8705)}},5443:t=>{t.exports=function(t){var e=Math.log(t)/.6931471805599453;return 1<<Math.ceil(e)}},725:t=>{t.exports=function(t,e){return t>0&&0==(t&t-1)&&e>0&&0==(e&e-1)}},167:t=>{t.exports=function(t){return t>0&&0==(t&t-1)}},7897:(t,e,i)=>{t.exports={GetNext:i(5443),IsSize:i(725),IsValue:i(167)}},6957:(t,e,i)=>{var n=new(i(7473))({initialize:function(t){void 0===t&&(t=[(Date.now()*Math.random()).toString()]),this.c=1,this.s0=0,this.s1=0,this.s2=0,this.n=0,this.signs=[-1,1],t&&this.init(t)},rnd:function(){var t=2091639*this.s0+2.3283064365386963e-10*this.c;return this.c=0|t,this.s0=this.s1,this.s1=this.s2,this.s2=t-this.c,this.s2},hash:function(t){var e,i=this.n;t=t.toString();for(var n=0;n<t.length;n++)e=.02519603282416938*(i+=t.charCodeAt(n)),e-=i=e>>>0,i=(e*=i)>>>0,i+=4294967296*(e-=i);return this.n=i,2.3283064365386963e-10*(i>>>0)},init:function(t){"string"==typeof t?this.state(t):this.sow(t)},sow:function(t){if(this.n=4022871197,this.s0=this.hash(" "),this.s1=this.hash(" "),this.s2=this.hash(" "),this.c=1,t)for(var e=0;e<t.length&&null!=t[e];e++){var i=t[e];this.s0-=this.hash(i),this.s0+=~~(this.s0<0),this.s1-=this.hash(i),this.s1+=~~(this.s1<0),this.s2-=this.hash(i),this.s2+=~~(this.s2<0)}},integer:function(){return 4294967296*this.rnd()},frac:function(){return this.rnd()+11102230246251565e-32*(2097152*this.rnd()|0)},real:function(){return this.integer()+this.frac()},integerInRange:function(t,e){return Math.floor(this.realInRange(0,e-t+1)+t)},between:function(t,e){return Math.floor(this.realInRange(0,e-t+1)+t)},realInRange:function(t,e){return this.frac()*(e-t)+t},normal:function(){return 1-2*this.frac()},uuid:function(){var t="",e="";for(e=t="";t++<36;e+=~t%5|3*t&4?(15^t?8^this.frac()*(20^t?16:4):4).toString(16):"-");return e},pick:function(t){return t[this.integerInRange(0,t.length-1)]},sign:function(){return this.pick(this.signs)},weightedPick:function(t){return t[~~(Math.pow(this.frac(),2)*t.length+.5)]},timestamp:function(t,e){return this.realInRange(t||9466848e5,e||1577862e6)},angle:function(){return this.integerInRange(-180,180)},rotation:function(){return this.realInRange(-3.1415926,3.1415926)},state:function(t){return"string"==typeof t&&t.match(/^!rnd/)&&(t=t.split(","),this.c=parseFloat(t[1]),this.s0=parseFloat(t[2]),this.s1=parseFloat(t[3]),this.s2=parseFloat(t[4])),["!rnd",this.c,this.s0,this.s1,this.s2].join(",")},shuffle:function(t){for(var e=t.length-1;e>0;e--){var i=Math.floor(this.frac()*(e+1)),n=t[i];t[i]=t[e],t[e]=n}return t}});t.exports=n},5659:t=>{t.exports=function(t,e,i,n){return void 0===i&&(i=0),0===e?t:(t-=i,t=e*Math.ceil(t/e),n?(i+t)/e:i+t)}},5461:t=>{t.exports=function(t,e,i,n){return void 0===i&&(i=0),0===e?t:(t-=i,t=e*Math.floor(t/e),n?(i+t)/e:i+t)}},5131:t=>{t.exports=function(t,e,i,n){return void 0===i&&(i=0),0===e?t:(t-=i,t=e*Math.round(t/e),n?(i+t)/e:i+t)}},3943:(t,e,i)=>{t.exports={Ceil:i(5659),Floor:i(5461),To:i(5131)}},8666:(t,e,i)=>{var n=new(i(7473))({initialize:function(t){this.pluginManager=t,this.game=t.game},init:function(){},start:function(){},stop:function(){},destroy:function(){this.pluginManager=null,this.game=null,this.scene=null,this.systems=null}});t.exports=n},8456:t=>{var e={},i={},n={register:function(t,i,n,r){void 0===r&&(r=!1),e[t]={plugin:i,mapping:n,custom:r}},registerCustom:function(t,e,n,r){i[t]={plugin:e,mapping:n,data:r}},hasCore:function(t){return e.hasOwnProperty(t)},hasCustom:function(t){return i.hasOwnProperty(t)},getCore:function(t){return e[t]},getCustom:function(t){return i[t]},getCustomClass:function(t){return i.hasOwnProperty(t)?i[t].plugin:null},remove:function(t){e.hasOwnProperty(t)&&delete e[t]},removeCustom:function(t){i.hasOwnProperty(t)&&delete i[t]},destroyCorePlugins:function(){for(var t in e)e.hasOwnProperty(t)&&delete e[t]},destroyCustomPlugins:function(){for(var t in i)i.hasOwnProperty(t)&&delete i[t]}};t.exports=n},5722:(t,e,i)=>{var n=i(8666),r=i(7473),s=i(204),a=new r({Extends:n,initialize:function(t,e,i){n.call(this,e),this.scene=t,this.systems=t.sys,this.pluginKey=i,t.sys.events.once(s.BOOT,this.boot,this)},boot:function(){},destroy:function(){this.pluginManager=null,this.game=null,this.scene=null,this.systems=null}});t.exports=a},8351:t=>{t.exports={SKIP_CHECK:-1,NORMAL:0,ADD:1,MULTIPLY:2,SCREEN:3,OVERLAY:4,DARKEN:5,LIGHTEN:6,COLOR_DODGE:7,COLOR_BURN:8,HARD_LIGHT:9,SOFT_LIGHT:10,DIFFERENCE:11,EXCLUSION:12,HUE:13,SATURATION:14,COLOR:15,LUMINOSITY:16,ERASE:17,SOURCE_IN:18,SOURCE_OUT:19,SOURCE_ATOP:20,DESTINATION_OVER:21,DESTINATION_IN:22,DESTINATION_OUT:23,DESTINATION_ATOP:24,LIGHTER:25,COPY:26,XOR:27}},8196:t=>{t.exports={DEFAULT:0,LINEAR:0,NEAREST:1}},3527:t=>{t.exports="resize"},8618:t=>{t.exports="addedtoscene"},4328:t=>{t.exports="boot"},6099:t=>{t.exports="create"},7645:t=>{t.exports="destroy"},2710:t=>{t.exports="pause"},2547:t=>{t.exports="postupdate"},8577:t=>{t.exports="prerender"},8197:t=>{t.exports="preupdate"},8997:t=>{t.exports="ready"},7604:t=>{t.exports="removedfromscene"},8999:t=>{t.exports="render"},9742:t=>{t.exports="resume"},3667:t=>{t.exports="shutdown"},3468:t=>{t.exports="sleep"},7840:t=>{t.exports="start"},9896:t=>{t.exports="transitioncomplete"},5103:t=>{t.exports="transitioninit"},3162:t=>{t.exports="transitionout"},7841:t=>{t.exports="transitionstart"},6454:t=>{t.exports="transitionwake"},6536:t=>{t.exports="update"},3875:t=>{t.exports="wake"},204:(t,e,i)=>{t.exports={ADDED_TO_SCENE:i(8618),BOOT:i(4328),CREATE:i(6099),DESTROY:i(7645),PAUSE:i(2710),POST_UPDATE:i(2547),PRE_RENDER:i(8577),PRE_UPDATE:i(8197),READY:i(8997),REMOVED_FROM_SCENE:i(7604),RENDER:i(8999),RESUME:i(9742),SHUTDOWN:i(3667),SLEEP:i(3468),START:i(7840),TRANSITION_COMPLETE:i(9896),TRANSITION_INIT:i(5103),TRANSITION_OUT:i(3162),TRANSITION_START:i(7841),TRANSITION_WAKE:i(6454),UPDATE:i(6536),WAKE:i(3875)}},2362:(t,e,i)=>{var n=i(7473),r=i(2915),s=i(1030),a=new n({initialize:function(t,e,i,n,r,s,a){this.texture=t,this.name=e,this.source=t.source[i],this.sourceIndex=i,this.glTexture=this.source.glTexture,this.cutX,this.cutY,this.cutWidth,this.cutHeight,this.x=0,this.y=0,this.width,this.height,this.halfWidth,this.halfHeight,this.centerX,this.centerY,this.pivotX=0,this.pivotY=0,this.customPivot=!1,this.rotated=!1,this.autoRound=-1,this.customData={},this.u0=0,this.v0=0,this.u1=0,this.v1=0,this.data={cut:{x:0,y:0,w:0,h:0,r:0,b:0},trim:!1,sourceSize:{w:0,h:0},spriteSourceSize:{x:0,y:0,w:0,h:0,r:0,b:0},radius:0,drawImage:{x:0,y:0,width:0,height:0},is3Slice:!1,scale9:!1,scale9Borders:{x:0,y:0,w:0,h:0}},this.setSize(s,a,n,r)},setSize:function(t,e,i,n){void 0===i&&(i=0),void 0===n&&(n=0),this.cutX=i,this.cutY=n,this.cutWidth=t,this.cutHeight=e,this.width=t,this.height=e,this.halfWidth=Math.floor(.5*t),this.halfHeight=Math.floor(.5*e),this.centerX=Math.floor(t/2),this.centerY=Math.floor(e/2);var r=this.data,s=r.cut;s.x=i,s.y=n,s.w=t,s.h=e,s.r=i+t,s.b=n+e,r.sourceSize.w=t,r.sourceSize.h=e,r.spriteSourceSize.w=t,r.spriteSourceSize.h=e,r.radius=.5*Math.sqrt(t*t+e*e);var a=r.drawImage;return a.x=i,a.y=n,a.width=t,a.height=e,this.updateUVs()},setTrim:function(t,e,i,n,r,s){var a=this.data,o=a.spriteSourceSize;return a.trim=!0,a.sourceSize.w=t,a.sourceSize.h=e,o.x=i,o.y=n,o.w=r,o.h=s,o.r=i+r,o.b=n+s,this.x=i,this.y=n,this.width=r,this.height=s,this.halfWidth=.5*r,this.halfHeight=.5*s,this.centerX=Math.floor(r/2),this.centerY=Math.floor(s/2),this.updateUVs()},setScale9:function(t,e,i,n){var r=this.data;return r.scale9=!0,r.is3Slice=0===e&&n===this.height,r.scale9Borders.x=t,r.scale9Borders.y=e,r.scale9Borders.w=i,r.scale9Borders.h=n,this},setCropUVs:function(t,e,i,n,s,a,o){var h=this.cutX,l=this.cutY,u=this.cutWidth,c=this.cutHeight,d=this.realWidth,f=this.realHeight,p=h+(e=r(e,0,d)),v=l+(i=r(i,0,f)),g=n=r(n,0,d-e),m=s=r(s,0,f-i),M=this.data;if(M.trim){var x=M.spriteSourceSize,y=e+(n=r(n,0,u-e)),w=i+(s=r(s,0,c-i));if(!(x.r<e||x.b<i||x.x>y||x.y>w)){var E=Math.max(x.x,e),b=Math.max(x.y,i),T=Math.min(x.r,y)-E,A=Math.min(x.b,w)-b;g=T,m=A,p=a?h+(u-(E-x.x)-T):h+(E-x.x),v=o?l+(c-(b-x.y)-A):l+(b-x.y),e=E,i=b,n=T,s=A}else p=0,v=0,g=0,m=0}else a&&(p=h+(u-e-n)),o&&(v=l+(c-i-s));var S=this.source.width,R=this.source.height;return t.u0=Math.max(0,p/S),t.v0=Math.max(0,v/R),t.u1=Math.min(1,(p+g)/S),t.v1=Math.min(1,(v+m)/R),t.x=e,t.y=i,t.cx=p,t.cy=v,t.cw=g,t.ch=m,t.width=n,t.height=s,t.flipX=a,t.flipY=o,t},updateCropUVs:function(t,e,i){return this.setCropUVs(t,t.x,t.y,t.width,t.height,e,i)},setUVs:function(t,e,i,n,r,s){var a=this.data.drawImage;return a.width=t,a.height=e,this.u0=i,this.v0=n,this.u1=r,this.v1=s,this},updateUVs:function(){var t=this.cutX,e=this.cutY,i=this.cutWidth,n=this.cutHeight,r=this.data.drawImage;r.width=i,r.height=n;var s=this.source.width,a=this.source.height;return this.u0=t/s,this.v0=e/a,this.u1=(t+i)/s,this.v1=(e+n)/a,this},updateUVsInverted:function(){var t=this.source.width,e=this.source.height;return this.u0=(this.cutX+this.cutHeight)/t,this.v0=this.cutY/e,this.u1=this.cutX/t,this.v1=(this.cutY+this.cutWidth)/e,this},clone:function(){var t=new a(this.texture,this.name,this.sourceIndex);return t.cutX=this.cutX,t.cutY=this.cutY,t.cutWidth=this.cutWidth,t.cutHeight=this.cutHeight,t.x=this.x,t.y=this.y,t.width=this.width,t.height=this.height,t.halfWidth=this.halfWidth,t.halfHeight=this.halfHeight,t.centerX=this.centerX,t.centerY=this.centerY,t.rotated=this.rotated,t.data=s(!0,t.data,this.data),t.updateUVs(),t},destroy:function(){this.texture=null,this.source=null,this.glTexture=null,this.customData=null,this.data=null},realWidth:{get:function(){return this.data.sourceSize.w}},realHeight:{get:function(){return this.data.sourceSize.h}},radius:{get:function(){return this.data.radius}},trimmed:{get:function(){return this.data.trim}},scale9:{get:function(){return this.data.scale9}},is3Slice:{get:function(){return this.data.is3Slice}},canvasData:{get:function(){return this.data.drawImage}}});t.exports=a},1864:t=>{t.exports=function(t,e,i){return t&&t.hasOwnProperty(e)?t[e]:i}},3747:t=>{t.exports={CREATED:0,DELAY:2,PENDING_RENDER:4,PLAYING_FORWARD:5,PLAYING_BACKWARD:6,HOLD_DELAY:7,REPEAT_DELAY:8,COMPLETE:9,PENDING:20,ACTIVE:21,LOOP_DELAY:22,COMPLETE_DELAY:23,START_DELAY:24,PENDING_REMOVE:25,REMOVED:26,FINISHED:27,DESTROYED:28,MAX:999999999999}},7473:t=>{function e(t,e,i){var n=i?t[e]:Object.getOwnPropertyDescriptor(t,e);return!i&&n.value&&"object"==typeof n.value&&(n=n.value),!(!n||!function(t){return!!t.get&&"function"==typeof t.get||!!t.set&&"function"==typeof t.set}(n))&&(void 0===n.enumerable&&(n.enumerable=!0),void 0===n.configurable&&(n.configurable=!0),n)}function i(t,e){var i=Object.getOwnPropertyDescriptor(t,e);return!!i&&(i.value&&"object"==typeof i.value&&(i=i.value),!1===i.configurable)}function n(t,n,r,a){for(var o in n)if(n.hasOwnProperty(o)){var h=e(n,o,r);if(!1!==h){if(i((a||t).prototype,o)){if(s.ignoreFinals)continue;throw new Error("cannot override final property '"+o+"', set Class.ignoreFinals = true to skip")}Object.defineProperty(t.prototype,o,h)}else t.prototype[o]=n[o]}}function r(t,e){if(e){Array.isArray(e)||(e=[e]);for(var i=0;i<e.length;i++)n(t,e[i].prototype||e[i])}}function s(t){var e,i;if(t||(t={}),t.initialize){if("function"!=typeof t.initialize)throw new Error("initialize must be a function");e=t.initialize,delete t.initialize}else if(t.Extends){var s=t.Extends;e=function(){s.apply(this,arguments)}}else e=function(){};t.Extends?(e.prototype=Object.create(t.Extends.prototype),e.prototype.constructor=e,i=t.Extends,delete t.Extends):e.prototype.constructor=e;var a=null;return t.Mixins&&(a=t.Mixins,delete t.Mixins),r(e,a),n(e,t,!0,i),e}s.extend=n,s.mixin=r,s.ignoreFinals=!1,t.exports=s},1984:t=>{t.exports=function(){}},1792:t=>{t.exports=function(t,e,i,n,r){if(void 0===r&&(r=t),i>0){var s=i-t.length;if(s<=0)return null}if(!Array.isArray(e))return-1===t.indexOf(e)?(t.push(e),n&&n.call(r,e),e):null;for(var a=e.length-1;a>=0;)-1!==t.indexOf(e[a])&&e.splice(a,1),a--;if(0===(a=e.length))return null;i>0&&a>s&&(e.splice(s),a=s);for(var o=0;o<a;o++){var h=e[o];t.push(h),n&&n.call(r,h)}return e}},2280:t=>{t.exports=function(t,e,i,n,r,s){if(void 0===i&&(i=0),void 0===s&&(s=t),n>0){var a=n-t.length;if(a<=0)return null}if(!Array.isArray(e))return-1===t.indexOf(e)?(t.splice(i,0,e),r&&r.call(s,e),e):null;for(var o=e.length-1;o>=0;)-1!==t.indexOf(e[o])&&e.pop(),o--;if(0===(o=e.length))return null;n>0&&o>a&&(e.splice(a),o=a);for(var h=o-1;h>=0;h--){var l=e[h];t.splice(i,0,l),r&&r.call(s,l)}return e}},2513:t=>{t.exports=function(t,e){var i=t.indexOf(e);return-1!==i&&i<t.length&&(t.splice(i,1),t.push(e)),e}},1771:(t,e,i)=>{var n=i(2497);t.exports=function(t,e,i,r,s){void 0===r&&(r=0),void 0===s&&(s=t.length);var a=0;if(n(t,r,s))for(var o=r;o<s;o++){t[o][e]===i&&a++}return a}},7883:t=>{t.exports=function(t,e,i){var n,r=[null];for(n=3;n<arguments.length;n++)r.push(arguments[n]);for(n=0;n<t.length;n++)r[0]=t[n],e.apply(i,r);return t}},5856:(t,e,i)=>{var n=i(2497);t.exports=function(t,e,i,r,s){if(void 0===r&&(r=0),void 0===s&&(s=t.length),n(t,r,s)){var a,o=[null];for(a=5;a<arguments.length;a++)o.push(arguments[a]);for(a=r;a<s;a++)o[0]=t[a],e.apply(i,o)}return t}},3957:t=>{t.exports=function(t,e,i){if(!e.length)return NaN;if(1===e.length)return e[0];var n,r,s=1;if(i){if(t<e[0][i])return e[0];for(;e[s][i]<t;)s++}else for(;e[s]<t;)s++;return s>e.length&&(s=e.length),i?(n=e[s-1][i],(r=e[s][i])-t<=t-n?e[s]:e[s-1]):(n=e[s-1],(r=e[s])-t<=t-n?r:n)}},4493:t=>{var e=function(t,i){void 0===i&&(i=[]);for(var n=0;n<t.length;n++)Array.isArray(t[n])?e(t[n],i):i.push(t[n]);return i};t.exports=e},6245:(t,e,i)=>{var n=i(2497);t.exports=function(t,e,i,r,s){void 0===r&&(r=0),void 0===s&&(s=t.length);var a=[];if(n(t,r,s))for(var o=r;o<s;o++){var h=t[o];(!e||e&&void 0===i&&h.hasOwnProperty(e)||e&&void 0!==i&&h[e]===i)&&a.push(h)}return a}},1647:(t,e,i)=>{var n=i(2497);t.exports=function(t,e,i,r,s){if(void 0===r&&(r=0),void 0===s&&(s=t.length),n(t,r,s))for(var a=r;a<s;a++){var o=t[a];if(!e||e&&void 0===i&&o.hasOwnProperty(e)||e&&void 0!==i&&o[e]===i)return o}return null}},5301:t=>{t.exports=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=t.length);var n=e+Math.floor(Math.random()*i);return void 0===t[n]?null:t[n]}},8683:t=>{t.exports=function(t,e,i){if(e===i)return t;var n=t.indexOf(e),r=t.indexOf(i);if(n<0||r<0)throw new Error("Supplied items must be elements of the same array");return n>r||(t.splice(n,1),r===t.length-1?t.push(e):t.splice(r,0,e)),t}},546:t=>{t.exports=function(t,e,i){if(e===i)return t;var n=t.indexOf(e),r=t.indexOf(i);if(n<0||r<0)throw new Error("Supplied items must be elements of the same array");return n<r||(t.splice(n,1),0===r?t.unshift(e):t.splice(r,0,e)),t}},1842:t=>{t.exports=function(t,e){var i=t.indexOf(e);if(i>0){var n=t[i-1],r=t.indexOf(n);t[i]=n,t[r]=e}return t}},1419:t=>{t.exports=function(t,e,i){var n=t.indexOf(e);if(-1===n||i<0||i>=t.length)throw new Error("Supplied index out of bounds");return n!==i&&(t.splice(n,1),t.splice(i,0,e)),e}},6512:t=>{t.exports=function(t,e){var i=t.indexOf(e);if(-1!==i&&i<t.length-1){var n=t[i+1],r=t.indexOf(n);t[i]=n,t[r]=e}return t}},4130:t=>{t.exports=function(t,e,i,n){var r,s=[],a=!1;if((i||n)&&(a=!0,i||(i=""),n||(n="")),e<t)for(r=t;r>=e;r--)a?s.push(i+r.toString()+n):s.push(r);else for(r=t;r<=e;r++)a?s.push(i+r.toString()+n):s.push(r);return s}},1316:(t,e,i)=>{var n=i(4078);t.exports=function(t,e,i){void 0===t&&(t=0),void 0===e&&(e=null),void 0===i&&(i=1),null===e&&(e=t,t=0);for(var r=[],s=Math.max(n((e-t)/(i||1)),0),a=0;a<s;a++)r.push(t),t+=i;return r}},9465:t=>{function e(t,e,i){var n=t[e];t[e]=t[i],t[i]=n}function i(t,e){return t<e?-1:t>e?1:0}var n=function(t,r,s,a,o){for(void 0===s&&(s=0),void 0===a&&(a=t.length-1),void 0===o&&(o=i);a>s;){if(a-s>600){var h=a-s+1,l=r-s+1,u=Math.log(h),c=.5*Math.exp(2*u/3),d=.5*Math.sqrt(u*c*(h-c)/h)*(l-h/2<0?-1:1),f=Math.max(s,Math.floor(r-l*c/h+d)),p=Math.min(a,Math.floor(r+(h-l)*c/h+d));n(t,r,f,p,o)}var v=t[r],g=s,m=a;for(e(t,s,r),o(t[a],v)>0&&e(t,s,a);g<m;){for(e(t,g,m),g++,m--;o(t[g],v)<0;)g++;for(;o(t[m],v)>0;)m--}0===o(t[s],v)?e(t,s,m):e(t,++m,a),m<=r&&(s=m+1),r<=m&&(a=m-1)}};t.exports=n},9703:(t,e,i)=>{var n=i(5851),r=i(4912),s=function(t,e,i){for(var n=[],r=0;r<t.length;r++)for(var s=0;s<e.length;s++)for(var a=0;a<i;a++)n.push({a:t[r],b:e[s]});return n};t.exports=function(t,e,i){var a=n(i,"max",0),o=n(i,"qty",1),h=n(i,"random",!1),l=n(i,"randomB",!1),u=n(i,"repeat",0),c=n(i,"yoyo",!1),d=[];if(l&&r(e),-1===u)if(0===a)u=0;else{var f=t.length*e.length*o;c&&(f*=2),u=Math.ceil(a/f)}for(var p=0;p<=u;p++){var v=s(t,e,o);h&&r(v),d=d.concat(v),c&&(v.reverse(),d=d.concat(v))}return a&&d.splice(a),d}},7161:(t,e,i)=>{var n=i(8935);t.exports=function(t,e,i,r){var s;if(void 0===r&&(r=t),!Array.isArray(e))return-1!==(s=t.indexOf(e))?(n(t,s),i&&i.call(r,e),e):null;for(var a=e.length-1,o=[];a>=0;){var h=e[a];-1!==(s=t.indexOf(h))&&(n(t,s),o.push(h),i&&i.call(r,h)),a--}return o}},4725:(t,e,i)=>{var n=i(8935);t.exports=function(t,e,i,r){if(void 0===r&&(r=t),e<0||e>t.length-1)throw new Error("Index out of bounds");var s=n(t,e);return i&&i.call(r,s),s}},8780:(t,e,i)=>{var n=i(2497);t.exports=function(t,e,i,r,s){if(void 0===e&&(e=0),void 0===i&&(i=t.length),void 0===s&&(s=t),n(t,e,i)){var a=i-e,o=t.splice(e,a);if(r)for(var h=0;h<o.length;h++){var l=o[h];r.call(s,l)}return o}return[]}},5744:(t,e,i)=>{var n=i(8935);t.exports=function(t,e,i){void 0===e&&(e=0),void 0===i&&(i=t.length);var r=e+Math.floor(Math.random()*i);return n(t,r)}},6960:t=>{t.exports=function(t,e,i){var n=t.indexOf(e),r=t.indexOf(i);return-1!==n&&-1===r&&(t[n]=i,!0)}},1021:t=>{t.exports=function(t,e){void 0===e&&(e=1);for(var i=null,n=0;n<e;n++)i=t.shift(),t.push(i);return i}},4027:t=>{t.exports=function(t,e){void 0===e&&(e=1);for(var i=null,n=0;n<e;n++)i=t.pop(),t.unshift(i);return i}},2497:t=>{t.exports=function(t,e,i,n){var r=t.length;if(e<0||e>r||e>=i||i>r){if(n)throw new Error("Range Error: Values outside acceptable range");return!1}return!0}},5361:t=>{t.exports=function(t,e){var i=t.indexOf(e);return-1!==i&&i>0&&(t.splice(i,1),t.unshift(e)),e}},3718:(t,e,i)=>{var n=i(2497);t.exports=function(t,e,i,r,s){if(void 0===r&&(r=0),void 0===s&&(s=t.length),n(t,r,s))for(var a=r;a<s;a++){var o=t[a];o.hasOwnProperty(e)&&(o[e]=i)}return t}},4912:t=>{t.exports=function(t){for(var e=t.length-1;e>0;e--){var i=Math.floor(Math.random()*(e+1)),n=t[e];t[e]=t[i],t[i]=n}return t}},2071:t=>{t.exports=function(t){var e=/\D/g;return t.sort((function(t,i){return parseInt(t.replace(e,""),10)-parseInt(i.replace(e,""),10)})),t}},8935:t=>{t.exports=function(t,e){if(!(e>=t.length)){for(var i=t.length-1,n=t[e],r=e;r<i;r++)t[r]=t[r+1];return t.length=i,n}}},9992:(t,e,i)=>{var n=i(9356);function r(t,e){return String(t).localeCompare(e)}function s(t,e,i,n){var r,s,a,o,h,l=t.length,u=0,c=2*i;for(r=0;r<l;r+=c)for(a=(s=r+i)+i,s>l&&(s=l),a>l&&(a=l),o=r,h=s;;)if(o<s&&h<a)e(t[o],t[h])<=0?n[u++]=t[o++]:n[u++]=t[h++];else if(o<s)n[u++]=t[o++];else{if(!(h<a))break;n[u++]=t[h++]}}t.exports=function(t,e){if(void 0===e&&(e=r),!t||t.length<2)return t;if(n.features.stableSort)return t.sort(e);var i=function(t,e){var i=t.length;if(i<=1)return t;for(var n=new Array(i),r=1;r<i;r*=2){s(t,e,r,n);var a=t;t=n,n=a}return t}(t,e);return i!==t&&s(i,null,t.length,t),t}},2372:t=>{t.exports=function(t,e,i){if(e===i)return t;var n=t.indexOf(e),r=t.indexOf(i);if(n<0||r<0)throw new Error("Supplied items must be elements of the same array");return t[n]=i,t[r]=e,t}},1953:(t,e,i)=>{t.exports={Matrix:i(1237),Add:i(1792),AddAt:i(2280),BringToTop:i(2513),CountAllMatching:i(1771),Each:i(7883),EachInRange:i(5856),FindClosestInSorted:i(3957),Flatten:i(4493),GetAll:i(6245),GetFirst:i(1647),GetRandom:i(5301),MoveDown:i(1842),MoveTo:i(1419),MoveUp:i(6512),MoveAbove:i(8683),MoveBelow:i(546),NumberArray:i(4130),NumberArrayStep:i(1316),QuickSelect:i(9465),Range:i(9703),Remove:i(7161),RemoveAt:i(4725),RemoveBetween:i(8780),RemoveRandomElement:i(5744),Replace:i(6960),RotateLeft:i(1021),RotateRight:i(4027),SafeRange:i(2497),SendToBack:i(5361),SetAll:i(3718),Shuffle:i(4912),SortByDigits:i(2071),SpliceOne:i(8935),StableSort:i(9992),Swap:i(2372)}},1816:t=>{t.exports=function(t){if(!Array.isArray(t)||!Array.isArray(t[0]))return!1;for(var e=t[0].length,i=1;i<t.length;i++)if(t[i].length!==e)return!1;return!0}},6655:(t,e,i)=>{var n=i(7222),r=i(1816);t.exports=function(t){var e="";if(!r(t))return e;for(var i=0;i<t.length;i++){for(var s=0;s<t[i].length;s++){var a=t[i][s].toString();e+="undefined"!==a?n(a,2):"?",s<t[i].length-1&&(e+=" |")}if(i<t.length-1){e+="\n";for(var o=0;o<t[i].length;o++)e+="---",o<t[i].length-1&&(e+="+");e+="\n"}}return e}},582:t=>{t.exports=function(t){return t.reverse()}},6063:t=>{t.exports=function(t){for(var e=0;e<t.length;e++)t[e].reverse();return t}},8321:(t,e,i)=>{var n=i(7116);t.exports=function(t){return n(t,180)}},2597:(t,e,i)=>{var n=i(7116);t.exports=function(t,e){void 0===e&&(e=1);for(var i=0;i<e;i++)t=n(t,90);return t}},7116:(t,e,i)=>{var n=i(1816),r=i(4780);t.exports=function(t,e){if(void 0===e&&(e=90),!n(t))return null;if("string"!=typeof e&&(e=(e%360+360)%360),90===e||-270===e||"rotateLeft"===e)(t=r(t)).reverse();else if(-90===e||270===e||"rotateRight"===e)t.reverse(),t=r(t);else if(180===Math.abs(e)||"rotate180"===e){for(var i=0;i<t.length;i++)t[i].reverse();t.reverse()}return t}},6285:(t,e,i)=>{var n=i(7116);t.exports=function(t,e){void 0===e&&(e=1);for(var i=0;i<e;i++)t=n(t,-90);return t}},7711:(t,e,i)=>{var n=i(1021),r=i(4027);t.exports=function(t,e,i){if(void 0===e&&(e=0),void 0===i&&(i=0),0!==i&&(i<0?n(t,Math.abs(i)):r(t,i)),0!==e)for(var s=0;s<t.length;s++){var a=t[s];e<0?n(a,Math.abs(e)):r(a,e)}return t}},4780:t=>{t.exports=function(t){for(var e=t.length,i=t[0].length,n=new Array(i),r=0;r<i;r++){n[r]=new Array(e);for(var s=e-1;s>-1;s--)n[r][s]=t[s][r]}return n}},1237:(t,e,i)=>{t.exports={CheckMatrix:i(1816),MatrixToString:i(6655),ReverseColumns:i(582),ReverseRows:i(6063),Rotate180:i(8321),RotateLeft:i(2597),RotateMatrix:i(7116),RotateRight:i(6285),Translate:i(7711),TransposeMatrix:i(4780)}},3911:t=>{var e=function(t){var i,n,r;if("object"!=typeof t||null===t)return t;for(r in i=Array.isArray(t)?[]:{},t)n=t[r],i[r]=e(n);return i};t.exports=e},1030:(t,e,i)=>{var n=i(2482),r=function(){var t,e,i,s,a,o,h=arguments[0]||{},l=1,u=arguments.length,c=!1;for("boolean"==typeof h&&(c=h,h=arguments[1]||{},l=2),u===l&&(h=this,--l);l<u;l++)if(null!=(t=arguments[l]))for(e in t)i=h[e],h!==(s=t[e])&&(c&&s&&(n(s)||(a=Array.isArray(s)))?(a?(a=!1,o=i&&Array.isArray(i)?i:[]):o=i&&n(i)?i:{},h[e]=r(c,o,s)):void 0!==s&&(h[e]=s));return h};t.exports=r},8361:(t,e,i)=>{var n=i(4675),r=i(5851);t.exports=function(t,e,i){var s=r(t,e,null);if(null===s)return i;if(Array.isArray(s))return n.RND.pick(s);if("object"==typeof s){if(s.hasOwnProperty("randInt"))return n.RND.integerInRange(s.randInt[0],s.randInt[1]);if(s.hasOwnProperty("randFloat"))return n.RND.realInRange(s.randFloat[0],s.randFloat[1])}else if("function"==typeof s)return s(e);return s}},4597:t=>{t.exports=function(t,e,i){var n=typeof t;return t&&"number"!==n&&"string"!==n&&t.hasOwnProperty(e)&&void 0!==t[e]?t[e]:i}},5851:t=>{t.exports=function(t,e,i,n){if(!t&&!n||"number"==typeof t)return i;if(t&&t.hasOwnProperty(e))return t[e];if(n&&n.hasOwnProperty(e))return n[e];if(-1!==e.indexOf(".")){for(var r=e.split("."),s=t,a=n,o=i,h=i,l=!0,u=!0,c=0;c<r.length;c++)s&&s.hasOwnProperty(r[c])?(o=s[r[c]],s=s[r[c]]):l=!1,a&&a.hasOwnProperty(r[c])?(h=a[r[c]],a=a[r[c]]):u=!1;return l?o:u?h:i}return i}},2482:t=>{t.exports=function(t){if(!t||"object"!=typeof t||t.nodeType||t===t.window)return!1;try{if(t.constructor&&!{}.hasOwnProperty.call(t.constructor.prototype,"isPrototypeOf"))return!1}catch(t){return!1}return!0}},7222:t=>{t.exports=function(t,e,i,n){void 0===e&&(e=0),void 0===i&&(i=" "),void 0===n&&(n=3);var r=0;if(e+1>=(t=t.toString()).length)switch(n){case 1:t=new Array(e+1-t.length).join(i)+t;break;case 3:var s=Math.ceil((r=e-t.length)/2);t=new Array(r-s+1).join(i)+t+new Array(s+1).join(i);break;default:t+=new Array(e+1-t.length).join(i)}return t}}},e={};var i=function i(n){var r=e[n];if(void 0!==r)return r.exports;var s=e[n]={exports:{}};return t[n](s,s.exports,i),s.exports}(4513);window.SpinePlugin=i})();