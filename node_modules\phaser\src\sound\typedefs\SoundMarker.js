/**
 * Marked section of a sound represented by name, and optionally start time, duration, and config object.
 *
 * @typedef {object} Phaser.Types.Sound.SoundMarker
 * @since 3.0.0
 *
 * @property {string} name - Unique identifier of a sound marker.
 * @property {number} [start=0] - Sound position offset at witch playback should start.
 * @property {number} [duration] - Playback duration of this marker.
 * @property {Phaser.Types.Sound.SoundConfig} [config] - An optional config object containing default marker settings.
 */
