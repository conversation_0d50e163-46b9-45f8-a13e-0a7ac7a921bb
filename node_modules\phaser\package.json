{"name": "phaser", "version": "3.90.0", "release": "<PERSON><PERSON><PERSON><PERSON>", "description": "A fast, free and fun HTML5 Game Framework for Desktop and Mobile web browsers from the team at Phaser Studio Inc.", "author": "<PERSON> <<EMAIL>> (https://www.phaser.io)", "homepage": "https://phaser.io", "bugs": "https://github.com/phaserjs/phaser/issues", "license": "MIT", "licenseUrl": "https://www.opensource.org/licenses/mit-license.php", "main": "./src/phaser.js", "types": "./types/phaser.d.ts", "browser": "./dist/phaser.js", "module": "./dist/phaser.esm.js", "repository": {"type": "git", "url": "https://<EMAIL>/phaserjs/phaser.git"}, "scripts": {"beta": "npm publish --tag beta", "help": "node scripts/help.js", "build": "webpack --config config/webpack.config.js", "watch": "webpack --watch --config config/webpack.config.js", "watchns": "webpack --watch --config config/webpack-nospector.config.js", "buildfb": "webpack --config config/webpack.fb.config.js", "watchfb": "webpack --config config/webpack.fb.config.js --watch", "dist": "webpack --config config/webpack.dist.config.js", "distfb": "webpack --config config/webpack.fb.dist.config.js", "distfull": "npm run dist && npm run distfb", "plugin.cam3d": "webpack --config plugins/camera3d/webpack.config.js", "plugin.spine": "webpack --config plugins/spine/webpack.config.js", "plugin.spine.dist": "webpack --config plugins/spine/webpack.auto.dist.config.js", "plugin.spine.watch": "webpack --config plugins/spine/webpack.auto.config.js --watch", "plugin.spine.dev": "webpack --config plugins/spine/webpack.auto.config.js", "plugin.spine.runtimes": "cd plugins/spine && tsc -p tsconfig.both.json && tsc -p tsconfig.canvas.json && tsc -p tsconfig.webgl.json", "plugin.spine.canvas.dist": "webpack --config plugins/spine/webpack.canvas.dist.config.js", "plugin.spine.canvas.watch": "webpack --config plugins/spine/webpack.canvas.config.js --watch", "plugin.spine.canvas.dev": "webpack --config plugins/spine/webpack.canvas.config.js", "plugin.spine.webgl.dist": "webpack --config plugins/spine/webpack.webgl.dist.config.js", "plugin.spine.webgl.watch": "webpack --config plugins/spine/webpack.webgl.config.js --watch", "plugin.spine.webgl.dev": "webpack --config plugins/spine/webpack.webgl.config.js", "plugin.spine.full": "npm run plugin.spine.dev && npm run plugin.spine.canvas.dev && npm run plugin.spine.webgl.dev", "plugin.spine.full.dist": "npm run plugin.spine.dist && npm run plugin.spine.canvas.dist && npm run plugin.spine.webgl.dist", "plugin.spine4.1.dist": "webpack --config plugins/spine4.1/webpack.auto.dist.config.js", "plugin.spine4.1.watch": "webpack --config plugins/spine4.1/webpack.auto.config.js --watch", "plugin.spine4.1.dev": "webpack --config plugins/spine4.1/webpack.auto.config.js", "plugin.spine4.1.runtimes": "cd plugins/spine4.1/spine-runtimes/spine-ts && npm run build:canvas && npm run build:webgl && cp spine-canvas/dist/iife/* ../../src/runtimes && cp spine-webgl/dist/iife/* ../../src/runtimes", "plugin.spine4.1.canvas.dist": "webpack --config plugins/spine4.1/webpack.canvas.dist.config.js", "plugin.spine4.1.canvas.watch": "webpack --config plugins/spine4.1/webpack.canvas.config.js --watch", "plugin.spine4.1.canvas.dev": "webpack --config plugins/spine4.1/webpack.canvas.config.js", "plugin.spine4.1.webgl.dist": "webpack --config plugins/spine4.1/webpack.webgl.dist.config.js", "plugin.spine4.1.webgl.watch": "webpack --config plugins/spine4.1/webpack.webgl.config.js --watch", "plugin.spine4.1.webgl.dev": "webpack --config plugins/spine4.1/webpack.webgl.config.js", "plugin.spine4.1.full": "npm run plugin.spine4.1.dev && npm run plugin.spine4.1.canvas.dev && npm run plugin.spine4.1.webgl.dev", "plugin.spine4.1.full.dist": "npm run plugin.spine4.1.dist && npm run plugin.spine4.1.canvas.dist && npm run plugin.spine4.1.webgl.dist", "lint": "eslint --config .eslintrc.json \"src/**/*.js\"", "lintfix": "eslint --config .eslintrc.json \"src/**/*.js\" --fix", "sloc": "node-sloc \"./src\" --include-extensions \"js\"", "bundleshaders": "node scripts/bundle-shaders.js", "build-tsgen": "cd scripts/tsgen && tsc", "tsgen": "cd scripts/tsgen && jsdoc -c jsdoc-tsd.conf.json", "test-ts": "cd scripts/tsgen/test && tsc --build tsconfig.json > output.txt", "ts": "npm run tsgen && npm run test-ts", "tsdev": "npm run build-tsgen && npm run tsgen && npm run test-ts"}, "keywords": ["2d", "HTML5", "WebGL", "canvas", "game", "javascript", "physics", "tweens", "typescript", "web audio"], "devDependencies": {"@types/offscreencanvas": "^2019.7.3", "@types/source-map": "^0.5.7", "clean-webpack-plugin": "^4.0.0", "dts-dom": "^3.7.0", "eslint": "^8.56.0", "eslint-plugin-es5": "^1.5.0", "exports-loader": "^5.0.0", "fs-extra": "^11.2.0", "imports-loader": "^5.0.0", "jsdoc": "3.x.x", "node-sloc": "^0.2.1", "path": "^0.12.7", "phaser3spectorjs": "^0.0.8", "remove-files-webpack-plugin": "^1.5.0", "source-map": "^0.7.4", "terser-webpack-plugin": "^5.3.10", "typescript": "^5.3.3", "vivid-cli": "^1.1.2", "webpack": "^5.90.3", "webpack-cli": "^5.1.4", "webpack-shell-plugin": "^0.5.0"}, "dependencies": {"eventemitter3": "^5.0.1"}}